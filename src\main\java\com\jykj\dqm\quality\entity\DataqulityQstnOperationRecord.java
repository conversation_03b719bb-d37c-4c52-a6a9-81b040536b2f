package com.jykj.dqm.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 质量问题操作记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 14:12:37
 */
@ApiModel(value="质量问题操作记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DATAQULITY_QSTN_OPERATION_RECORD")
public class DataqulityQstnOperationRecord implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "PK_ID", type = IdType.AUTO)
    @ApiModelProperty(value="主键ID")
    private Integer pkId;

    /**
     * 问题ID
     */
    @TableField(value = "DATA_QLTY_QSTN_ID")
    @ApiModelProperty(value="问题ID")
    private String dataQltyQstnId;

    /**
     * 问题整改状态
     */
    @TableField(value = "QSTN_RCTFCTN_STUS_CD")
    @ApiModelProperty(value="问题整改状态")
    private String qstnRctfctnStusCd;

    /**
     * 操作内容
     */
    @TableField(value = "OPERATION_CONTENT")
    @ApiModelProperty(value="操作内容")
    private String operationContent;

    /**
     * 操作时间
     */
    @TableField(value = "OPERATION_TIME")
    @ApiModelProperty(value="操作时间")
    private Date operationTime;

    /**
     * 操作人员
     */
    @TableField(value = "OPERATION_PERSON")
    @ApiModelProperty(value="操作人员")
    private String operationPerson;

    private static final long serialVersionUID = 1L;
}