package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.entity.EmrTestSqlExecResultRecord;
import com.jykj.dqm.emr.entity.SqlStatementQuery;
import com.jykj.dqm.emr.entity.SqlStatementQueryOneRule;
import com.jykj.dqm.emr.manager.DbQueryNewUtil;
import com.jykj.dqm.emr.service.DocumentRuleConfigurationService;
import com.jykj.dqm.emr.service.EmrTestSqlExecResultRecordService;
import com.jykj.dqm.emr.service.SqlStatementTestService;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.quality.manager.rulesql.BaseCheckSqlGenerator;
import com.jykj.dqm.quality.manager.rulesql.SqlUtils;
import com.jykj.dqm.utils.SymmetricCryptoFactory;
import com.jykj.dqm.utils.RedisUtil;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * SQL语句测试
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:27:39
 */
@Service
public class SqlStatementTestServiceImpl implements SqlStatementTestService {
    @Autowired
    private DbQueryNewUtil dbQueryUtil;

    @Autowired
    private DocumentRuleConfigurationService documentRuleConfigurationService;

    @Autowired
    private EmrTestSqlExecResultRecordService emrTestSqlExecResultRecordService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Resource(name = "threadPoolMonitor")
    private ThreadPoolExecutor threadPoolMonitor;

    @Override
    public R execute(SqlStatementQuery sqlStatementQuery) {
        // 根据dataSourceId查询数据源信息
        MetadataDatasource metadataDatasource = RedisUtil
                .getMetadataDatasourceById(sqlStatementQuery.getDataSourceId());
        String sql = sqlStatementQuery.getSql();
        if (sql.contains("#startDate") && sql.contains("#endDate")) {
            // 获取当前月的起始和结束日期(1小时)
            Date date = new Date();
            int hour = sqlStatementQuery.getOffsetHour() == null ? 1 : sqlStatementQuery.getOffsetHour();
            String startDate = DateUtil.formatDateTime(DateUtil.offsetHour(date, -hour));
            String endDate = DateUtil.formatDateTime(date);
            sql = addTimeToSql(sql, metadataDatasource.getDatabaseType(), startDate, endDate);
        }
        // 去除SQL中的注释 # -- /**/
        String normalSql = SqlUtils.removeComments(sql);
        metadataDatasource.setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasource.getDatabasePwd()));
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> result = dbQueryUtil.queryLimit(metadataDatasource, normalSql, 10);
        resultMap.put("result", result);
        if (result.size() >= Constant.DEFAULT_NUMBER_OF_RETURNED_DATA) {
            resultMap.put("msg", "注意：当前查询只返回前" + Constant.DEFAULT_NUMBER_OF_RETURNED_DATA + "条数据！");
            // 去掉最后一条数据
            result.remove(result.size() - 1);
        }
        return RUtil.success(resultMap);
    }

    @Override
    public R executeOneRule(SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        // 找到对应的规则配置
        LambdaQueryWrapper<DocumentRuleConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(DocumentRuleConfiguration::getDirectoryCode, sqlStatementQueryOneRule.getDirectoryCode())
                .eq(DocumentRuleConfiguration::getDirectoryName, sqlStatementQueryOneRule.getDirectoryName())
                .eq(DocumentRuleConfiguration::getEmrRuleType, sqlStatementQueryOneRule.getEmrRuleType())
                .eq(DocumentRuleConfiguration::getProjectId, sqlStatementQueryOneRule.getProjectId());
        List<DocumentRuleConfiguration> list = documentRuleConfigurationService.list(lambdaQueryWrapper);
        MetadataDatasource metadataDatasource;
        Pair<String, Long> result;
        for (DocumentRuleConfiguration documentRuleConfiguration : list) {
            // 根据dataSourceId查询数据源信息
            if ("1".equals(documentRuleConfiguration.getWhetherCrossDbQuery())) {
                metadataDatasource = RedisUtil
                        .getMetadataDatasourceById(documentRuleConfiguration.getCrossDbQueryDataSourceId());
            } else {
                metadataDatasource = RedisUtil.getMetadataDatasourceById(documentRuleConfiguration.getDataSourceId());
            }
            if (metadataDatasource == null) {
                continue;
            }
            metadataDatasource.setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasource.getDatabasePwd()));
            // 满足条件记录数
            result = execSql(sqlStatementQueryOneRule.getDataStartTime(), sqlStatementQueryOneRule.getDataEndTime(),
                    documentRuleConfiguration.getConditionalRecordsSql(), metadataDatasource,
                    documentRuleConfiguration);
            documentRuleConfiguration.setConditionalRecordsNum(result.getValue());
            documentRuleConfiguration.setConditionalRecordsSql(result.getKey());
            // 总记录数
            result = execSql(sqlStatementQueryOneRule.getDataStartTime(), sqlStatementQueryOneRule.getDataEndTime(),
                    documentRuleConfiguration.getRecordsSql(), metadataDatasource, documentRuleConfiguration);
            documentRuleConfiguration.setRecordsNum(result.getValue());
            documentRuleConfiguration.setRecordsSql(result.getKey());
        }
        return RUtil.success(list);
    }

    @Override
    @Async("doDQMExecutor")
    @SneakyThrows
    public void asyncExecuteOneRule(SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        String unique = sqlStatementQueryOneRule.getProjectId() + sqlStatementQueryOneRule.getDirectoryCode()
                + sqlStatementQueryOneRule.getDirectoryName() + sqlStatementQueryOneRule.getEmrRuleType();
        redisTemplate.opsForValue().set("executeOneRule" + unique, "0", 60, TimeUnit.MINUTES);
        // 找到对应的规则配置
        LambdaQueryWrapper<DocumentRuleConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(DocumentRuleConfiguration::getDirectoryCode, sqlStatementQueryOneRule.getDirectoryCode())
                .eq(DocumentRuleConfiguration::getDirectoryName, sqlStatementQueryOneRule.getDirectoryName())
                .eq(DocumentRuleConfiguration::getEmrRuleType, sqlStatementQueryOneRule.getEmrRuleType())
                .eq(DocumentRuleConfiguration::getProjectId, sqlStatementQueryOneRule.getProjectId());
        List<DocumentRuleConfiguration> list = documentRuleConfigurationService.list(lambdaQueryWrapper);
        if (CollUtil.isEmpty(list)) {
            redisTemplate.opsForValue().set("executeOneRule" + unique, "-1", 60, TimeUnit.MINUTES);
            return;
        }
        // 清空之前的记录
        emrTestSqlExecResultRecordService.remove(Wrappers.<EmrTestSqlExecResultRecord>lambdaQuery()
                .eq(EmrTestSqlExecResultRecord::getDirectoryCode, sqlStatementQueryOneRule.getDirectoryCode())
                .eq(EmrTestSqlExecResultRecord::getDirectoryName, sqlStatementQueryOneRule.getDirectoryName())
                .eq(EmrTestSqlExecResultRecord::getEmrRuleType, sqlStatementQueryOneRule.getEmrRuleType())
                .eq(EmrTestSqlExecResultRecord::getConfigType, sqlStatementQueryOneRule.getConfigType()));

        MetadataDatasource metadataDatasource;
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (DocumentRuleConfiguration documentRuleConfiguration : list) {
            // 根据dataSourceId查询数据源信息
            if ("1".equals(documentRuleConfiguration.getWhetherCrossDbQuery())) {
                metadataDatasource = RedisUtil
                        .getMetadataDatasourceById(documentRuleConfiguration.getCrossDbQueryDataSourceId());
            } else {
                metadataDatasource = RedisUtil.getMetadataDatasourceById(documentRuleConfiguration.getDataSourceId());
            }
            if (metadataDatasource == null) {
                continue;
            }
            MetadataDatasource metadataDatasourceTemp = metadataDatasource;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                execSqlAndRecord(sqlStatementQueryOneRule, documentRuleConfiguration, metadataDatasourceTemp);
            }, threadPoolMonitor);
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        // 标记完成
        redisTemplate.opsForValue().set("executeOneRule" + unique, "1", 60, TimeUnit.MINUTES);
    }

    private void execSqlAndRecord(SqlStatementQueryOneRule sqlStatementQueryOneRule,
            DocumentRuleConfiguration documentRuleConfiguration, MetadataDatasource metadataDatasourceTemp) {
        EmrTestSqlExecResultRecord emrTestSqlExecResultRecord = new EmrTestSqlExecResultRecord();
        emrTestSqlExecResultRecord.setProjectId(sqlStatementQueryOneRule.getProjectId());
        emrTestSqlExecResultRecord.setExportRecordId("1");
        emrTestSqlExecResultRecord.setDirectoryCode(documentRuleConfiguration.getDirectoryCode());
        emrTestSqlExecResultRecord.setDirectoryName(documentRuleConfiguration.getDirectoryName());
        emrTestSqlExecResultRecord.setEmrRuleType(documentRuleConfiguration.getEmrRuleType());
        emrTestSqlExecResultRecord.setExecTime(LocalDateTime.now());
        emrTestSqlExecResultRecord.setConfigType(sqlStatementQueryOneRule.getConfigType());
        emrTestSqlExecResultRecord.setRequiredProject(documentRuleConfiguration.getRequiredProject());
        emrTestSqlExecResultRecord.setExecStatus("2");
        emrTestSqlExecResultRecordService.save(emrTestSqlExecResultRecord);
        metadataDatasourceTemp.setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasourceTemp.getDatabasePwd()));
        // 满足条件记录数
        try {
            Pair<String, Long> result = execSql(sqlStatementQueryOneRule.getDataStartTime(),
                    sqlStatementQueryOneRule.getDataEndTime(), documentRuleConfiguration.getConditionalRecordsSql(),
                    metadataDatasourceTemp, documentRuleConfiguration);
            Long value = result.getValue();
            documentRuleConfiguration.setConditionalRecordsNum(value);
            documentRuleConfiguration.setConditionalRecordsSql(result.getKey());
            emrTestSqlExecResultRecord.setConditionalRecordsNum(value.intValue());
            // 总记录数
            result = execSql(sqlStatementQueryOneRule.getDataStartTime(), sqlStatementQueryOneRule.getDataEndTime(),
                    documentRuleConfiguration.getRecordsSql(), metadataDatasourceTemp, documentRuleConfiguration);
            value = result.getValue();
            documentRuleConfiguration.setRecordsNum(value);
            documentRuleConfiguration.setRecordsSql(result.getKey());
            emrTestSqlExecResultRecord.setRecordsNum(value.intValue());
        } catch (Exception e) {
            emrTestSqlExecResultRecord.setExecStatus("1");
            emrTestSqlExecResultRecord.setFailureReason(e.getMessage());
            e.printStackTrace();
        } finally {
            emrTestSqlExecResultRecord.setExecStatus("0");
            emrTestSqlExecResultRecordService.updateById(emrTestSqlExecResultRecord);
        }
    }

    @Override
    public R getSqlExecStatus(SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        String num = redisTemplate.opsForValue()
                .get("executeOneRule" + sqlStatementQueryOneRule.getProjectId()
                        + sqlStatementQueryOneRule.getDirectoryCode() + sqlStatementQueryOneRule.getDirectoryName()
                        + sqlStatementQueryOneRule.getEmrRuleType());
        return RUtil.success(num);
    }

    @Override
    public R getSqlExecResult(SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        LambdaQueryWrapper<DocumentRuleConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper
                .eq(DocumentRuleConfiguration::getDirectoryCode, sqlStatementQueryOneRule.getDirectoryCode())
                .eq(DocumentRuleConfiguration::getDirectoryName, sqlStatementQueryOneRule.getDirectoryName())
                .eq(DocumentRuleConfiguration::getEmrRuleType, sqlStatementQueryOneRule.getEmrRuleType())
                .eq(DocumentRuleConfiguration::getProjectId, sqlStatementQueryOneRule.getProjectId());
        List<DocumentRuleConfiguration> documentRuleConfigurations = documentRuleConfigurationService
                .list(lambdaQueryWrapper);
        if (CollUtil.isEmpty(documentRuleConfigurations)) {
            return RUtil.success(new ArrayList<>());
        }
        List<EmrTestSqlExecResultRecord> list = emrTestSqlExecResultRecordService.lambdaQuery()
                .eq(EmrTestSqlExecResultRecord::getDirectoryCode, sqlStatementQueryOneRule.getDirectoryCode())
                .eq(EmrTestSqlExecResultRecord::getDirectoryName, sqlStatementQueryOneRule.getDirectoryName())
                .eq(EmrTestSqlExecResultRecord::getEmrRuleType, sqlStatementQueryOneRule.getEmrRuleType())
                .eq(EmrTestSqlExecResultRecord::getConfigType, sqlStatementQueryOneRule.getConfigType())
                .eq(EmrTestSqlExecResultRecord::getProjectId, sqlStatementQueryOneRule.getProjectId())
                .list();
        Map<String, EmrTestSqlExecResultRecord> recordMap = list.stream()
                .collect(
                        Collectors.toMap(
                                item -> item.getDirectoryCode() + item.getDirectoryName() + item.getEmrRuleType()
                                        + item.getConfigType() + item.getRequiredProject(),
                                item -> item, (k1, k2) -> k1));
        EmrTestSqlExecResultRecord emrTestSqlExecResultRecord;
        for (DocumentRuleConfiguration documentRuleConfiguration : documentRuleConfigurations) {
            emrTestSqlExecResultRecord = recordMap.get(documentRuleConfiguration.getDirectoryCode()
                    + documentRuleConfiguration.getDirectoryName() + documentRuleConfiguration.getEmrRuleType()
                    + sqlStatementQueryOneRule.getConfigType() + documentRuleConfiguration.getRequiredProject());
            if (emrTestSqlExecResultRecord == null) {
                continue;
            }
            documentRuleConfiguration
                    .setConditionalRecordsNum(emrTestSqlExecResultRecord.getConditionalRecordsNum().longValue());
            documentRuleConfiguration.setRecordsNum(emrTestSqlExecResultRecord.getRecordsNum().longValue());
        }
        return RUtil.success(documentRuleConfigurations);
    }

    private Pair<String, Long> execSql(String startDate, String endDate, String sql,
            MetadataDatasource metadataDatasource, DocumentRuleConfiguration documentRuleConfiguration) {
        Pair<String, Long> result;
        if (StrUtil.isBlank(sql)) {
            result = new Pair<>(sql, 0L);
            return result;
        }
        if (sql.contains("#startDate") && sql.contains("#endDate")) {
            sql = addTimeToSql(sql, metadataDatasource.getDatabaseType(), startDate, endDate + " 23:59:59");
        }
        Long count = dbQueryUtil.queryCount(metadataDatasource, sql);
        result = new Pair<>(sql, count);
        return result;
    }

    String addTimeToSql(String sql, String dbType, String startDate, String endDate) {
        BaseCheckSqlGenerator baseCheckSqlGenerator = new BaseCheckSqlGenerator();
        String startTimeSql = baseCheckSqlGenerator.getSqlDateFunction(dbType, startDate);
        String endTimeSql = baseCheckSqlGenerator.getSqlDateFunction(dbType, endDate);
        return sql.replace("#startDate", startTimeSql).replace("#endDate", endTimeSql);
    }
}
