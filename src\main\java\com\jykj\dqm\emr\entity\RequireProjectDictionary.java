package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 要求项目字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/20 16:23:09
 */
@ApiModel(description="要求项目字典")
@Data
@EqualsAndHashCode(callSuper=true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_REQUIRE_PROJECT_DICTIONARY")
public class RequireProjectDictionary extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="ID")
    private String id;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value="目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value="目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value="规则类型")
    private String emrRuleType;

    /**
     * 要求项目名称，多个用逗号隔开
     */
    @TableField(value = "REQUIRE_PROJECT_NAME")
    @ApiModelProperty(value="要求项目名称，多个用中文顿号'、'隔开")
    private String requireProjectName;

    /**
     * 医院项目名称，多个用中文顿号'、'隔开
     */
    @TableField(value = "HOSPITAL_PROJECT_NAME")
    @ApiModelProperty(value="医院项目名称，多个用中文顿号'、'隔开")
    private String hospitalProjectName;

    /**
     * 表头名称1（及时性+整合性）
     */
    @TableField(value = "HEADER_NAME1")
    @ApiModelProperty(value = "表头名称1（及时性+整合性）")
    private String headerName1;

    /**
     * 表头名称2（整合性）
     */
    @TableField(value = "HEADER_NAME2")
    @ApiModelProperty(value = "表头名称2（整合性）")
    private String headerName2;

    private static final long serialVersionUID = 1L;
}