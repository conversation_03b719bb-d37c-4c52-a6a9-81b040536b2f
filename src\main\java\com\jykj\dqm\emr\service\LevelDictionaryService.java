package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.LevelDictionary;
import com.jykj.dqm.emr.entity.LevelDictionaryQuery;

import java.util.List;

/**
 * 等级字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 9:51:53
 */
public interface LevelDictionaryService extends IService<LevelDictionary> {
    R add(LevelDictionary requireProjectDictionary);

    R update(LevelDictionary levelDictionary);

    R delete(List<Long> ids);

    R query(LevelDictionaryQuery levelDictionaryQuery);

    R queryAllow(LevelDictionaryQuery levelDictionaryQuery);
}
