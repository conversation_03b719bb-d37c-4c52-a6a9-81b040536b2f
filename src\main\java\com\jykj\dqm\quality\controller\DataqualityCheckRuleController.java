package com.jykj.dqm.quality.controller;

import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.config.group.CustomValidateGroup;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleQueryDTO;
import com.jykj.dqm.quality.entity.RuleFirstPageQueryDTO;
import com.jykj.dqm.quality.service.DataqualityCheckRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 检核规则
 *
 * <AUTHOR>
 */
@Api(tags = {"规则配置"})
@RestController
@RequestMapping("/dataquality")
public class DataqualityCheckRuleController {
    @Autowired
    private DataqualityCheckRuleService dataqualityCheckRuleService;


    @ApiOperation(value = "规则配置列表（首页）", notes = "规则配置")
    @PostMapping(value = "/firstPageList")
    public R getList(@RequestBody RuleFirstPageQueryDTO ruleFirstPageQueryDTO) {
        return dataqualityCheckRuleService.firstPageList(ruleFirstPageQueryDTO);
    }

    @ApiOperation(value = "规则配置列表（配置界面）", notes = "规则配置")
    @PostMapping(value = "/ruleList")
    public R ruleList(@Validated @RequestBody DataqualityCheckRuleQueryDTO dataqualityCheckRuleQueryDTO) {
        return dataqualityCheckRuleService.ruleList(dataqualityCheckRuleQueryDTO);
    }

    @ApiOperation(value = "新增规则配置", notes = "规则配置")
    @LogRemark(operate = "新增规则配置", module = "规则配置")
    @PostMapping(value = "/addRule")
    public R addRule(@Validated @RequestBody DataqualityCheckRule dataqualityCheckRule) {
        return dataqualityCheckRuleService.addRule(dataqualityCheckRule);
    }

    @ApiOperation(value = "更新规则配置", notes = "规则配置")
    @LogRemark(operate = "更新规则配置", module = "规则配置")
    @PostMapping(value = "/updateRule")
    public R updateRule(@Validated(value = CustomValidateGroup.Crud.Update.class) @RequestBody DataqualityCheckRule dataqualityCheckRule) {
        return dataqualityCheckRuleService.updateRule(dataqualityCheckRule);
    }

    @ApiOperation(value = "修改规则状态", notes = "规则配置")
    @LogRemark(operate = "修改规则状态", module = "规则配置")
    @PostMapping(value = "/updateRuleStatus")
    // 设置动态参数描述注解
    @DynamicParameters(name = "params",
            properties = {
                    @DynamicParameter(name = "checkRuleId", value = "规则ID", example = "1", required = true, dataTypeClass = Integer.class),
                    @DynamicParameter(name = "checkRuleStatus", value = "检核规则状态 0：启用 1：未启用", example = "0", required = true, dataTypeClass = String.class)
            })
    public R updateRuleStatus(@RequestBody Map<String,Object> params) {
        return dataqualityCheckRuleService.updateRuleStatus(params);
    }

    @ApiOperation(value = "删除规则配置", notes = "规则配置")
    @LogRemark(operate = "删除规则配置", module = "规则配置")
    @DeleteMapping(value = "/delRule")
    public R delRule(@RequestParam("checkRuleId") String checkRuleId) {
        return dataqualityCheckRuleService.delRule(checkRuleId);
    }

    @ApiOperation(value = "获取当前系统和数据库的表（dataSourceId不能为空）", notes = "规则配置")
    @GetMapping(value = "/getTable")
    public R getTable(@RequestParam("dataSourceId") String dataSourceId, @RequestParam("tableName") String tableName) {
        return dataqualityCheckRuleService.getTable(dataSourceId, tableName);
    }

    @ApiOperation(value = "获取表对应的字段(dataSourceId和tableName不能为空)", notes = "规则配置")
    @GetMapping(value = "/getTableField")
    public R getTableField(@RequestParam("dataSourceId") String dataSourceId,
                           @RequestParam("tableName") String tableName,
                           @RequestParam("fieldName") String fieldName,
                           @RequestParam("pageSize") Integer pageSize,
                           @RequestParam("pageNum") Integer pageNum) {
        return dataqualityCheckRuleService.getTableField(dataSourceId, tableName, fieldName, pageSize, pageNum);
    }

    @ApiOperation(value = "获取字典中的表名", notes = "规则配置")
    @GetMapping(value = "/getDictTable")
    public R getDictTable(@RequestParam("tableName") String tableName,
                          @RequestParam("pageSize") Integer pageSize,
                          @RequestParam("pageNum") Integer pageNum) {
        return dataqualityCheckRuleService.getDictTable(tableName, pageSize, pageNum);
    }

    @ApiOperation(value = "获取字典表字段", notes = "规则配置")
    @GetMapping(value = "/getDictTableFiled")
    public R getDictTableFiled(@RequestParam("fieldName") String fieldName,
                               @RequestParam("pageSize") Integer pageSize,
                               @RequestParam("pageNum") Integer pageNum) {
        return dataqualityCheckRuleService.getDictTableFiled(fieldName, pageSize, pageNum);
    }

    @ApiOperation(value = "根据目前的配置获取检核规则SQL", notes = "规则配置")
    @PostMapping(value = "/getCheckSql")
    public R getCheckSql(@Validated @RequestBody DataqualityCheckRule dataqualityCheckRule) {
        //如果单引号被编码，使用下面的解码
        //dataqualityCheckRule.setCheckRuleWhere(HtmlUtils.htmlUnescape(dataqualityCheckRule.getCheckRuleWhere()));
        return dataqualityCheckRuleService.getCheckSql(dataqualityCheckRule);
    }

    @ApiOperation(value = "获取复制的序列号", notes = "规则配置")
    @GetMapping(value = "/getCopiedSerialNumber")
    public R getDictTableFiled(@RequestParam("checkRuleName") String checkRuleName) {
        return dataqualityCheckRuleService.getCopiedSerialNumber(checkRuleName);
    }
}
