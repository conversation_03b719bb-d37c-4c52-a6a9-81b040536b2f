package com.jykj.dqm.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.system.entity.SysCodetabContent;
import com.jykj.dqm.system.entity.SysCodetabContentDTO;

import java.util.List;

/**
 * 系统码值内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:53:39
 */
public interface SysCodetabContentService extends IService<SysCodetabContent> {

    R addSysCodetabContent(SysCodetabContent sysCodetabContent);

    R modifySysCodetabContent(SysCodetabContent sysCodetabContent);

    R deleteSysCodetabContent(List<Integer> ids);

    R querySysCodetabContent(SysCodetabContentDTO sysCodetabContentDTO);
}
