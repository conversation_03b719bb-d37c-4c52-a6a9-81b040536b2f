package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.emr.entity.UserAndTimeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 实证材料流程和资料
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH
 */
@TableName(value = "DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH")
@Data
public class EmpiricalMaterialFlowPath extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private String id;

    @TableField(value = "PROJECT_ID")
    @ApiModelProperty(value = "项目ID")
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 目录名称
     */
    @NotBlank
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @NotBlank
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 评价内容ID
     */
    @ApiModelProperty(value = "评价内容ID")
    private String evaluationContentId;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 流程
     */
    @ApiModelProperty(value = "流程")
    private String flowPath;

    /**
     * 流程资料
     */
    @ApiModelProperty(value = "流程资料")
    private String flowPathData;

    /**
     * 删除图片名称
     */
    @ApiModelProperty(value = "删除图片名称")
    @TableField(exist = false)
    private List<String> deleteImageNames;

    @TableField(exist = false)
    private List<EMImagesInfo> imagesBase64List;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarksDesc;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private String parentId;

    /**
     * 流程类型：0-总说明；1-路径组；0-具体路径
     */
    @ApiModelProperty(value = "流程类型：0-总说明；1-路径组；2-具体路径")
    private String flowPathType;
}
