package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 测试SQL执行结果（只保留一次数据）
 *
 * @TableName DQM_EMR_TEST_SQL_EXEC_RESULT_RECORD
 */
@TableName(value = "DQM_EMR_TEST_SQL_EXEC_RESULT_RECORD")
@Data
public class EmrTestSqlExecResultRecord implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * EXPORT_RECORD的ID
     */
    @ApiModelProperty(value = "EXPORT_RECORD的ID")
    private String exportRecordId;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private String emrRuleType;

    /**
     * 要求项目
     */
    @ApiModelProperty(value = "要求项目")
    private String requiredProject;

    /**
     * 记录数
     */
    @ApiModelProperty(value = "记录数")
    private Integer recordsNum;

    /**
     * 满足条件记录数（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）
     */
    @ApiModelProperty(value = "满足条件记录数（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）")
    private Integer conditionalRecordsNum;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failureReason;

    /**
     * 执行状态，0成功，1失败
     */
    @ApiModelProperty(value = "执行状态，0成功，1失败，2执行中")
    private String execStatus;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime execTime;

    /**
     * 配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据
     */
    @ApiModelProperty(value = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据")
    private String configType;

}
