package com.jykj.dqm.system.dao;

import com.jykj.dqm.system.entity.SysConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 系统配置Mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 10:53
 */
@Mapper
public interface SysConfigMapper {
    int deleteByPrimaryKey(int id);

    /**
     * 新增系统配置（不带if-test）
     *
     * @param record SysConfig
     * @return 变更条数
     * <AUTHOR>
     */
    int insert(SysConfig record);

    /**
     * 新增系统配置（带有if-test）
     *
     * @param record SysConfig
     * @return 变更条数
     * <AUTHOR>
     */
    int insertSelective(SysConfig record);

    SysConfig selectByPrimaryKey(int id);

    /**
     * 更新系统配置（带有if-test）
     *
     * @param record SysConfig
     * @return 变更条数
     * <AUTHOR>
     */
    int updateByPrimaryKeySelective(SysConfig record);

    /**
     * 更新系统配置（不带if-test）
     *
     * @param record SysConfig
     * @return 变更条数
     * <AUTHOR>
     */
    int updateByPrimaryKey(SysConfig record);

    /**
     * 获取所有系统设置（webShow为Y的）
     *
     * @return List<SysConfig>
     * <AUTHOR>
     */
    List<SysConfig> querySysConfig();

    SysConfig queryByConfigCode(String configCode);

    /**
     * 查询所有系统设置
     *
     * @return List<SysConfig>
     * <AUTHOR>
     */
    List<SysConfig> querySysConfigAll();
}