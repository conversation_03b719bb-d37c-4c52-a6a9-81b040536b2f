package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.EmrProjectManager;
import com.jykj.dqm.emr.entity.EmrProjectManagerQuery;
import com.jykj.dqm.emr.service.EmrProjectManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = {"项目管理"})
@RestController
@RequestMapping("/emr/project")
public class EmrProjectManagerController {
    @Autowired
    private EmrProjectManagerService emrProjectManagerService;

    /**
     * 新增项目
     *
     * @param projectManager EmrProjectManager
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "新增项目", notes = "")
    @LogRemark(operate = "新增项目", module = "项目管理")
    @PostMapping("/addProject")
    public R addProject(@Validated @RequestBody EmrProjectManager projectManager) {
        return emrProjectManagerService.addProject(projectManager);
    }

    /**
     * 更新项目
     *
     * @param projectManager EmrProjectManager
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "更新项目", notes = "")
    @LogRemark(operate = "更新项目", module = "项目管理")
    @PostMapping("/updateProject")
    public R updateProject(@Validated @RequestBody EmrProjectManager projectManager) {
        return emrProjectManagerService.updateProject(projectManager);
    }

    /**
     * 删除项目
     *
     * @param projectId projectId
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "删除项目", notes = "")
    @LogRemark(operate = "删除项目", module = "项目管理")
    @DeleteMapping("/deleteProjectById/{projectId}")
    public R updateProject(@PathVariable(name = "projectId") String projectId) {
        return emrProjectManagerService.deleteProjectById(projectId);
    }

    /**
     * 查询项目
     *
     * @param emrProjectManagerQuery EmrProjectManagerQuery
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "查询项目", notes = "")
    @LogRemark(operate = "查询项目", module = "项目管理")
    @PostMapping("/queryProject")
    public R queryProject(@RequestBody EmrProjectManagerQuery emrProjectManagerQuery) {
        return emrProjectManagerService.queryProject(emrProjectManagerQuery);
    }

    /**
     * 查询项目管理员
     *
     * @param projectType projectType
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "查询项目管理员", notes = "")
    @GetMapping("/queryProjectAdmin")
    public R queryProjectAdmin(@RequestParam String projectType) {
        return emrProjectManagerService.queryProjectAdmin(projectType);
    }
}
