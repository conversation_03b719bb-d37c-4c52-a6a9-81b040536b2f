package com.jykj.dqm.auth.service.imp;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.captcha.generator.RandomGenerator;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.dao.UserWxAccountMapper;
import com.jykj.dqm.auth.entity.IdentifyType;
import com.jykj.dqm.auth.entity.LogginForm;
import com.jykj.dqm.auth.entity.PermissionVo;
import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.entity.SysUserDTO;
import com.jykj.dqm.auth.entity.SysUserRole;
import com.jykj.dqm.auth.entity.UserQueryForm;
import com.jykj.dqm.auth.entity.UserWxAccount;
import com.jykj.dqm.auth.service.SysPermissionService;
import com.jykj.dqm.auth.service.SysRoleService;
import com.jykj.dqm.auth.service.SysUserRoleService;
import com.jykj.dqm.auth.service.SysUserService;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.ExceptionEnum;
import com.jykj.dqm.common.LogLogin;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.notice.sms.MyAliyunSms;
import com.jykj.dqm.sso.entity.SsoSysUser;
import com.jykj.dqm.sso.service.SsoService;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.system.service.LogLoginService;
import com.jykj.dqm.utils.HashCryptoFactory;
import com.jykj.dqm.utils.DateTimeUtil;
import com.jykj.dqm.utils.MD5Util;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.PassWordUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.SlidingWindowCounter;
import com.jykj.dqm.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    /**
     * 禁止登录Redis前缀
     */
    private final String DISABLE_LOGIN_PREFIX = "EMRM_DISABLE_LOGIN_";

    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private LogLoginService logLoginService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private UserWxAccountMapper userWxAccountMapper;
    @Autowired
    private SlidingWindowCounter slidingWindowCounter;
    @Autowired
    private SsoService ssoService;

    /**
     * 登录
     *
     * @param logginForm LogginForm
     * @return 登录结果
     * <AUTHOR>
     */
    @Override
    public R login(LogginForm logginForm) {
        String loggingIp = StringUtil.getIpAddr(request);
        log.warn("登录IP={}，登录时间={}", loggingIp, DateTimeUtil.getNowDateTimeStr());
        if (StringUtil.isEmpty(logginForm.getLoginId())) {
            return RUtil.error("登录id不能为空！");
        }
        String key = loggingIp;
        if (StrUtil.isNotBlank(redisTemplate.opsForValue().get(DISABLE_LOGIN_PREFIX + key))) {
            String failedLoginAttemptsTime = getSettingValue("failed.login.attempts.time", "1");
            String failedLoginAttemptsCount = getSettingValue("failed.login.attempts.count", "5");
            Long expire = redisTemplate.opsForValue().getOperations().getExpire(DISABLE_LOGIN_PREFIX + key);
            return RUtil.error("由于您在" + failedLoginAttemptsTime + "分钟内连续登录失败次数达到" + failedLoginAttemptsCount + "次,请"
                    + expire + "秒过后再次登录");
        }
        // 通过loginId查询信息
        SysUser userInfo = sysUserMapper.getUserInfoByLoginId(logginForm.getLoginId());
        if (userInfo == null) {
            dealRateLimit(key, "用户不存在！");
            return RUtil.error("用户不存在！");
        }
        R error;
        // 0:首次认证；1：二次认证
        if ("1".equals(logginForm.getAuthenticationType())) {
            if (StringUtils.isBlank(userInfo.getMobile())) {
                dealRateLimit(key, "手机号未配置！");
                return RUtil.error("手机号未配置！！！");
            }
            // 二次认证
            String oldLogginStr = redisTemplate.opsForValue()
                    .get("UUID:" + userInfo.getMobile() + logginForm.getVerificationCode());
            error = secondaryCertification(logginForm, userInfo, oldLogginStr);
        } else {
            // 首次认证
            error = firstTimeCertification(logginForm, userInfo);
        }
        if (error != null) {
            dealRateLimit(key, error.getMsg());
            return error;
        }

        // 记录登录日志
        recordLog(userInfo);
        // 修改上次登录时间
        String now = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        userInfo.setLastLoginDate(now);
        // 获取本次登录ip
        String ip = StringUtil.getIpAddr(request);
        userInfo.setLastLoginIp(userInfo.getLoginIp());
        userInfo.setLoginIp(ip);
        // 不需要更新密码
        userInfo.setPassword(null);
        int userId = userInfo.getUserId();
        sysUserMapper.updateByUserId(userInfo);
        // 登录
        StpUtil.login(userId);
        StpUtil.getSession().set("userInfo", userInfo);
        // 根据userId获取token过期（数据库是小时）
        int tokenExpirationTime = userInfo.getTokenExpirationTime();
        // 页面上要设置token过期时间，Sa-Token无法做到实时更新token的失效时间，所以放入redis中，全局拦截校验，首先判断redis中是否存在
        redisTemplate.opsForValue().set(StpUtil.getTokenValue(), "1", tokenExpirationTime, TimeUnit.HOURS);
        return RUtil.success(StpUtil.getTokenValueByLoginId(userId));
    }

    private void dealRateLimit(String key, String result) {
        String failedLoginAttemptsTime = getSettingValue("failed.login.attempts.time", "1");
        String failedLoginAttemptsCount = getSettingValue("failed.login.attempts.count", "5");
        String disableLoginTime = getSettingValue("disable.login.time", "1");
        boolean allowedPass = slidingWindowCounter.canAccess(key, Integer.parseInt(failedLoginAttemptsTime) * 60,
                Integer.parseInt(failedLoginAttemptsCount));
        if (!allowedPass) {
            redisTemplate.opsForValue().set(DISABLE_LOGIN_PREFIX + key, "1", Integer.parseInt(disableLoginTime),
                    TimeUnit.MINUTES);
            throw new BusinessException("由于您在" + failedLoginAttemptsTime + "分钟内连续登录失败次数达到" + failedLoginAttemptsCount
                    + "次,请" + disableLoginTime + "分钟过后再次登录");
        }
    }

    private String getSettingValue(String name, String defaultValue) {
        SysConfig sysConfig = RedisUtil.getSysConfigByName(name);
        String failedLoginAttemptsCount;
        if (sysConfig == null || StrUtil.isBlank(sysConfig.getConfigValue())) {
            failedLoginAttemptsCount = defaultValue;
        } else {
            failedLoginAttemptsCount = sysConfig.getConfigValue();
        }
        return failedLoginAttemptsCount;
    }

    private R firstTimeCertification(LogginForm logginForm, SysUser userInfo) {
        String verificationCode = logginForm.getVerificationCode();
        if (StringUtil.isEmpty(verificationCode)) {
            return RUtil.error("验证码为空！");
        }
        String identifyType = logginForm.getIdentifyType();
        if (IdentifyType.ACCOUNT_PASSWORD.getCode().equals(identifyType)) {
            if (StringUtil.isEmpty(logginForm.getPassword())) {
                return RUtil.error("认证类型为账密，但是密码为空！");
            }
            if (verificationCode.length() != 4) {
                return RUtil.error("验证码错误！");
            }
        } else if (IdentifyType.ACCOUNT_CODE.getCode().equals(identifyType)) {
            if (verificationCode.length() != 6) {
                return RUtil.error("验证码错误！");
            }
        } else {
            return RUtil.error("认证类型错误！只能为账密或者短信验证码！");
        }

        // 用手机号替换UUID，补充短信登录时redis的key
        if (StringUtils.isNotBlank(identifyType) && !IdentifyType.ACCOUNT_PASSWORD.getCode().equals(identifyType)) {
            logginForm.setUuid(userInfo.getMobile());
        }

        // 检查验证码(短信和静态验证码都可以用)
        R result = checkPin(verificationCode, logginForm.getUuid());
        if (result.getStatus() != 0) {
            return result;
        }
        if (!IdentifyType.ACCOUNT_CODE.getCode().equals(identifyType)) {
            String passwordMd5Code = HashCryptoFactory.encrypt(logginForm.getPassword());
            // 由于是根据loginId查询的，需要单独检查一下密码是否正确
            if (!userInfo.getPassword().equalsIgnoreCase(passwordMd5Code)) {
                return RUtil.error("用户名或密码错误！");
            }
        }
        // 检查账户信息
        R error = checkUserAccount(userInfo);
        if (error != null) {
            return error;
        }
        String identifyTypes = userInfo.getIdentifyType();
        if (identifyTypes.contains(identifyType)) {
            // 验证通过
            return null;
        }

        // 检查配置的登录方式是否和当前登录方式相同，如果不同，弹出提示，让他重新登录，以及是否需要补充登录
        // 需要二次认证
        if (identifyTypes.contains(IdentifyType.ACCOUNT_PASSWORD_PIN.getCode())) {
            if (identifyType.equals(IdentifyType.ACCOUNT_PASSWORD.getCode())) {
                redisTemplate.opsForValue().set("UUID:" + userInfo.getMobile(), JSONUtil.toJsonStr(logginForm), 5,
                        TimeUnit.MINUTES);
                // 请补充短信验证码
                return RUtil.error("ADD_ACCOUNT_CODE");
            }
            if (identifyType.equals(IdentifyType.ACCOUNT_CODE.getCode())) {
                redisTemplate.opsForValue().set("UUID:" + userInfo.getMobile(), JSONUtil.toJsonStr(logginForm), 5,
                        TimeUnit.MINUTES);
                // 请补充密码
                return RUtil.error("ADD_ACCOUNT_PASSWORD");
            }
        }
        // 需要前端跳转到对应正确登录界面
        if (identifyTypes.contains(IdentifyType.ACCOUNT_PASSWORD.getCode())) {
            // 请密码登录
            return RUtil.error("CHOOSE_ACCOUNT_PASSWORD");
        }
        if (identifyTypes.contains(IdentifyType.ACCOUNT_CODE.getCode())) {
            // 请短信登录
            return RUtil.error("CHOOSE_ACCOUNT_CODE");
        }
        return RUtil.error("认证类型错误！！！");
    }

    private R secondaryCertification(LogginForm logginForm, SysUser userInfo, String oldLogginStr) {
        LogginForm logginFormOld = JSONUtil.toBean(oldLogginStr, LogginForm.class);
        if (!IdentifyType.ACCOUNT_PASSWORD_PIN.getCode().equals(logginForm.getIdentifyType())) {
            return RUtil.error("认证类型错误！");
        }
        // 之前校验的短信验证码，现在需要校验密码
        if (logginFormOld.getIdentifyType().equals(IdentifyType.ACCOUNT_CODE.getCode())) {
            String passwordMd5Code = HashCryptoFactory.encrypt(logginForm.getPassword());
            // 由于是根据loginId查询的，需要单独检查一下密码是否正确
            if (!userInfo.getPassword().equalsIgnoreCase(passwordMd5Code)) {
                return RUtil.error("用户名或密码错误！");
            }
        }
        // 之前校验的密码，现在需要校验短信验证码
        if (logginFormOld.getIdentifyType().equals(IdentifyType.ACCOUNT_PASSWORD.getCode())) {
            R result = checkPin(logginForm.getVerificationCode(),
                    userInfo.getMobile() + logginForm.getVerificationCode());
            if (result.getStatus() != 0) {
                return result;
            }
        }
        return null;
    }

    /**
     * 记录日志
     *
     * @param sysUser SysUser
     * <AUTHOR>
     */
    private void recordLog(SysUser sysUser) {
        LogLogin logLogin = new LogLogin();
        logLogin.setLoginId(sysUser.getLoginId());
        logLogin.setLoginTime(new Date());
        logLogin.setSysId(Constant.SYS_NAME);
        String loggingIp = StringUtil.getIpAddr(request);
        logLogin.setIp(loggingIp);
        logLogin.setUsername(sysUser.getUserName());
        logLogin.setUserId(String.valueOf(sysUser.getUserId()));
        logLoginService.insertSelective(logLogin);
    }

    /**
     * 检查账户信息
     *
     * @param userInfo SysUser
     * @return 结果
     * <AUTHOR>
     */
    private R checkUserAccount(SysUser userInfo) {
        if (userInfo == null) {
            return RUtil.error("用户名或密码错误！");
        }
        Date date = new Date();
        if (userInfo.getUserStatus() != null && !userInfo.getUserStatus().equals(Constant.USER_STATUS_ENABLE)) {
            return RUtil.error("账号未启用！！！");
        } else if (userInfo.getUserStatus() == null) {
            // 如果值为空，看一下是否达到启用日期，如果达到启用日期，就将账号状态更新为启用
            if (date.after(userInfo.getAccountActivationDate())) {
                userInfo.setUserStatus(Constant.USER_STATUS_ENABLE);
                // 将用户状态标志为停用
                updateUserStatus(userInfo);
            } else {
                return RUtil.error("账号未启用！！！");
            }
        } else {
            // 如果账号已经启用判断账号失效日期，并更新
            if (userInfo.getAccountExpirationDate() != null && date.after(userInfo.getAccountExpirationDate())) {
                userInfo.setUserStatus(Constant.USER_STATUS_STOP);
                updateUserStatus(userInfo);
                return RUtil.error("账号已过期！！！");
            }
        }
        return null;
    }

    /**
     * 登出
     *
     * @param token token凭证
     * @return Result
     */
    @Override
    public R loginOut(String token) {
        if (StpUtil.isLogin()) {
            // StpUtil.logout();
            StpUtil.logoutByTokenValue(token);
            redisTemplate.delete(token);
            return RUtil.success();
        } else {
            return RUtil.error("未登陆，无法登出！");
        }
    }

    /**
     * 添加用户
     *
     * @param sysUser SysUser
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R add(SysUser sysUser) {
        String loginId = sysUser.getLoginId();
        if (StringUtil.isEmpty(loginId)) {
            return RUtil.error("登录id不能为空");
        }
        R error = checkMobileAndIdentifyType(sysUser);
        if (error != null) {
            return error;
        }
        String password = sysUser.getPassword();
        // 检查登录密码是否符合要求：密码长度限制必须在8—20之间，不能是纯数字或字母
        // 根据密码判断密码是否符合要求
        error = PassWordUtil.checkPassWord(loginId, password);
        if (error != null) {
            return error;
        }
        SysUser loginUser = this
                .getOne(new QueryWrapper<SysUser>().eq("login_id", loginId).eq("sys_id", Constant.SYS_NAME));
        if (loginUser != null) {
            return RUtil.error("该loginId已存在:   " + loginId);
        }

        sysUser.setPassword(HashCryptoFactory.encrypt(sysUser.getPassword()));
        // 新增账户默认状态为停用，状态字段state为1
        // sysUser.setUserStatus(Constant.USER_STATUS_ENABLE);
        // 如果有账号启用日期，将账号状态修改为null
        Date date = new Date();
        if (sysUser.getAccountActivationDate() != null && date.before(sysUser.getAccountActivationDate())) {
            sysUser.setUserStatus(null);
        }
        String now = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        sysUser.setModifiedDate(now);
        sysUser.setCreateTime(now);
        // 从设置中获取系统设置
        SysConfig config = RedisUtil.getSysConfigByName("password.expiration.time");
        int passwordExpirationTime = Constant.DEFAULT_PASSWORD_EXPIRATION_DAY;
        if (config != null && StringUtil.isInteger(config.getConfigValue())) {
            passwordExpirationTime = Integer.parseInt(config.getConfigValue());
        }
        // 设置密码过期时间
        LocalDate localDate = LocalDate.now();
        LocalDate afterNow = localDate.plusDays(passwordExpirationTime);
        sysUser.setPasswordExpirationDate(DateTimeUtil.LocalDateToString(afterNow));
        if (!StringUtil.isEmpty(sysUser.getPasswordExpirationDate())) {
            sysUserMapper.addSysUser(sysUser);
        } else {
            sysUserMapper.addSysUserWithoutPasswordExpirationDate(sysUser);
        }
        // 添加用户对应的角色
        List<SysRole> list = sysUser.getList();
        if (list != null) {
            if (sysUser.getUserId() == null) {
                sysUser.setUserId(sysUserMapper.getUserInfoByLoginId(sysUser.getLoginId()).getUserId());
            }
            list.stream().forEach(sysRole -> {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setRoleId(Integer.parseInt(sysRole.getRoleId()));
                sysUserRole.setUserId(sysUser.getUserId());
                sysUserRole.setGroupCode(sysUser.getGroupCode());
                sysUserRole.setOrganizationCode(sysUser.getOrganizationCode());
                sysUserRoleService.add(sysUserRole);
            });
        }
        // 添加企业微信账户
        addUserWxAccount(sysUser);
        // 单点登录注册用户
        registSsoUser(sysUser, "NEW", "0");
        return RUtil.success();
    }

    /**
     * 添加用户和微信账号的关系
     *
     * @param sysUser SysUser
     * <AUTHOR>
     */
    private void addUserWxAccount(SysUser sysUser) {
        // 如果为空，清除之前的，再判断，否则无法清除配置的企业微信账号
        userWxAccountMapper.deleteByUserAccount(sysUser.getLoginId());
        if (StrUtil.isEmpty(sysUser.getWxAccount())) {
            return;
        }
        // 添加企业微信账户
        UserWxAccount odrUserWxAccount = UserWxAccount.builder()
                .userAccount(sysUser.getLoginId())
                .wxAccount(sysUser.getWxAccount())
                .userName(sysUser.getUserName())
                .sysId(Constant.SYS_NAME).build();
        userWxAccountMapper.insertSelective(odrUserWxAccount);
    }

    /**
     * 更新用户状态
     *
     * @param sysUser SysUser
     */
    @Override
    public R updateUserStatus(SysUser sysUser) {
        String now = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        sysUser.setModifiedDate(now);
        // 检验状态码
        List<Integer> userStatusList = Arrays.asList(0, 1);
        if (!userStatusList.contains(sysUser.getUserStatus())) {
            return RUtil.error("状态码不正确！");
        }
        sysUserMapper.updateUserStatus(sysUser);
        return RUtil.success();
    }

    /**
     * 更新用户信息
     *
     * @param sysUser SysUser
     * @return Result
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateUser(SysUser sysUser) {
        R error = checkMobileAndIdentifyType(sysUser);
        if (error != null) {
            return error;
        }
        // 如果有账号启用日期，并且还没有到启用日期，就将账号状态修改为null
        Date date = new Date();
        if (sysUser.getAccountActivationDate() != null && date.before(sysUser.getAccountActivationDate())) {
            sysUser.setUserStatus(null);
        }
        // 删除已有角色
        sysUserRoleService.deleteByUserId(sysUser.getUserId());
        // 新建角色关系
        List<SysRole> list = sysUser.getList();
        list.stream().forEach(sysRole -> {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setRoleId(Integer.parseInt(sysRole.getRoleId()));
            sysUserRole.setUserId(sysUser.getUserId());
            sysUserRole.setGroupCode(sysUser.getGroupCode());
            sysUserRole.setOrganizationCode(sysUser.getOrganizationCode());
            sysUserRoleService.add(sysUserRole);
        });
        // 不需要更新密码
        sysUser.setPassword(null);
        sysUserMapper.updateByUserId(sysUser);
        // 删除用户和微信账号的关系，重新添加
        addUserWxAccount(sysUser);
        // 单点登录注册用户
        registSsoUser(sysUser, "UPDATE", "0");
        return RUtil.success();
    }

    /**
     * 验证手机号码格式和认证类别
     *
     * @param sysUser SysUser
     * @return 验证结果
     * <AUTHOR>
     */
    private R checkMobileAndIdentifyType(SysUser sysUser) {
        if (StrUtil.isNotBlank(sysUser.getMobile())) {
            boolean isMatch = ReUtil.isMatch(Constant.MOBILE_REG, sysUser.getMobile());
            if (!isMatch) {
                return RUtil.error("手机号码不正确！");
            }
        } else {
            if (StrUtil.isBlank(sysUser.getIdentifyType())) {
                return RUtil.error("认证方式不能为空！");
            }
            if (sysUser.getIdentifyType().contains(IdentifyType.ACCOUNT_CODE.getCode())
                    || sysUser.getIdentifyType().contains(IdentifyType.ACCOUNT_PASSWORD_PIN.getCode())) {
                return RUtil.error("手机号码为空，不能设置短信验证码相关的认证方式！");
            }
        }
        return null;
    }

    /**
     * 删除用户
     *
     * @param sysUser SysUser
     * @return Result
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R deleteUser(SysUser sysUser) {
        int userId = sysUser.getUserId();
        SysUser sysUserNew = sysUserMapper.getUser(String.valueOf(sysUser.getUserId()));
        sysUserMapper.delete(new QueryWrapper<SysUser>().eq("user_id", userId).eq("sys_id", Constant.SYS_NAME));
        // 删除和角色的关联关系
        sysUserRoleService.deleteByUserId(userId);
        // 删除用户与微信账号的关系
        Map<String, String> userAccount = sysUserMapper.getUserById(Integer.toString(userId));
        if (userAccount != null) {
            userWxAccountMapper.deleteByUserAccount(userAccount.get("LOGINID"));
        }
        // 单点登录注册用户
        registSsoUser(sysUserNew, "UPDATE", "1");
        return RUtil.success();
    }

    /**
     * 查询用户
     *
     * @param userQueryForm UserQueryForm
     * @return List<SysUser>
     */
    @Override
    public R queryList(UserQueryForm userQueryForm) {
        if (StrUtil.isNotBlank(userQueryForm.getEndTime())) {
            userQueryForm.setEndTime(userQueryForm.getEndTime() + " 23:59:59");
        }
        PageHelper.startPage(userQueryForm.getCurrent(), userQueryForm.getSize());
        List<SysUser> list = sysUserMapper.queryList(userQueryForm.getLoginId(), userQueryForm.getUserName(),
                userQueryForm.getMobile(), userQueryForm.getStartTime(), userQueryForm.getEndTime());
        list.stream().forEach(sysUser -> {
            // 将passWord展示为******
            sysUser.setPassword(Constant.PASSWORD);
            List<SysUserRole> userRoleList = sysUserRoleService.queryByUserId(sysUser.getUserId() + "");
            List<SysRole> collect = userRoleList.stream().map(sysUserRole -> {
                SysRole role = sysRoleService.getOne(new QueryWrapper<SysRole>().eq("role_id", sysUserRole.getRoleId())
                        .eq("sys_id", Constant.SYS_NAME));
                if (role == null) {
                    return new SysRole();
                }
                List<PermissionVo> permissionVoList = permissionService.getPermissionByRoleId(role.getRoleId());
                role.setPermissionIds(permissionVoList);
                return role;
            }).collect(Collectors.toList());
            sysUser.setList(collect);
            // 添加微信账号
            UserWxAccount userWxAccount = userWxAccountMapper.selectByUserAccount(sysUser.getLoginId());
            if (userWxAccount != null && StrUtil.isNotEmpty(userWxAccount.getWxAccount())) {
                sysUser.setUserWxAccount(userWxAccount);
                sysUser.setWxAccount(userWxAccount.getWxAccount());
            }
        });

        return RUtil.success(new PageInfo<>(list));
    }

    /**
     * 重置密码
     *
     * @param sysUser SysUser
     */
    @Override
    public R updateUserPassword(SysUser sysUser) {
        SysUser user = sysUserMapper.getUser("" + sysUser.getUserId());
        if (user.getPassword().equals(HashCryptoFactory.encrypt(sysUser.getPassword()))) {
            return RUtil.error("新密码不能与旧密码相同");
        }
        R error = PassWordUtil.checkPassWord(user.getLoginId(), sysUser.getPassword());
        if (error != null) {
            return error;
        }
        // 添加密码和过期时间
        addPasswordAndExpirationTime(sysUser);
        sysUserMapper.updatePasswordByUserId(sysUser);
        return RUtil.success();
    }

    /**
     * 获取用户信息
     *
     * @param token token
     * @return Result
     */
    @Override
    public R getUserInfo(String token) {
        SysUser user = sysUserMapper.getUser("" + StpUtil.getLoginId());
        user.setPassword(Constant.PASSWORD);
        List<SysUserRole> list = sysUserRoleService.queryByUserId(user.getUserId() + "");
        List<SysRole> collect = list.stream().map(sysUserRole -> {
            SysRole role = sysRoleService.getOne(
                    new QueryWrapper<SysRole>().eq("role_id", sysUserRole.getRoleId()).eq("sys_id", Constant.SYS_NAME));
            if (role == null) {
                return new SysRole();
            }
            List<PermissionVo> permissionVoList = permissionService.getPermissionByRoleId(role.getRoleId());
            role.setPermissionIds(permissionVoList);
            return role;
        }).collect(Collectors.toList());
        user.setList(collect);
        return RUtil.success(user);
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    @Override
    public R getUserName() {
        List<String> userNameList = sysUserMapper.getUserName();
        return RUtil.success(userNameList);
    }

    /**
     * 通过用户ID，获取用户名
     *
     * @param id 用户ID
     * @return 用户名
     */
    @Override
    public String getUserNameById(String id) {
        return sysUserMapper.getUserNameById(id);
    }

    @Override
    public R checkUserInfoStatus() {
        SysUser userInfo = sysUserMapper.getUser("" + StpUtil.getLoginId());
        R error = checkUserAccount(userInfo);
        if (error != null) {
            return error;
        }
        Date date = new Date();
        String passwordExpirationDate = userInfo.getPasswordExpirationDate();
        // 3天提示密码即将过期
        LocalDate localDate = LocalDate.now();
        LocalDate beforeNow = localDate.plusDays(Constant.PASSWORD_EXPIRATION_REMINDER_DAY);
        Date beforedate = DateTimeUtil.localDate2Date(beforeNow);
        // 判断密码是否过期，过期提示
        if (!StringUtil.isEmpty(passwordExpirationDate)
                && date.after(DateTimeUtil.getDateByTimeString2(passwordExpirationDate))) {
            return RUtil.error(ExceptionEnum.USER_PASSWORD_ALREADY_EXPIRED);
        }
        // 判断密码是否即将过期，过期提示
        if (!StringUtil.isEmpty(passwordExpirationDate)
                && beforedate.after(DateTimeUtil.getDateByTimeString2(passwordExpirationDate))) {
            // return RUtil.error("密码将在" + passwordExpirationDate + "过期，请及时修改密码！！！");
            return RUtil.error("密码将在" + passwordExpirationDate + "过期，请及时修改密码！！！", "2009");
        }
        return RUtil.success();
    }

    @Override
    public R checkToken() {
        Object loginId = StpUtil.getLoginId();
        long disableTime = StpUtil.getTokenTimeout();
        // 小于5分钟自动续期
        if (disableTime >= 5 * 60) {
            return RUtil.success(StpUtil.getTokenValue());
        }
        SysUser userInfo = sysUserMapper.getUser(String.valueOf(loginId));
        int tokenExpirationTime = userInfo.getTokenExpirationTime();
        StpUtil.login(loginId);
        // 页面上要设置token过期时间，Sa-Token无法做到实时更新token的失效时间，所以放入redis中，全局拦截校验，首先判断redis中是否存在
        redisTemplate.opsForValue().set(StpUtil.getTokenValue(), "1", tokenExpirationTime, TimeUnit.HOURS);
        return RUtil.success(StpUtil.getTokenValueByLoginId(userInfo.getUserId()));
    }

    @Override
    public R<Map<String, Object>> getIdentifyInfo(String loginId) {
        Map<String, Object> map = new HashMap<>();
        SysUser userInfo = sysUserMapper.getUserInfoByLoginId(loginId);
        if (userInfo == null) {
            return RUtil.error("用户不存在，请重新输入！");
        }
        String identifyType = userInfo.getIdentifyType();
        map.put("identifyType", identifyType);
        String uuid = UUID.randomUUID().toString(true);
        map.put("uuid", uuid);
        // 根据情况返回不同的信息
        // "0"---->静态验证码,并将验证码放入session中
        if (identifyType.contains(IdentifyType.ACCOUNT_PASSWORD.getCode())) {
            // createCodeImagesByCaptchaProducer(map);
            createCodeImagesByHutool(map);
        } else if (identifyType.contains(IdentifyType.ACCOUNT_CODE.getCode())
                || identifyType.contains(IdentifyType.ACCOUNT_PASSWORD_PIN.getCode())) {
            // 把手机号脱敏返回
            String mobile = userInfo.getMobile();
            if (StringUtil.isEmpty(mobile)) {
                return RUtil.error("手机号为空!");
            }
            boolean isMatch = ReUtil.isMatch(Constant.MOBILE_REG, mobile);
            if (!isMatch) {
                return RUtil.error("手机号码不正确！");
            }
            // 手机号脱敏
            map.put("mobile", DesensitizedUtil.mobilePhone(mobile));
        } else {
            return RUtil.error("identifyType类型错误！！！");
        }
        return RUtil.success(map);
    }

    /**
     * hutool生成验证码
     *
     * @param map 接收结果
     * <AUTHOR>
     */
    private void createCodeImagesByHutool(Map<String, Object> map) {
        // 静态验证码
        String uuid = StringUtil.getValue(map.get("uuid"));
        String verifyKey = Constant.DQM_REDIS_LOGIN_UUID_KEY + ":" + uuid;
        // 生成验证码
        // 自定义纯数字的验证码（随机4位数字，可重复）
        RandomGenerator randomGenerator = new RandomGenerator("3456789ABCDEFGHKMNPQRSTUWXY", 4);
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(200, 60, 4, 10);
        captcha.setGenerator(randomGenerator);
        // 重新生成code
        captcha.createCode();
        redisTemplate.opsForValue().set(verifyKey, captcha.getCode(), 5, TimeUnit.MINUTES);
        map.put("codeImage", captcha.getImageBase64Data());
    }

    @Override
    public R sendPin(String loginId) {
        // 发送短信验证码
        SysUser userInfo = sysUserMapper.getUserInfoByLoginId(loginId);
        // 检查账户信息
        checkUserAccount(userInfo);
        if (IdentifyType.ACCOUNT_PASSWORD.getCode().equals(userInfo.getIdentifyType())
                || StringUtils.isBlank(userInfo.getMobile())) {
            return RUtil.error("该账户暂不支持短信验证码登录");
        }
        // 构建一个短信的验证码
        String code = getPin();
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("hospital.code");
        R reason;
        if ("********-5".equals(sysConfigByName.getConfigValue())) {
            // 成都8院特殊处理
            String msg = "您好，您的登录验证码为{}，验证码5分钟内有效，请勿向他人泄露";
            reason = MyAliyunSms.sendPin8Hospital(StrUtil.format(msg, code), userInfo.getMobile());
        } else {
            reason = MyAliyunSms.sendPinBySystemConfig(code, userInfo.getMobile());
        }
        // 成功
        if (reason.getStatus() == 0) {
            redisTemplate.opsForValue().set(Constant.DQM_REDIS_LOGIN_UUID_KEY + ":" + userInfo.getMobile(), code, 5,
                    TimeUnit.MINUTES);
        }
        return reason;
    }

    @Override
    public R checkPin(String pin, String uuid) {
        String code = redisTemplate.opsForValue().get(Constant.DQM_REDIS_LOGIN_UUID_KEY + ":" + uuid);
        if (code == null) {
            return RUtil.error("验证失败，验证码失效！");
        }
        if (!code.equalsIgnoreCase(pin)) {
            return RUtil.error("验证失败：验证码错误！");
        }
        return RUtil.success("验证成功");
    }

    @Override
    public R validateWxInterface(String wxAccount) {
        // 根据用户微信号查询账户(目前是多条取一条)
        SysUser userInfo = sysUserMapper.selectByUserWxAccount(wxAccount);
        if (userInfo == null) {
            return RUtil.success("UNBOUND_ACCOUNT");
        }
        // 检查账户信息
        R error = checkUserAccount(userInfo);
        if (error != null) {
            return error;
        }
        StpUtil.login(userInfo.getUserId());
        // 根据userId获取token过期（数据库是小时）
        int tokenExpirationTime = userInfo.getTokenExpirationTime();
        // 页面上要设置token过期时间，Sa-Token无法做到实时更新token的失效时间，所以放入redis中，全局拦截校验，首先判断redis中是否存在
        redisTemplate.opsForValue().set(StpUtil.getTokenValue(), "1", tokenExpirationTime, TimeUnit.HOURS);
        return RUtil.success(StpUtil.getTokenValueByLoginId(userInfo.getUserId()));
    }

    /**
     * 随机生成短信验证码
     *
     * @return 6位短信验证码
     * <AUTHOR>
     */
    private static String getPin() {
        RandomGenerator randomGenerator = new RandomGenerator("*********", 6);
        return randomGenerator.generate();
    }

    @Override
    public R getImagePin() {
        Map<String, Object> map = new HashMap<>();
        String uuid = UUID.randomUUID().toString(true);
        map.put("uuid", uuid);
        // 根据情况返回不同的信息
        createCodeImagesByHutool(map);
        return RUtil.success(map);
    }

    @Override
    public R updateUserPasswordByOldPS(SysUserDTO sysUserDTO) {
        if (sysUserDTO.getPassword().equals(sysUserDTO.getOldPassword())) {
            return RUtil.error("新密码不能与旧密码相同");
        }
        SysUser user = sysUserMapper.getUser("" + StpUtil.getLoginId());
        if (!user.getPassword().equals(HashCryptoFactory.encrypt(sysUserDTO.getOldPassword()))) {
            return RUtil.error("旧密码错误！");
        }
        if (user.getPassword().equals(HashCryptoFactory.encrypt(sysUserDTO.getPassword()))) {
            return RUtil.error("新密码不能与旧密码相同");
        }
        R error = PassWordUtil.checkPassWord(user.getLoginId(), sysUserDTO.getPassword());
        if (error != null) {
            return error;
        }
        SysUser sysUser = MapperUtils.INSTANCE.map(SysUser.class, sysUserDTO);
        // 添加密码和过期时间
        addPasswordAndExpirationTime(sysUser);
        sysUserMapper.updatePasswordByUserId(sysUser);
        return RUtil.success();
    }

    @Override
    public R queryUserInfoList(UserQueryForm userQueryForm) {
        PageHelper.startPage(userQueryForm.getCurrent(), userQueryForm.getSize());
        List<SysUser> userList = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery()
                .like(StrUtil.isNotBlank(userQueryForm.getUserName()), SysUser::getUserName,
                        userQueryForm.getUserName())
                .eq(StrUtil.isNotBlank(userQueryForm.getLoginId()), SysUser::getLoginId, userQueryForm.getLoginId())
                .like(StrUtil.isNotBlank(userQueryForm.getMobile()), SysUser::getMobile, userQueryForm.getMobile()));
        PageInfo<SysUser> pageInfo = new PageInfo<>(userList);
        return RUtil.success(pageInfo);
    }

    /**
     * 添加密码和过期时间
     *
     * @param sysUser SysUser
     * <AUTHOR>
     */
    private void addPasswordAndExpirationTime(SysUser sysUser) {
        sysUser.setPassword(HashCryptoFactory.encrypt(sysUser.getPassword()));
        // 从设置中获取系统设置
        SysConfig config = RedisUtil.getSysConfigByName("password.expiration.time");
        int passwordExpirationTime = Constant.DEFAULT_PASSWORD_EXPIRATION_DAY;
        if (config != null && StringUtil.isInteger(config.getConfigValue())) {
            passwordExpirationTime = Integer.parseInt(config.getConfigValue());
        }
        // 重新设置密码过期时间
        LocalDate localDate = LocalDate.now();
        LocalDate afterNow = localDate.plusDays(passwordExpirationTime);
        sysUser.setPasswordExpirationDate(DateTimeUtil.LocalDateToString(afterNow));
    }

    private void registSsoUser(SysUser sysUser, String type, String deleteFlag) {
        String configValue = RedisUtil.getSysConfigByName("sso.service.registration.switch").getConfigValue();
        if ("N".equalsIgnoreCase(configValue)) {
            return;
        }
        SsoSysUser ssoSysUser = new SsoSysUser();
        ssoSysUser.setGroupCode(Constant.GROUP_CODE);
        ssoSysUser.setOrgCode(Constant.ORGANIZATION_CODE);
        ssoSysUser.setUserId(sysUser.getLoginId());
        ssoSysUser.setDeleteFlag(deleteFlag);
        ssoSysUser.setUserName(sysUser.getUserName());
        String certificate = RedisUtil.getSysConfigByName("sso.service.certificate").getConfigValue();
        ssoSysUser.setCertificate(certificate);
        ssoSysUser.setType(type);
        ssoSysUser.setSystemId(Constant.SYS_NAME);
        R result;
        try {
            result = ssoService.registration(ssoSysUser);
        } catch (Throwable e) {
            log.error("SSO单点登录注册用户失败！！！" + e.getMessage(), e);
            throw new BusinessException("SSO单点登录注册用户失败！！！" + e.getMessage());
        }
        if (-1 == result.getStatus()) {
            throw new BusinessException("SSO单点登录注册用户失败！！！" + result.getMsg());
        }
    }
}
