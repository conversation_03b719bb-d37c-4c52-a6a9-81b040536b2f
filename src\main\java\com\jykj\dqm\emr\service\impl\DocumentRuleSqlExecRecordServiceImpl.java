package com.jykj.dqm.emr.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.emr.entity.DocumentRuleSqlExecRecord;
import com.jykj.dqm.emr.service.DocumentRuleSqlExecRecordService;
import com.jykj.dqm.emr.dao.DocumentRuleSqlExecRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DQM_EMR_DOCUMENT_RULE_SQL_EXEC_RECORD(文档规则配置SQL执行结果数据)】的数据库操作Service实现
* @createDate 2024-02-28 10:17:01
*/
@Service
public class DocumentRuleSqlExecRecordServiceImpl extends ServiceImpl<DocumentRuleSqlExecRecordMapper, DocumentRuleSqlExecRecord>
    implements DocumentRuleSqlExecRecordService{

}
