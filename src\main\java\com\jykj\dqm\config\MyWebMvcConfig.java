package com.jykj.dqm.config;

import com.jykj.dqm.common.UploadImageFile;
import com.jykj.dqm.utils.FileUtil;
import com.jykj.dqm.utils.SystemUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 展示资源
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/21 16:43:07
 */
@Component
public class MyWebMvcConfig implements WebMvcConfigurer {
    @Autowired
    UploadImageFile uploadImageFile;

    @Lazy
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        String uploadPath = uploadImageFile.getLinuxUploadPath();
        if (SystemUtils.isWindows()) {
            uploadPath = uploadImageFile.getWinUploadPath();
        }
        registry.addResourceHandler("/static/viewimage/**").addResourceLocations("file:" + FileUtil.fixPath(uploadPath) + "/");
    }
}
