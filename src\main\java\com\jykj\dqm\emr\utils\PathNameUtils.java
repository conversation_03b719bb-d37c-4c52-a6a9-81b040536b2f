package com.jykj.dqm.emr.utils;

import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.HashCryptoFactory;
import com.jykj.dqm.utils.RedisUtil;

/**
 * 文件名
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/6 17:16:43
 */
public class PathNameUtils {
    /**
     * 组装文件名称
     *
     * @param directoryCode 目录编码
     * @param directoryName 目录名称
     * @param emrRuleType   规则名称
     * @return 文件名称
     * <AUTHOR>
     */
    public static String getFileName(String directoryCode, String directoryName, String emrRuleType) {
        StringBuffer buffer = new StringBuffer();
        return buffer.append(directoryCode).append(HashCryptoFactory.encrypt(directoryName)).append("_")
                .append(emrRuleType)
                .toString();
    }

    /**
     * 组装预览文件名称
     *
     * @param directoryCode 目录编码
     * @param directoryName 目录名称
     * @param emrRuleType   规则名称
     * @return 文件名称
     * <AUTHOR>
     */
    public static String getPreviewFileName(String directoryCode, String directoryName, String emrRuleType) {
        StringBuffer buffer = new StringBuffer();
        return buffer.append(directoryCode).append(HashCryptoFactory.encrypt(directoryName)).append("_")
                .append(emrRuleType)
                .toString();
    }

    /**
     * 组装带路径的文件名称
     *
     * @param path          文件路径
     * @param directoryCode 目录编码
     * @param directoryName 目录名称
     * @param emrRuleType   规则名称
     * @return 带路径的文件名称
     * <AUTHOR>
     */
    public static String getFullPathFileName(String path, String directoryCode, String directoryName,
            String emrRuleType) {
        StringBuffer buffer = new StringBuffer();
        return buffer.append(path).append(directoryCode).append(HashCryptoFactory.encrypt(directoryName)).append("_")
                .append(emrRuleType).toString();
    }

    /**
     * 组装带路径的预览文件名称
     *
     * @param path          文件路径
     * @param directoryCode 目录编码
     * @param directoryName 目录名称
     * @param emrRuleType   规则名称
     * @return 带路径的文件名称
     * <AUTHOR>
     */
    public static String getPreviewFullPathFileName(String path, String directoryCode, String directoryName,
            String emrRuleType) {
        StringBuffer buffer = new StringBuffer();
        return buffer.append(path).append(directoryCode).append(HashCryptoFactory.encrypt(directoryName)).append("_")
                .append(emrRuleType).append("_pre").toString();
    }

    public static String getEmpiricalmaterialFileName(String fileName) {
        boolean horizontal = false;
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("empiricalmaterial.word.horizontal");
        if (sysConfigByName != null) {
            if ("Y".equals(sysConfigByName.getConfigValue())) {
                horizontal = true;
            }
        }
        if (horizontal) {
            fileName = "horizontal/" + fileName;
        }
        return fileName;
    }
}
