package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文档预览
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/27 11:13:45
 */
@Data
public class DocumentPreview {
    /**
     * 导出记录ID
     */
    @ApiModelProperty(value = "导出记录ID")
    private String exportRecordId;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    //@ApiModelProperty(value = "完整目录名称")
    //private String fullDirectoryName;

}
