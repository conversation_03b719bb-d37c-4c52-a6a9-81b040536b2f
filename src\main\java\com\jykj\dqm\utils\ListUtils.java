package com.jykj.dqm.utils;

import java.util.Comparator;
import java.util.List;
import java.util.Locale;

/**
 * ListUtils工具类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public class ListUtils {
    /**
     * 使用list自带的sort方法先进性排序，然后转成toString去判断两个集合是否相等
     *
     * @param list  List
     * @param list1 List
     * @return 相等返回true，不相等false
     */
    public static boolean checkDiffrent(List<String> list, List<String> list1) {
        list.sort(Comparator.comparing(String::hashCode));
        list1.sort(Comparator.comparing(String::hashCode));
        return list.toString().equals(list1.toString());
    }

    /**
     * 检查list是否为空，
     *
     * @param list List
     * @return true为空，false为非空
     * <AUTHOR>
     */
    public static boolean checkNull(List<?> list) {
        return list == null || list.size() == 0;
    }

    /**
     * 检查List是否包含字符串，忽略大小写
     *
     * @param list List
     * @param str  String
     * @return true包含，false不包含
     * <AUTHOR>
     */
    public static boolean checkContainIgnoreCase(List<?> list, Object str) {
        if (checkNull(list) || StringUtil.isEmpty(StringUtil.getValue(str))) {
            return false;
        }
        return list.contains(String.valueOf(str).toUpperCase(Locale.ENGLISH)) || list.contains(String.valueOf(str).toLowerCase(Locale.ENGLISH));
    }
}
