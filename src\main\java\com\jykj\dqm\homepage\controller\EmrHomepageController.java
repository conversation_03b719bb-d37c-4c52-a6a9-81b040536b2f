package com.jykj.dqm.homepage.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.homepage.service.EmrHomepageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 电子病历评级管理首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/14 10:52:01
 */
@Api(tags = {"电子病历评级管理首页"})
@RestController
@RequestMapping("/emr/homepage")
@RequiredArgsConstructor
public class EmrHomepageController {

    private final EmrHomepageService emrHomepageService;

    @ApiOperation(value = "我的任务及进度", notes = "", httpMethod = "GET")
    @GetMapping("/getMytasksAndProgress")
    public R<?> getMytasksAndProgress(@RequestParam("userAccount") String userAccount) {
        Map<String, Object> map = emrHomepageService.getMyTasksAndProgress(userAccount);
        return RUtil.success(map);
    }
}
