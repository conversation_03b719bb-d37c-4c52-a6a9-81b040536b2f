package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户和角色
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 15:04
 */
@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "SYS_USER_ROLE")
public class SysUserRole {
    /**
     * 集团编码
     */
    @TableField(value = "GROUP_CODE")
    private String groupCode = Constant.GROUP_CODE;

    /**
     * 组织编码
     */
    @TableField(value = "ORGANIZATION_CODE")
    private String organizationCode = Constant.ORGANIZATION_CODE;
    /**
     * 权限角色id
     */
    @TableField(value = "USER_ROLE_ID")
    @TableId(type = IdType.INPUT)
    private Integer userRoleId;
    /**
     * 权限id
     */
    @TableField(value = "USER_ID")
    private Integer userId;

    /**
     * 角色id
     */
    @TableField(value = "ROLE_ID")
    private Integer roleId;

    /**
     * 系统编码
     */
    @TableField(value = "SYS_ID")
    @ApiParam(value = "系统编码")
    private String sysId = Constant.SYS_NAME;
}
