package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.stereotype.Component;

/**
 * 非空(null和空字符串)SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class NonNullCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    /*
         SELECT LOGIN_ID from SYS_USER where (LOGIN_ID IS NULL or trim(LOGIN_ID) = '') and SYS_ID='DQM'
     */
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        //检核表
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        //检核where条件
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        //检核明细字段
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        //检核字段
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        //0:null，1：空字符串
        String isNullTag = dataqualityCheckRule.getIsnulltag();

        //问题明细字段SQL
        StringBuilder qstDetailFieldSql = new StringBuilder();
        qstDetailFieldSql.append("select " + detailFiled + " from " + table);
        if (isNullTag.equals("0")) {
            qstDetailFieldSql.append(" where (" + checkRuleColumn + " IS NULL )");
        } else {
            qstDetailFieldSql.append(" where (" + checkRuleColumn + " IS NULL or trim(" + checkRuleColumn + ") = '')");
        }
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" and " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.WZX;
    }
}
