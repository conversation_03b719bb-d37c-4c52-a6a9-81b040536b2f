package com.jykj.dqm.quality.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据质量检查任务配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 14:27:56
 */
@ApiModel(value = "数据质量检查任务配置")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QCTaskDTO {
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private String jobId;
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String checkRuleName;

    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    private String checkRuleId;

    /**
     * 检核系统代码
     */
    @ApiModelProperty(value = "检核系统代码")
    private String sysCode;

    /**
     * 检核系统名称
     */
    @ApiModelProperty(value = "检核系统名称")
    private String sysName;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbNm;

    /**
     * 整体执行标志：0：单独设置 1：整体设置
     */
    @ApiModelProperty(value = "整体执行标志：0：单独设置 1：整体设置")
    private String overallSettingFlag;

    /**
     * 是否覆盖：0：不覆盖 1：覆盖
     */
    @ApiModelProperty(value = "是否覆盖：0：不覆盖 1：覆盖")
    private String whetherCovered;

    /**
     * 检核规则状态 0：启用 1：未启用
     */
    @ApiModelProperty(value = "检核规则状态 0：启用 1：未启用")
    private String checkRuleStatus;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String jobStartDate;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private String jobStartTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String jobEndDate;


    /**
     * 执行方式
     */
    @ApiModelProperty(value = "执行类别：0：手动执行，1：定时调度")
    private String jobExeType;

    /**
     * 间隔时间
     */
    @ApiModelProperty(value = "间隔时间")
    private String jobSpan;

    /**
     * 执行频率
     */
    @ApiModelProperty(value = "执行频次 1：每分 2：每小时 3：每天 4：每周 5：每月")
    private String jobExeRate;

    /**
     * 周执行星期
     */
    @ApiModelProperty(value = "周执行星期")
    private String dayOfWeek;
    /**
     * 月的执行日期
     */
    @ApiModelProperty(value = "月的执行日期")
    private String dayOfMonth;
    /**
     * 月的执行周次
     */
    @ApiModelProperty(value = "月的执行周次")
    private String weekTime;
    /**
     * 月的执行星期
     */
    @ApiModelProperty(value = "月的执行星期")
    private String dayOfWeek2;
    /**
     * 月的执行方式
     */
    @ApiModelProperty(value = "月的执行方式")
    private String chkType;
    /**
     * 执行的月份
     */
    @ApiModelProperty(value = "执行的月份")
    private String chkMonth;
}
