package com.jykj.dqm.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.auth.entity.SysRole;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public interface SysRoleService extends IService<SysRole> {
    /**
     * 添加角色
     *
     * @param sysRole SysRole
     * @return Result
     */
    R add(SysRole sysRole);

    /**
     * 更新角色
     *
     * @param sysRole SysRole
     * @return Result
     */
    R updateRole(SysRole sysRole);

    /**
     * 通过角色Id获取权限
     *
     * @param roleId 角色Id
     * @return Result
     */
    R queryById(String roleId);

    /**
     * 查询角色及其权限
     *
     * @param page 当前页
     * @param size 每页数量
     * @return PageInfo
     */
    PageInfo querylist(int page, int size);

    /**
     * 通过角色Id删除角色
     *
     * @param roleId 角色Id
     */
    void deleteById(Integer roleId);
}
