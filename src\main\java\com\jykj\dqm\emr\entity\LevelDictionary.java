package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 等级字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 9:55:52
 */
@ApiModel(description = "等级字典")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_LEVEL_DICTIONARY")
public class LevelDictionary extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 等级名称
     */
    @TableField(value = "LEVEL_NAME")
    @ApiModelProperty(value = "等级名称")
    private String levelName;

    /**
     * 等级编码
     */
    @TableField(value = "LEVEL_CODE")
    @ApiModelProperty(value = "等级编码")
    private String levelCode;

    private static final long serialVersionUID = 1L;
}