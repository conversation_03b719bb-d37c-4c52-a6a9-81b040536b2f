package com.jykj.dqm.metadata.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResultDTO;
import com.jykj.dqm.metadata.service.MetadataStructureChooseResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 库表结构选择结果
 *
 * 左侧所有表（复用/metadataStructure/getLeftTree）
 * 同步数据结构（执行一次采集）
 * 查询已保存的结构
 * 更新结构
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/14 13:48:09
 */
@Api(tags = {"库表结构选择结果"})
@RestController
@RequestMapping("/metadataStructureChooseResult")
public class MetadataStructureChooseResultController {
    public static final String MODULE_NAME = "库表结构选择结果";

    @Autowired
    private MetadataStructureChooseResultService structureChooseResultService;

    /**
     * 查询已保存的结构
     *
     * @param dataSourceId 数据源ID
     * @return 已保存的结构
     * <AUTHOR>
     */
    @ApiOperation(value = "查询已保存的结构", notes = "库表结构选择结果")
    @GetMapping("/getDBStructure")
    public R getDBStructure(@RequestParam String dataSourceId) {
        return structureChooseResultService.getDBStructure(dataSourceId);
    }

    /**
     * 保存结构
     *
     * @param metadataStructureChooseResultDTO MetadataStructureChooseResultDTO
     * @return 执行结果
     * <AUTHOR>
     */
    @ApiOperation(value = "保存结构", notes = "库表结构选择结果")
    @LogRemark(operate = "保存结构", module = MODULE_NAME)
    @PostMapping("/saveDbStructure")
    public R saveDbStructure(@RequestBody MetadataStructureChooseResultDTO metadataStructureChooseResultDTO) {
        return structureChooseResultService.saveDbStructure(metadataStructureChooseResultDTO);
    }

    /**
     * 同步数据结构
     *
     * @param dataSourceId 数据源ID
     * @return 执行结果
     * <AUTHOR>
     */
    @ApiOperation(value = "同步数据结构", notes = "库表结构选择结果")
    @LogRemark(operate = "同步数据结构", module = MODULE_NAME)
    @GetMapping("/synchronizeDatabaseStructure")
    public R synchronizeDatabaseStructure(@RequestParam String dataSourceId) {
        return structureChooseResultService.synchronizeDatabaseStructure(dataSourceId);
    }
}
