package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import com.jykj.dqm.system.dao.DictMappingsContentMapper;
import com.jykj.dqm.system.dao.SysCodetabContentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代码有效性检核SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class CodeValidityCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    @Autowired
    private SysCodetabContentMapper sysCodetabContentMapper;

    @Autowired
    private DictMappingsContentMapper dictMappingsContentMapper;

    private static SysCodetabContentMapper sysCodetabContentMappers;

    private static DictMappingsContentMapper dictMappingsContentMappers;

    @PostConstruct
    public void init() {
        sysCodetabContentMappers = this.sysCodetabContentMapper;
        dictMappingsContentMappers = this.dictMappingsContentMapper;
    }

    /*
     * select count(1) from XXX where CHECK_RULE_TYPE in
     * (select CHECK_RULE_TYPE from BBB where CHECK_RULE_FATHER_TYPE='YXX')
     * 或者
     * (a,b,c)
     */
    //@Override
    //public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
    //    //检核表
    //    String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
    //    //检核where条件
    //    String checkRuleWhere = getWhereStr(dataqualityCheckRule);
    //    //检核明细字段
    //    String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
    //    //检核字段
    //    String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
    //
    //    String stTable = dataqualityCheckRule.getStCheckSchema() + "." + dataqualityCheckRule.getStCheckRuleTableOrView();
    //    //码值类型字段
    //    String stCheckRuleColumn = dataqualityCheckRule.getStCheckRuleColumn();
    //    //码值字段名称
    //    String cdValFieldNm = dataqualityCheckRule.getCdValFieldNm();
    //    //码值类型（值）
    //    String cdVal = dataqualityCheckRule.getCdVal();
    //    //检核标准来源代码，1选择的数据源/2对标码值管理
    //    String rvwStddSorcCd = dataqualityCheckRule.getRvwStddSorcCd();
    //    //问题明细字段SQL
    //    StringBuilder qstDetailFieldSql = new StringBuilder();
    //    String resultIn;
    //    if ("2".equals(rvwStddSorcCd)) {
    //        // 从本系统对标码值获取码值作为标准
    //        Map<String, Object> paramMap = new HashMap<String, Object>(16);
    //        paramMap.put("TYPE_CODE", cdVal);
    //        List<String> results = getSysCodeValueData(paramMap);
    //        resultIn = String.join("','", results);
    //        if (StrUtil.isNotBlank(resultIn)) {
    //            resultIn = "'" + resultIn + "'";
    //        }
    //    } else {
    //        //选择的数据源数据库里面的码值表
    //        resultIn = "SELECT " + cdValFieldNm + " FROM " + stTable + " WHERE "
    //                + stCheckRuleColumn + "=" + " '" + cdVal + "'";
    //    }
    //    qstDetailFieldSql.append("SELECT " + detailFiled + " FROM " + table);
    //    if (StrUtil.isNotBlank(resultIn)) {
    //        qstDetailFieldSql.append(" WHERE " + checkRuleColumn + " NOT IN (" + resultIn + ")");
    //    }
    //    if (StrUtil.isNotBlank(checkRuleWhere)) {
    //        qstDetailFieldSql.append(" AND " + checkRuleWhere);
    //    }
    //    CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
    //    return checkSqlDTO;
    //}

    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        //检核表
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        //检核where条件
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        //检核明细字段
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        //检核字段
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        //字典表或视图
        String stTable = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getStCheckRuleTableOrView();
        //字典字段
        String stCheckRuleColumn = dataqualityCheckRule.getStCheckRuleColumn();
        //类型（填写的值）
        String cdVal = dataqualityCheckRule.getCdVal();
        //问题明细字段SQL
        StringBuilder qstDetailFieldSql = new StringBuilder();

        // 从本系统的字典表获取码值作为标准
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("stTable", dataqualityCheckRule.getStCheckRuleTableOrView());
        paramMap.put("stCheckRuleColumn", stCheckRuleColumn);
        paramMap.put("cdVal", cdVal);
        // todo 可能特别长，目前术语的字典，最多的有几万个，用IN可能处错
        List<String> results = getTermDictData(paramMap);
        String resultIn = String.join("','", results);
        if (StrUtil.isNotBlank(resultIn)) {
            resultIn = "'" + resultIn + "'";
        }

        qstDetailFieldSql.append("SELECT " + detailFiled + " FROM " + table);
        if (StrUtil.isNotBlank(resultIn)) {
            qstDetailFieldSql.append(" WHERE " + checkRuleColumn + " NOT IN (" + resultIn + ")");
        }
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" AND " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }


    /**
     * 根据条件获取码值内容
     *
     * @param params Map<String, Object> TYPE_CODE
     * @return CONTENT_KEY LIST
     * <AUTHOR>
     */
    protected List<String> getSysCodeValueData(Map<String, Object> params) {
        List<String> results = sysCodetabContentMappers.getSysCodeValueData(params);
        return results;
    }

    /**
     * 根据条件获取字典的值
     *
     * @param params Map<String, Object>
     * @return CONTENT_KEY LIST
     * <AUTHOR>
     */
    protected List<String> getTermDictData(Map<String, Object> params) {
        return dictMappingsContentMappers.getTermDictData(params);
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.DMYXX;
    }
}
