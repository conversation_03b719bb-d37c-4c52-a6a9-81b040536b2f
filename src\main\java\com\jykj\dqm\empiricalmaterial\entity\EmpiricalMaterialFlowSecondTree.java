package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 实证材料流程和资料
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH
 */
@Data
public class EmpiricalMaterialFlowSecondTree implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 流程
     */
    @NotBlank
    @ApiModelProperty(value = "流程")
    private String flowPath;

    /**
     * 流程类型：0-总说明；1-路径组；0-具体路径
     */
    @ApiModelProperty(value = "流程类型：0-总说明；1-路径组；2-具体路径")
    private String flowPathType;

    /**
     * 流程资料
     */
    @ApiModelProperty(value = "流程资料")
    private String flowPathData;


    @ApiModelProperty(value = "children")
    private List<EmpiricalMaterialFlowPath> children;
}
