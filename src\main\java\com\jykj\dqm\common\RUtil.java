package com.jykj.dqm.common;

/**
 * 组装返回给前端的结果实体类的工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 14:53
 */
public class RUtil {

    /**
     * 返回成功，传入返回体具体出參
     *
     * @param object 响应数据
     * @return 组装成功响应体
     * <AUTHOR>
     */
    public static <T> R<T> success(Object object) {
        R result = new R();
        result.setStatus(0);
        result.setMsg("success");
        result.setData(object);
        return result;
    }

    /**
     * 提供给部分不需要出參的接口
     *
     * @return 默认成功响应体
     * <AUTHOR>
     */
    public static <T> R<T> success() {
        return success(null);
    }

    /**
     * 自定义错误信息
     *
     * @param code 编码
     * @param msg  信息
     * @param data 结果
     * @return Result
     * <AUTHOR>
     */
    public static <T> R<T> error(Integer code, String msg, T data) {
        R<T> result = new R();
        result.setStatus(code);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    /**
     * 自定义错误信息
     *
     * @param code 编码
     * @param msg  信息
     * @return Result
     * <AUTHOR>
     */
    public static <T> R<T> error(Integer code, String msg) {
        R<T> result = new R();
        result.setStatus(code);
        result.setMsg(msg);
        result.setData(null);
        return result;
    }

    /**
     * 自定义错误信息
     *
     * @param msg 信息
     * @return Result
     * <AUTHOR>
     */
    public static <T> R<T> error(String msg) {
        R<T> result = new R();
        result.setStatus(-1);
        result.setMsg(msg);
        result.setData(null);
        return result;
    }

    /**
     * 自定义错误信息
     *
     * @param msg  信息
     * @param data 结果
     * @return Result
     * <AUTHOR>
     */
    public static <T> R<T> error(String msg, T data) {
        R result = new R();
        result.setStatus(-1);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    /**
     * 返回异常信息，在已知的范围内
     *
     * @return Result
     * <AUTHOR>
     */
    public static <T> R<T> error(String msg, String code) {
        R<T> result = new R();
        result.setStatus(Integer.parseInt(code));
        result.setMsg(msg);
        result.setData(null);
        return result;
    }

    /**
     * 返回异常信息，在已知的范围内
     *
     * @param exceptionEnum 异常类型
     * @return Result
     * <AUTHOR>
     */
    public static <T> R<T> error(ExceptionEnum exceptionEnum) {
        R<T> result = new R();
        System.out.println(exceptionEnum.getCode() + exceptionEnum.getMsg());
        result.setStatus(Integer.parseInt(exceptionEnum.getCode()));
        result.setMsg(exceptionEnum.getMsg());
        result.setData(null);
        return result;
    }
}
