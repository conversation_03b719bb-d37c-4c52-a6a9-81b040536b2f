package com.jykj.dqm.quality.entity;

import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 质量问题管理查询DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@ApiModel(value = "质量问题管理查询DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataqulityQstnManagerQueryDTO extends MyPageInfo {
    /**
     * 检核系统代码
     */
    @ApiModelProperty(value = "检核系统代码")
    private String sysCode;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbNm;

    /**
     * 检查表或视图
     */
    @ApiModelProperty(value = "检查表或视图")
    private String checkRuleTableOrView;

    /**
     * 检核字段
     */
    @ApiModelProperty(value = "检核字段")
    private String checkRuleColumn;

    /**
     * 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
     */
    @ApiModelProperty(value = "检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性")
    private String checkRuleFatherType;

    /**
     * 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）
     */
    @ApiModelProperty(value = "检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）")
    private String checkRuleType;

    /**
     * 问题名称
     */
    @ApiModelProperty(value = "问题名称")
    private String dataQltyQstnNm;

    /**
     * 检测时间
     */
    @ApiModelProperty(value = "检测时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date checkDt;

    /**
     * 检测结束时间
     */
    @ApiModelProperty(value = "检测结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date checkEndDt;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date handleDt;

    /**
     * 处理结束时间
     */
    @ApiModelProperty(value = "处理结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date handleEndDt;

    /**
     * 问题整改状态代码（1、关闭、2、处理中、3、待处理）
     */
    @ApiModelProperty(value = "问题整改状态代码（1、关闭、2、处理中、3、待处理）")
    private List<String> qstnRctfctnStusCds;

    private static final long serialVersionUID = 1L;
}
