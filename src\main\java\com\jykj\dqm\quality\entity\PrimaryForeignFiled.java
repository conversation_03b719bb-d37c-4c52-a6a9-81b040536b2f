package com.jykj.dqm.quality.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 一致性主外键对应关系
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/23 17:44:03
 */
@ApiModel(value = "PrimaryForeignFiled")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrimaryForeignFiled {
    @ApiModelProperty(value = "主键表字段")
    String primaryKeyTableField;
    @ApiModelProperty(value = "副键表字段")
    String secondaryKeyTableField;
}
