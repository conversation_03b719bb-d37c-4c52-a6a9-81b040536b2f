<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.metadata.dao.MetadataStructureChooseResultMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.metadata.entity.MetadataStructureChooseResult">
        <!--@mbg.generated-->
        <!--@Table DQM_METADATA_STRUCTURE_CHOOSE_RESULT-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="DATA_SOURCE_ID" jdbcType="INTEGER" property="dataSourceId"/>
        <result column="DATABASE_NAME" jdbcType="VARCHAR" property="databaseName"/>
        <result column="SCHEMA_NAME" jdbcType="VARCHAR" property="schemaName"/>
        <result column="CATALOG_NAME" jdbcType="VARCHAR" property="catalogName"/>
        <result column="SYS_CODE" jdbcType="VARCHAR" property="sysCode"/>
        <result column="SYS_NAME" jdbcType="VARCHAR" property="sysName"/>
        <result column="TABLE_NAME" jdbcType="VARCHAR" property="tableName"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="STRUCTURE_TYPE" jdbcType="VARCHAR" property="structureType"/>
        <result column="CHOOSE_RESULT" jdbcType="VARCHAR" property="chooseResult"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List" databaseId="oracle">
        <!--@mbg.generated-->
        ID,
        DATA_SOURCE_ID,
        DATABASE_NAME,
        "SCHEMA_NAME",
        "CATALOG_NAME",
        SYS_CODE,
        SYS_NAME,
        "TABLE_NAME",
        REMARKS,
        STRUCTURE_TYPE,
        CHOOSE_RESULT,
        CREATE_TIME
    </sql>

    <sql id="Base_Column_List" databaseId="mysql">
        <!--@mbg.generated-->
        ID,
        DATA_SOURCE_ID,
        DATABASE_NAME,
        `SCHEMA_NAME`,
        `CATALOG_NAME`,
        SYS_CODE,
        SYS_NAME,
        `TABLE_NAME`,
        REMARKS,
        STRUCTURE_TYPE,
        CHOOSE_RESULT,
        CREATE_TIME
    </sql>

    <insert id="saveBatchMy" databaseId="oracle">
        INSERT INTO DQM_METADATA_STRUCTURE_CHOOSE_RESULT
        (DATA_SOURCE_ID,
         DATABASE_NAME,
         "SCHEMA_NAME",
         SYS_CODE,
         SYS_NAME,
         "TABLE_NAME",
         CHOOSE_RESULT)
        <foreach collection="list" item="item" index="index" separator="union all">
            (
                SELECT #{item.dataSourceId},
                       #{item.databaseName},
                       #{item.schemaName},
                       #{item.sysCode},
                       #{item.sysName},
                       #{item.tableName},
                       #{item.chooseResult}
                FROM DUAL
            )
        </foreach>
    </insert>
</mapper>