package com.jykj.dqm.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasks;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 任务组与子任务关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 14:01:15
 */
@Mapper
public interface TaskGroupSubtasksMapper extends BaseMapper<TaskGroupSubtasks> {
    List<String> getAllTaskGroupDataSourceId();
}