package com.jykj.dqm.quality.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.quality.entity.CheckRuleExecutionRecordQueryDTO;
import com.jykj.dqm.quality.service.DataqualityTaskInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 规则执行记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/26 15:53:37
 */
@Api(tags = {"规则执行记录"})
@RestController
@RequestMapping("/ruleExecutionRecord")
public class CheckRuleExecutionRecordController {
    @Autowired
    private DataqualityTaskInstanceService dataqualityTaskInstanceService;

    @ApiOperation(value = "查询规则执行记录", notes = "规则执行记录")
    @PostMapping("/queryRecord")
    public R queryRecord(@RequestBody CheckRuleExecutionRecordQueryDTO checkRuleExecutionRecordQueryDTO) {
        return dataqualityTaskInstanceService.queryRecord(checkRuleExecutionRecordQueryDTO);
    }

    @ApiOperation(value = "查询规则执行记录详情", notes = "规则执行记录")
    @GetMapping(value = "/queryRecordDetail")
    public R queryRecordDetail(@RequestParam("dataQltyQstnId") String dataQltyQstnId) {
        return dataqualityTaskInstanceService.queryRecordDetail(dataQltyQstnId);
    }

    @ApiOperation(value = "查询系统及数据源", notes = "规则执行记录")
    @GetMapping("/querySysAndDb")
    public R querySysAndDb() {
        return dataqualityTaskInstanceService.querySysAndDb();
    }
}
