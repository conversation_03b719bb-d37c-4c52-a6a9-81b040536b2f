package com.jykj.dqm.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResult;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResultDTO;

/**
 * 库表结构选择结果
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/14 13:43:20
 */
public interface MetadataStructureChooseResultService extends IService<MetadataStructureChooseResult> {
    R getDBStructure(String dataSourceId);

    R saveDbStructure(MetadataStructureChooseResultDTO metadataStructureChooseResult);

    R synchronizeDatabaseStructure(String dataSourceId);
}
