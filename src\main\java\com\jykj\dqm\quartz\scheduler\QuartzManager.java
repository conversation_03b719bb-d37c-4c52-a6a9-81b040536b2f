package com.jykj.dqm.quartz.scheduler;

import cn.hutool.core.date.DateUtil;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.service.impl.ScheduleJobTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class QuartzManager {
    @Autowired
    private Scheduler scheduler;
    @Autowired
    private ScheduleJobTaskService scheduleJobTaskService;

    /**
     * 根据执行标识，进行调度任务的管理
     *
     * @param jobInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void processScheduleJob(ScheduleJobInfo jobInfo) {
        //定时执行
        if ("1".equals(jobInfo.getJobExeType())) {
            jobInfo = CronExpressionUtil.pageValue2CronValue(jobInfo);
            List<ScheduleJobInfo> checkData = scheduleJobTaskService.queryScheduleJobTask(jobInfo);
            if (checkData != null && checkData.size() > 0) {//存在数据
                scheduleJobTaskService.updateScheduleJobTask(jobInfo);
            } else {//新增
                scheduleJobTaskService.addScheduleJobTask(jobInfo);
            }
            try {
                JobDetail jobDetail = scheduler.getJobDetail(JobKey.jobKey(jobInfo.getJobId(), jobInfo.getJobGroup()));
                if (jobDetail == null) {
                    this.addJob(jobInfo);
                } else {
                    this.removeJob(jobInfo);
                    this.addJob(jobInfo);
                }
            } catch (SchedulerException e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e.getMessage(), e);
            }
        } else {
            //手动执行
            List<ScheduleJobInfo> checkData = scheduleJobTaskService.queryScheduleJobTask(jobInfo);
            if (checkData != null && checkData.size() > 0) {//存在数据
                jobInfo.setJobDesc("手动执行");
                scheduleJobTaskService.updateScheduleJobTask(jobInfo);
            } else {
                //新增
                scheduleJobTaskService.addScheduleJobTask(jobInfo);
            }
            //手动执行，需要删除定时
            this.removeJob(jobInfo);
        }
    }

    /**
     * @param jobInfo 任务信息
     * @Description: 添加一个定时任务
     */
    public void addJob(ScheduleJobInfo jobInfo) {
        try {
            // 任务名，任务组，任务执行类
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(CommonJob.TASK_ITEM_DATA_KEY, jobInfo);
            JobDetail jobDetail = null;
            if ("MG".equals(jobInfo.getTaskType()) || "QC".equals(jobInfo.getTaskType())) {
                jobDetail = JobBuilder.newJob(MgDisallowConcurrentJob.class).withIdentity(jobInfo.getJobId(), jobInfo.getJobGroup()).setJobData(jobDataMap).build();
            } else {
                jobDetail = JobBuilder.newJob(CommonJob.class).withIdentity(jobInfo.getJobId(), jobInfo.getJobGroup()).setJobData(jobDataMap).build();
            }
            CronTrigger trigger = getCronTrigger(jobInfo);

            // 调度容器设置JobDetail和Trigger
            scheduler.scheduleJob(jobDetail, trigger);

            // 启动
            if (!scheduler.isShutdown()) {
                scheduler.start();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建Trigger对象
     *
     * @param jobInfo ScheduleJobInfo
     * @return CronTrigger
     * <AUTHOR>
     */
    private CronTrigger getCronTrigger(ScheduleJobInfo jobInfo) {
        // 触发器
        TriggerBuilder<Trigger> triggerBuilder = TriggerBuilder.newTrigger();
        // 触发器名,触发器组
        triggerBuilder.withIdentity(jobInfo.getJobId(), jobInfo.getJobGroup());
        triggerBuilder.startAt(DateUtil.parseDate(jobInfo.getJobStartDate()));
        triggerBuilder.endAt(DateUtil.parseDateTime(appendEndTime(jobInfo.getJobEndDate())));
        // 触发器时间设定
        triggerBuilder.withSchedule(CronScheduleBuilder.cronSchedule(jobInfo.getCronExpression()).withMisfireHandlingInstructionDoNothing());

        // 创建Trigger对象
        CronTrigger trigger = (CronTrigger) triggerBuilder.build();
        return trigger;
    }

    /**
     * @Description: 修改一个任务的触发时间
     */
    public void modifyJobTime(ScheduleJobInfo jobInfo) {
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(jobInfo.getJobId(), jobInfo.getJobGroup());
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            if (trigger == null) {
                return;
            }

            String oldTime = trigger.getCronExpression();
            if (!oldTime.equalsIgnoreCase(jobInfo.getCronExpression())) {
                /** 方式一 ：调用 rescheduleJob 开始 */
                // 触发器
                trigger = getCronTrigger(jobInfo);
                // 方式一 ：修改一个任务的触发时间
                scheduler.rescheduleJob(triggerKey, trigger);
                //更新jobDetail的jobInfo信息
                JobDetail jobDetail = scheduler.getJobDetail(JobKey.jobKey(jobInfo.getJobId(), jobInfo.getJobGroup()));
                jobDetail.getJobDataMap().put(CommonJob.TASK_ITEM_DATA_KEY, jobInfo);
                /** 方式一 ：调用 rescheduleJob 结束 */

                /** 方式二：先删除，然后在创建一个新的Job */
                // JobDetail jobDetail =
                // scheduler.getJobDetail(JobKey.jobKey(jobName, jobGroupName));
                // Class<? extends Job> jobClass = jobDetail.getJobClass();
                // removeJob(jobName, jobGroupName, triggerName,
                // triggerGroupName);
                // addJob(jobName, jobGroupName, triggerName, triggerGroupName,
                // jobClass, cron);
                /** 方式二 ：先删除，然后在创建一个新的Job */
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 暂停定时任务
     *
     * @param scheduleJob
     * @throws Exception
     */
    public void pauseJob(ScheduleJobInfo scheduleJob) throws Exception {

        JobKey jobKey = JobKey.jobKey(scheduleJob.getJobId(), scheduleJob.getJobGroup());
        try {
            scheduler.pauseJob(jobKey);
        } catch (SchedulerException e) {
            log.error(e.getMessage(), e);
            throw new Exception("暂停定时任务失败");
        }
    }

    /**
     * 恢复任务
     *
     * @param scheduleJob
     * @throws Exception
     */
    public void resumeJob(ScheduleJobInfo scheduleJob) throws Exception {

        JobKey jobKey = JobKey.jobKey(scheduleJob.getJobId(), scheduleJob.getJobGroup());
        try {
            scheduler.resumeJob(jobKey);
        } catch (SchedulerException e) {
            log.error(e.getMessage(), e);
            throw new Exception("恢复定时任务失败");
        }
    }

    /**
     * @param scheduleJob ScheduleJobInfo
     * @Description: 移除一个任务
     */
    public void removeJob(ScheduleJobInfo scheduleJob) {
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(scheduleJob.getJobId(), scheduleJob.getJobGroup());
            if (triggerKey != null) {
                scheduler.pauseTrigger(triggerKey);// 停止触发器
                scheduler.unscheduleJob(triggerKey);// 移除触发器
                scheduler.deleteJob(JobKey.jobKey(scheduleJob.getJobId(), scheduleJob.getJobGroup()));// 删除任务
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * @Description:启动所有定时任务
     */
    public void startJobs() {
        try {
            scheduler.start();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * @Description:关闭所有定时任务
     */
    public void shutdownJobs() {
        try {
            if (!scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 未执行所有任务
     *
     * @return 未执行所有任务
     * @throws Exception
     */
    public List<ScheduleJobInfo> getPlanJobTask() throws Exception {
        GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
        Set<JobKey> jobKeys = scheduler.getJobKeys(matcher);
        List<ScheduleJobInfo> jobList = new ArrayList<ScheduleJobInfo>();
        for (JobKey jobKey : jobKeys) {
            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            ScheduleJobInfo job = (ScheduleJobInfo) jobDetail.getJobDataMap().get(CommonJob.TASK_ITEM_DATA_KEY);
            Trigger trigger = triggers.get(0);
            Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
            job.setJobStatus(triggerState.name());
            jobList.add(job);
        }
        return jobList;
    }

    /**
     * 当前正在执行的所有作业
     *
     * @return 当前正在执行的所有作业
     * @throws Exception
     */
    public List<ScheduleJobInfo> getRuntimeJobTask() throws Exception {
        List<JobExecutionContext> executingJobs = scheduler.getCurrentlyExecutingJobs();
        List<ScheduleJobInfo> jobList = new ArrayList<ScheduleJobInfo>(executingJobs.size());
        for (JobExecutionContext executingJob : executingJobs) {
            JobDetail jobDetail = executingJob.getJobDetail();
            ScheduleJobInfo job = (ScheduleJobInfo) jobDetail.getJobDataMap().get(CommonJob.TASK_ITEM_DATA_KEY);
            Trigger trigger = executingJob.getTrigger();
            Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
            job.setJobStatus(triggerState.name());
            jobList.add(job);
        }
        return jobList;
    }

    public Scheduler getScheduler() {
        return scheduler;
    }

    public void setScheduler(Scheduler scheduler) {
        this.scheduler = scheduler;
    }

    public String transCron(String time) {
        String seconds = StringUtils.substringAfterLast(time, ":");
        String minute = StringUtils.substringAfter(time, ":").substring(0, 2).trim();
        String hour = StringUtils.substringAfter(time, " ").substring(0, 2).trim();
        String day = StringUtils.substringAfterLast(time, "-").substring(0, 2).trim();
        String month = StringUtils.substringAfter(time, "-").substring(0, 2).trim();
        return seconds + " " + minute + " " + hour + " " + day + " " + month + " ?";
    }

    private String appendEndTime(String dateStr) {
        String tmpStr = dateStr.trim();
        if (tmpStr.length() < 19 && !tmpStr.contains(":")) {
            tmpStr += " 23:59:59";
        }
        return tmpStr;
    }

}