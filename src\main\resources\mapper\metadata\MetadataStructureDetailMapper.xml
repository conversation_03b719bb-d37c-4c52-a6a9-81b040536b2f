<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.metadata.dao.MetadataStructureDetailMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.metadata.entity.MetadataStructureDetail">
        <!--@mbg.generated-->
        <!--@Table DQM_METADATA_STRUCTURE_DETAIL-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="STRUCTURE_INFO_ID" jdbcType="INTEGER" property="structureInfoId"/>
        <result column="DATA_SOURCE_ID" jdbcType="INTEGER" property="dataSourceId"/>
        <result column="SCHEMA" jdbcType="VARCHAR" property="schema"/>
        <result column="TABLE_NAME" jdbcType="VARCHAR" property="tableName"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="NULLABLE" jdbcType="VARCHAR" property="nullable"/>
        <result column="PK" jdbcType="VARCHAR" property="pk"/>
        <result column="LENGTH" jdbcType="VARCHAR" property="length"/>
        <result column="SCALE" jdbcType="VARCHAR" property="scale"/>
        <result column="DEFAULTVALUE" jdbcType="VARCHAR" property="defaultvalue"/>
    </resultMap>
    <sql id="Base_Column_List" databaseId="oracle">
        <!--@mbg.generated-->
        ID,
        STRUCTURE_INFO_ID,
        DATA_SOURCE_ID,
        "SCHEMA",
        "TABLE_NAME",
        "NAME",
        REMARKS,
        "TYPE",
        "NULLABLE",
        PK,
        "LENGTH",
        "SCALE",
        DEFAULTVALUE
    </sql>
    <sql id="Base_Column_List" databaseId="mysql">
        <!--@mbg.generated-->
        ID,
        STRUCTURE_INFO_ID,
        DATA_SOURCE_ID,
        `SCHEMA`,
        `TABLE_NAME`,
        `NAME`,
        REMARKS,
        `TYPE`,
        `NULLABLE`,
        PK,
        `LENGTH`,
        `SCALE`,
        DEFAULTVALUE
    </sql>

    <insert id="saveBatch" databaseId="oracle">
        INSERT INTO DQM_METADATA_STRUCTURE_DETAIL
        (STRUCTURE_INFO_ID, DATA_SOURCE_ID, DATABASE_NAME, "SCHEMA", TABLE_NAME, "NAME", "REMARKS", "TYPE", "NULLABLE",
         "PK", "LENGTH", "SCALE", DEFAULTVALUE)
        <foreach collection="list" item="item" index="index" separator="union all">
            (
                SELECT #{item.structureInfoId},
                       #{item.dataSourceId},
                       #{item.databaseName},
                       #{item.schema},
                       #{item.tableName},
                       #{item.name},
                       #{item.remarks},
                       #{item.type},
                       #{item.nullable},
                       #{item.pk},
                       #{item.length},
                       #{item.scale},
                       #{item.defaultvalue}
                FROM DUAL
            )
        </foreach>
    </insert>
</mapper>