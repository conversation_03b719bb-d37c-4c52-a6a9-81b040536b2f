package com.jykj.dqm.auth.controller;

import com.jykj.dqm.auth.entity.EmrSysUserGroupVo;
import com.jykj.dqm.auth.service.EmrSysUserGroupService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * 用户分组表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 24/3/15 18:00:42
 */
@Api(tags = {"用户分组表"})
@RestController
@RequestMapping("/emr/sysUserGroup")
public class EmrSysUserGroupController {
    public static final String MODUE_NAME = "用户分组表";

    @Autowired
    private EmrSysUserGroupService emrSysUserGroupService;

    /**
     * 保存用户分组
     *
     * @param emrSysUserGroupVo EmrSysUserGroupVo
     * @return Result
     */
    @ApiOperation(value = "保存用户分组", notes = "")
    @LogRemark(operate = "保存用户分组", module = MODUE_NAME)
    @PostMapping("/save")
    public R save(@RequestBody EmrSysUserGroupVo emrSysUserGroupVo) {
        return emrSysUserGroupService.save(emrSysUserGroupVo);
    }


    /**
     * 通过分组名称查询用户
     *
     * @param groupName 分组名称
     * @return Result
     */
    @ApiOperation(value = "通过分组名称查询用户", notes = "")
    @GetMapping("/queryByGroupName")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupName", value = "分组名称，直接填名字：0、病历文档；1、基础数据；2、病历数据；3、质量数据；4、实证材料", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String")
    })
    public R queryByGroupName(@NotBlank @RequestParam("groupName") String groupName, @NotBlank @RequestParam("projectId") String projectId) {
        return RUtil.success(emrSysUserGroupService.queryByGroupName(groupName, null, projectId));
    }

}
