package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 电子病历评级相关信息
 *
 * @TableName DQM_EMR_MSG
 */
@TableName(value = "DQM_EMR_MSG")
@Data
public class EmrMsg implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 消息类型（行业新闻、行业标准、政策文件）
     */
    @ApiModelProperty(value = "消息类型（行业新闻、行业标准、政策文件）/sysCodetabContent/query  typeCode=msgType")
    private String msgType;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    private String msgTitle;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String msgContent;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String sender;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendingTime;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "附件")
    private String attachment;

}
