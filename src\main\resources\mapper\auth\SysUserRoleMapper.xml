<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jykj.dqm.auth.dao.SysUserRoleMapper">

    <resultMap id="BaseResultMap" type="com.jykj.dqm.auth.entity.SysUserRole">
        <result column="GROUP_CODE" property="groupCode" jdbcType="VARCHAR"/>
        <result column="ORGANIZATION_CODE" property="organizationCode" jdbcType="VARCHAR"/>
        <result column="USER_ROLE_ID" property="userRoleId" jdbcType="VARCHAR"/>
        <result column="USER_ID" property="userId" jdbcType="VARCHAR"/>
        <result column="ROLE_ID" property="roleId" jdbcType="VARCHAR"/>
        <result column="SYS_ID" property="sysId" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="add" parameterType="com.jykj.dqm.auth.entity.SysUserRole" databaseId="mysql">
        insert into SYS_USER_ROLE
        (GROUP_CODE,ORGANIZATION_CODE,USER_ID,ROLE_ID,SYS_ID)
        VALUES (
        #{sysUserRole.groupCode,jdbcType=VARCHAR},
        #{sysUserRole.organizationCode,jdbcType=VARCHAR},
        #{sysUserRole.userId,jdbcType=VARCHAR},
        #{sysUserRole.roleId,jdbcType=NUMERIC},
        #{sysUserRole.sysId,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="add" parameterType="com.jykj.dqm.auth.entity.SysUserRole"  databaseId="oracle">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="userRoleId">
            SELECT SEQ_SYS_USER_ROLE.nextval as userRoleId from dual
        </selectKey>
        insert into SYS_USER_ROLE
        (GROUP_CODE,ORGANIZATION_CODE,USER_ROLE_ID,USER_ID,ROLE_ID,SYS_ID)
        VALUES (
        #{sysUserRole.groupCode,jdbcType=VARCHAR},
        #{sysUserRole.organizationCode,jdbcType=VARCHAR},
        #{userRoleId},
        #{sysUserRole.userId,jdbcType=VARCHAR},
        #{sysUserRole.roleId,jdbcType=NUMERIC},
        #{sysUserRole.sysId,jdbcType=VARCHAR}
        )
    </insert>


    <select id="queryList" resultMap="BaseResultMap">
        select *
        from SYS_USER_ROLE
        WHERE SYS_ID = 'DQM'
    </select>

    <select id="getAllRoleList" resultType="java.lang.String">
        SELECT DISTINCT ROLE_ID from SYS_USER_ROLE
        WHERE SYS_ID = 'DQM'
        <if test="userId !=null and userId!=''">
            AND USER_ID = #{userId}
        </if>
    </select>
    <select id="getAllPermissionList" resultType="java.lang.String">
        SELECT DISTINCT PERMISSION_ID from SYS_ROLE_PERMISSION t WHERE SYS_ID = 'DQM' AND
        t.ROLE_ID in
        (
        SELECT DISTINCT ROLE_ID from SYS_USER_ROLE
        WHERE SYS_ID = 'DQM'
        <if test="userId !=null and userId!=''">
            AND USER_ID = #{userId}
        </if>
        )
    </select>

    <select id="getAllRoleInfoList" resultType="com.jykj.dqm.auth.entity.SysRole">
        SELECT SUR.USER_ROLE_ID AS USER_ROLE_ID, SR.ROLE_NAME as ROLE_NAME
        FROM SYS_USER_ROLE SUR
        LEFT JOIN SYS_ROLE SR ON
        SUR.ROLE_ID = SR.ROLE_ID and SR.SYS_ID = 'DQM'
        WHERE SUR.SYS_ID = 'DQM' and SR.ROLE_NAME IS NOT NULL
        <if test="userId != null and userId != ''">
            AND SUR.USER_ID = #{userId}
        </if>
    </select>
</mapper>