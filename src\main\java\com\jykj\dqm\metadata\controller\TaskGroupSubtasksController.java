package com.jykj.dqm.metadata.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasksDTO;
import com.jykj.dqm.metadata.service.TaskGroupSubtasksService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 任务组与子任务关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 14:14:40
 */
@Api(tags = {"任务组与子任务关联关系"})
@Slf4j
@RestController
@RequestMapping("/subtask")
public class TaskGroupSubtasksController {
    public static final String MODULE_NAME = "任务组与子任务关联关系";
    @Autowired
    private TaskGroupSubtasksService taskGroupSubtasksService;

    /**
     * 添加或更新子任务
     *
     * @param taskGroupSubtasksDto TaskGroupSubtasksDto
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "添加或更新子任务", notes = "任务组与子任务关联关系")
    @PostMapping("/addOrUpdate")
    @LogRemark(operate = "添加或更新子任务", module = MODULE_NAME)
    public R addOrUpdate(@RequestBody TaskGroupSubtasksDTO taskGroupSubtasksDto) {
        return taskGroupSubtasksService.saveOrUpdateBatch(taskGroupSubtasksDto);
    }

    /**
     * 删除子任务
     *
     * @param ids TaskGroupSubtasksDto
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "删除子任务", notes = "任务组与子任务关联关系")
    @DeleteMapping("/delete")
    @LogRemark(operate = "删除子任务", module = MODULE_NAME)
    public R delete(@RequestParam("ids") List<String> ids) {
        taskGroupSubtasksService.deleteSubTask(ids);
        return RUtil.success("操作成功！");
    }

    /**
     * 查询详细的子任务信息
     *
     * @param taskGroupId 任务组id
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "根据任务组ID查询子任务详情（数据源详情）", notes = "任务组与子任务关联关系")
    @GetMapping("/query")
    public R query(@RequestParam("taskGroupId") String taskGroupId) {
        //需要组装详细的信息（数据源）
        return taskGroupSubtasksService.querySubtask(taskGroupId);
    }
}
