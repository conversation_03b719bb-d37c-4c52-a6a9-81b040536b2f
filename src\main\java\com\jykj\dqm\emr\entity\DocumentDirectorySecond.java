package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 文档目录配置一级标题
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "文档目录配置一级标题")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentDirectorySecond {
    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    @ApiModelProperty(value = "三级标题Map")
    private Map<String, List<DocumentDirectoryConfiguration>> thirdLevels;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentDirectorySecond that = (DocumentDirectorySecond) o;
        return Objects.equals(directoryName, that.directoryName) && Objects.equals(directoryCode, that.directoryCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(directoryName, directoryCode);
    }
}