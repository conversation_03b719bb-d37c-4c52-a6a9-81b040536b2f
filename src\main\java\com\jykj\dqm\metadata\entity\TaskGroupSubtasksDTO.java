package com.jykj.dqm.metadata.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务组和子任务关系表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 14:18:25
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TaskGroupSubtasksDTO {
    /**
     * 任务组
     */
    @ApiModelProperty(value = "任务组ID")
    String taskGroupId;
    /**
     * 需要添加的子任务
     */
    @ApiModelProperty(value = "任务组和子任务关系表")
    List<TaskGroupSubtasks> dqmTaskGroupSubtasks;
}
