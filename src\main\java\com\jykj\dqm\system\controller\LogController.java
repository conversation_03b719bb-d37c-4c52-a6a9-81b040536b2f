package com.jykj.dqm.system.controller;

import com.jykj.dqm.auth.service.SysUserService;
import com.jykj.dqm.common.OperLogForm;
import com.jykj.dqm.common.R;
import com.jykj.dqm.system.service.OperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 操作日志controller
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Api(tags = {"操作日志"})
@RestController
@RequestMapping("/log")
public class LogController {
    @Autowired
    private OperLogService operLogService;

    @Autowired
    private SysUserService sysUserService;

    /**
     * 查询操作日志接口
     *
     * @param operLogQuery OperLogForm
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "查询操作日志接口", notes = "日志管理")
    @PostMapping("/queryoperlog")
    public R queryLog(@RequestBody OperLogForm operLogQuery) {
        return operLogService.queryLog(operLogQuery);
    }

    /**
     * 获取所有的用户列表
     *
     * @return 所有的用户列表
     * <AUTHOR>
     */
    @ApiOperation(value = "获取所有的用户列表", notes = "日志管理")
    @GetMapping("/getUserName")
    public R getUserName() {
        return sysUserService.getUserName();
    }
}
