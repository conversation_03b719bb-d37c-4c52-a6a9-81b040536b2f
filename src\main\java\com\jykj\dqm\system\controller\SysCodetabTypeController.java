package com.jykj.dqm.system.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.system.entity.SysCodetabType;
import com.jykj.dqm.system.entity.SysCodetabTypeDTO;
import com.jykj.dqm.system.service.SysCodetabTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统码值类型
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:50:29
 */
@Api(tags = {"系统码值类型"})
@RestController
@RequestMapping("/sysCodetabType")
public class SysCodetabTypeController {
    public static final String MODULE_NAME = "系统码值类型";
    @Autowired
    private SysCodetabTypeService sysCodetabTypeService;

    @ApiOperation(value = "新增系统码值类型", notes = "系统码值类型")
    @LogRemark(operate = "新增系统码值类型", module = MODULE_NAME)
    @PostMapping("/add")
    public R add(@RequestBody SysCodetabType sysCodetabType) {
        return sysCodetabTypeService.addSysCodetabType(sysCodetabType);
    }

    @ApiOperation(value = "更新系统码值类型", notes = "系统码值类型")
    @LogRemark(operate = "更新系统码值类型", module = MODULE_NAME)
    @PostMapping("/modify")
    public R modify(@RequestBody SysCodetabType sysCodetabType) {
        return sysCodetabTypeService.modifySysCodetabType(sysCodetabType);
    }

    @ApiOperation(value = "删除系统码值类型（提示用户会删除对应的码值内容）", notes = "系统码值类型")
    @LogRemark(operate = "删除系统码值类型", module = MODULE_NAME)
    @DeleteMapping("/delete")
    public R delete(@RequestParam("ids") List<Integer> ids) {
        return sysCodetabTypeService.deleteSysCodetabType(ids);
    }

    @ApiOperation(value = "查询系统码值类型", notes = "系统码值类型")
    @PostMapping("/query")
    public R query(@RequestBody SysCodetabTypeDTO sysCodetabTypeDTO) {
        return sysCodetabTypeService.querySysCodetabType(sysCodetabTypeDTO);
    }
}
