package com.jykj.dqm.auth.controller;

import com.jykj.dqm.auth.entity.SysRolePermission;
import com.jykj.dqm.auth.service.SysRolePermissionService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Api(tags = {"权限管理"})
@RestController
@RequestMapping("/sysRolePermission")
public class SysRolePermissionController {
    public static final String MODUE_NAME = "权限管理";
    @Autowired
    private SysRolePermissionService sysRolePermissionService;

    /**
     * 新增角色权限
     *
     * @param sysRolePermission SysRolePermission
     * @return Result
     */
    @ApiOperation(value = "添加角色权限", notes = "")
    @LogRemark(operate = "添加角色权限", module = MODUE_NAME)
    @PostMapping("/add")
    public R add(@RequestBody SysRolePermission sysRolePermission) {
        return sysRolePermissionService.add(sysRolePermission);
    }

    /**
     * 修改角色权限
     *
     * @param sysRolePermission SysRolePermission
     * @return Result
     */
    @ApiOperation(value = "修改角色权限", notes = "")
    @LogRemark(operate = "修改角色权限", module = MODUE_NAME)
    @PostMapping("/update")
    public R update(@RequestBody SysRolePermission sysRolePermission) {
        return RUtil.success(sysRolePermissionService.updateById(sysRolePermission));
    }

    /**
     * 修改角色权限
     *
     * @param sysRolePermissionId 角色权限ID
     * @return Result
     */
    @ApiOperation(value = "删除角色权限", notes = "")
    @LogRemark(operate = "删除角色权限", module = MODUE_NAME)
    @PostMapping("/delete")
    public R delete(String sysRolePermissionId) {
        return RUtil.success(sysRolePermissionService.removeById(sysRolePermissionId));
    }

    /**
     * 查询所有用户角色
     *
     * @param page 当前页
     * @param size 每页数量
     * @return Result
     */
    @ApiOperation(value = "查询所有用户角色", notes = "")
    @GetMapping("/list")
    public R list(Integer page, Integer size) {
        return sysRolePermissionService.queryList(page, size);
    }

    /**
     * 通过角色权限ID获取角色权限
     *
     * @param sysRolePermissionId 角色权限ID
     * @return Result
     */
    @ApiOperation(value = "通过角色权限ID获取角色权限", notes = "")
    @GetMapping("/queryById")
    public R queryById(String sysRolePermissionId) {
        return RUtil.success(sysRolePermissionService.getById(sysRolePermissionId));
    }

    /**
     * 通过角色Id查询角色权限
     *
     * @param roleId 角色Id
     * @return List<SysRolePermission>
     */
    @ApiOperation(value = "通过角色Id查询角色权限", notes = "")
    @GetMapping("/queryByRoleId")
    public R queryByRoleId(Integer roleId) {
        return RUtil.success(sysRolePermissionService.queryByRoleId(roleId));
    }

    /**
     * 删除角色权限
     *
     * @param roleId 角色Id
     */
    @ApiOperation(value = "根据角色id删除角色权限", notes = "")
    @LogRemark(operate = "根据角色id删除角色权限", module = MODUE_NAME)
    @DeleteMapping("/deleteByRoleId")
    public R deleteByRoleId(String roleId) {
        sysRolePermissionService.deleteByRoleId(roleId);
        return RUtil.success();
    }


}
