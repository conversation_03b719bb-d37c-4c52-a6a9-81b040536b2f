package com.jykj.dqm.emr.manager;

import cn.hutool.core.lang.Tuple;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 权限控制-级别
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/20 15:23:40
 */
public class PermissionLevelCodeUtil {
    /**
     * 获取权限控制的级别
     *
     * @return 级别
     */
    public static Integer getLevelCode() {
        return 5;
    }

    public static void main(String[] args) {
        //RSA rsa = new RSA();
        ////获得私钥
        //String privateKeyBase64 = rsa.getPrivateKeyBase64();
        ////获得公钥
        //String publicKeyBase64 = rsa.getPublicKeyBase64();
        //System.out.println(privateKeyBase64);
        //System.out.println(publicKeyBase64);

        String publicKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCyT/dPwLCVsHGFyEohDoduWg6OdwBOL9gCgjL+Upu4pnFtI36zauVYvpIBMI78yjDl1e1FkBFomnd8Ze3zFCyr1QMe757kcHB/zC6I9HHg26IfdtXji0d64vPtBpCQWqkX9+DBpunxRkbC5zvb9Jyi6Caf1D39jpHJX0tw+spsnQIDAQAB";
        RSA rsa1 = new RSA(null, publicKeyBase64);
        //公钥加密
        byte[] encrypt = rsa1.encrypt(StrUtil.bytes("5", CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        String s = HexUtil.encodeHexStr(encrypt);


        String privateKeyBase64 = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALJP90/AsJWwcYXISiEOh25aDo53AE4v2AKCMv5Sm7imcW0jfrNq5Vi+kgEwjvzKMOXV7UWQEWiad3xl7fMULKvVAx7vnuRwcH/MLoj0ceDboh921eOLR3ri8+0GkJBaqRf34MGm6fFGRsLnO9v0nKLoJp/UPf2OkclfS3D6ymydAgMBAAECgYAJpn58U5gbAEAjA4dQVhcQhJFtUDwOsm2VGsY3JNhmfD3/FqDL7Aw6kW6ZRhWb0Wn0RNY0SBfkTeVxymbZtSAcgeGJdgxbIbL/W+ODyF6VDaYsca2VY5c3uM1kclxqgPQ93zt9EykaoCvegsPp0O2Hg3qF4290DklmpoirsrM0XwJBAOBvOkrEGBs52C0hBNqERqE3uW4DBOXLFSuoU7lOD72xOdJOrgiHfuJQZic8wRFmbB7Vpk+ViFcnRvZfkf8A9rsCQQDLZBwa1s0UWrPMX7KEw+BD5ca1yJgh89NXnGc48eu3gtyiCIJcjfv1ioaftA7eCJMGv+dl248U00qZ4GoY2/CHAkBkVRUoK3XjZf6gXix/S7cPh4vcBHmCCUHTnJKhOqaK1NnWjNn8ciZlIjAoxl5BWxpmxnhtB5pF7AcrSRpJd7LVAkBF2XSZdwlpIxk6Z+ccF1TunHvYtDIi/uSm4uzELrkABe4Vq4xxksVj9xXyxNiSQimUGNW0Mmn6AfJN1Dc7oFUDAkEAnM1tE9b24ESCP8WNdrpT8WTg/+fWENcPBXAkhX7THnnF3Ry++nDDddzj6a2Ey9w35G49h5y9oGkEVorzyiKk9Q==";
        //私钥解密
        RSA rsa2 = new RSA(privateKeyBase64, null);
        byte[] decrypt = rsa2.decrypt(HexUtil.decodeHex(s), KeyType.PrivateKey);
        System.out.println(StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8));

        Tuple tuple = new Tuple(publicKeyBase64, privateKeyBase64);
        System.out.println(tuple.size());
    }

    public static Map<String, String> generateRSAPublicKeyAndPrivateKey() {
        RSA rsa = new RSA();
        //获得私钥
        String privateKeyBase64 = rsa.getPrivateKeyBase64();
        //获得公钥
        String publicKeyBase64 = rsa.getPublicKeyBase64();
        System.out.println(privateKeyBase64);
        System.out.println(publicKeyBase64);
        return MapUtil.builder(new HashMap<String, String>()).put("privateKeyBase64", privateKeyBase64).put("publicKeyBase64", publicKeyBase64).build();
    }

    public static String publicKeyEncryption(String publicKeyBase64, String value) {
        RSA rsa1 = new RSA(null, publicKeyBase64);
        //公钥加密
        byte[] encrypt = rsa1.encrypt(StrUtil.bytes(value, CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        return HexUtil.encodeHexStr(encrypt);
    }

    public static String privateKeyDecryption(String privateKeyBase64, String encryptionValue) {
        //私钥解密
        RSA rsa2 = new RSA(privateKeyBase64, null);
        byte[] decrypt = rsa2.decrypt(HexUtil.decodeHex(encryptionValue), KeyType.PrivateKey);
        return StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8);
    }
}
