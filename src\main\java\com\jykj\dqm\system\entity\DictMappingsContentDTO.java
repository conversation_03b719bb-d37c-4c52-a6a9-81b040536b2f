package com.jykj.dqm.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 术语映射内容DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 14:26:57
 */
@ApiModel(value = "术语映射内容DTO")
@Data
public class DictMappingsContentDTO extends MyPageInfo implements Serializable {
    /**
     * 术语编码
     */
    @TableField(value = "TERM_ID")
    @ApiModelProperty(value = "术语编码")
    private String termId;

    /**
     * 映射类型
     */
    @TableField(value = "MAPPINGS_TYPE")
    @ApiModelProperty(value = "映射类型")
    private String mappingsType;

    /**
     * 源字段
     */
    @TableField(value = "SOURCE_FIELD")
    @ApiModelProperty(value = "源字段")
    private String sourceField;

    /**
     * 源字段编码
     */
    @TableField(value = "SOURCE_FIELD_CODE")
    @ApiModelProperty(value = "源字段编码")
    private String sourceFieldCode;

    /**
     * 目标字段
     */
    @TableField(value = "TARGET_FIELD")
    @ApiModelProperty(value = "目标字段")
    private String targetField;

    /**
     * 目标字段编码
     */
    @TableField(value = "TARGET_FIELD_CODE")
    @ApiModelProperty(value = "目标字段编码")
    private String targetFieldCode;

    /**
     * 是否匹配
     */
    @TableField(value = "ISMATCH")
    @ApiModelProperty(value = "是否匹配")
    private String isMatch;

    private static final long serialVersionUID = 1L;
}