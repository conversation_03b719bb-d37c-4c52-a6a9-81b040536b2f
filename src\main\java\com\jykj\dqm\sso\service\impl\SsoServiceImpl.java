package com.jykj.dqm.sso.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.LogLogin;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.sso.entity.SsoSysUser;
import com.jykj.dqm.sso.service.SsoService;
import com.jykj.dqm.system.service.LogLoginService;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 单点登录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/2 15:49
 */
@Service
public class SsoServiceImpl implements SsoService {
    @Autowired
    private HttpServletRequest request;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private LogLoginService logLoginService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public R registration(SsoSysUser ssoSysUser) {
        String resultStr;
        String registrationUrl = RedisUtil.getSysConfigByName("sso.service.registration.addr").getConfigValue();
        try {
            Map<String, Object> ssoUserMap = BeanUtil.beanToMap(ssoSysUser);
            resultStr =
                    HttpUtil.createPost(registrationUrl).timeout(5 * 1000)
                            .body(JSONUtils.toJSONString(ssoUserMap))
                            .execute().body();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        if (StringUtil.isEmpty(resultStr)) {
            return RUtil.error("注册失败！！！");
        }
        R result = JSONArray.parseObject(resultStr, R.class);
        return result;
    }

    /**
     * 单点登录
     * 实现思路：
     * 先用token去sso认证，看看是否有效，
     * 如果有效，验证主数据是否有该账号，
     * 如果有该账号，并且状态正常，直接这边登录，不再依赖SSO
     *
     * @param token 凭证
     * @return 结果
     * <AUTHOR>
     */
    @Override
    public R doSsoLogin(String token) {
        Map map = new HashMap();
        map.put("systemId", Constant.SYS_NAME);
        map.put("token", token);
        String resultStr;
        try {
            String tokenVerificationUrl = RedisUtil.getSysConfigByName("sso.service.tokenVerification.addr").getConfigValue();
            resultStr = HttpUtil.createPost(tokenVerificationUrl).timeout(5 * 1000)
                    .body(JSONUtils.toJSONString(map))
                    .execute().body();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        if (StringUtil.isEmpty(resultStr)) {
            return RUtil.error("登录失败,token检验失败！！！");
        }
        Map<String, Object> result = JSONArray.parseObject(resultStr, Map.class);
        if (!result.containsKey("userId")) {
            return RUtil.error("登录失败,token检验失败！！！");
        }
        if ("true".equalsIgnoreCase(StringUtil.getValue(result.get("expired")))) {
            return RUtil.error("登录失败,token已过期！！！");
        }
        String loginId = StringUtil.getValue(result.get("userId"));
        LambdaQueryWrapper<SysUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysUser::getLoginId, loginId);
        lambdaQueryWrapper.eq(SysUser::getSysId, "DQM");
        SysUser userInfo = sysUserMapper.selectOne(lambdaQueryWrapper);
        if (userInfo == null) {
            return RUtil.error("该用户不存在！！！");
        }
        //记录日志
        recordLog(userInfo);
        if (userInfo.getUserStatus() == null || !userInfo.getUserStatus().equals(Constant.USER_STATUS_ENABLE)) {
            return RUtil.error("账号未启用");
        }
        //修改上次登录时间
        String now = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        userInfo.setLastLoginDate(now);
        //获取本次登录ip
        String ip = StringUtil.getIpAddr(request);
        userInfo.setLastLoginIp(userInfo.getLoginIp());
        userInfo.setLoginIp(ip);
        //不需要更新密码
        userInfo.setPassword(null);
        sysUserMapper.updateByUserId(userInfo);
        //互斥登录
        StpUtil.login(userInfo.getUserId());
        StpUtil.getSession().set("userInfo", userInfo);
        //看看需不需要单独维护一套token
        //redisTemplate.opsForValue().set(token, "1", 60, TimeUnit.MINUTES);
        //多端同时登录 // 指定`账号id`和`设备标识`进行登录 如果不加也能实现
        //StpUtil.login(userInfo.getUserId(), "PC" + System.currentTimeMillis());
        //根据userId获取token过期（数据库是小时）
        int tokenExpirationTime = userInfo.getTokenExpirationTime();
        //页面上要设置token过期时间，Sa-Token无法做到实时更新token的失效时间，所以放入redis中，全局拦截校验，首先判断redis中是否存在
        redisTemplate.opsForValue().set(StpUtil.getTokenValue(), "1", tokenExpirationTime, TimeUnit.HOURS);
        return RUtil.success(StpUtil.getTokenValueByLoginId(userInfo.getUserId()));
    }

    /**
     * 记录日志
     *
     * @param userInfo SysUser
     * <AUTHOR>
     */
    private void recordLog(SysUser userInfo) {
        LogLogin logLogin = new LogLogin();
        logLogin.setLoginId(userInfo.getLoginId());
        logLogin.setLoginTime(new Date());
        logLogin.setSysId(Constant.SYS_NAME);
        String loggingIp = StringUtil.getIpAddr(request);
        logLogin.setIp(loggingIp);
        logLogin.setUsername(userInfo.getUserName());
        logLogin.setUserId(Integer.toString(userInfo.getUserId()));
        logLoginService.insertSelective(logLogin);
    }
}
