package com.jykj.dqm.quartz.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.quartz.entity.CronExpressionItem;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.scheduler.CronExpressionUtil;
import com.jykj.dqm.quartz.scheduler.QuartzManager;
import com.jykj.dqm.quartz.service.impl.ScheduleJobTaskService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "任务配置")
@Slf4j
@RestController
@RequestMapping("/scheduler")
public class SchedulerController {
    @Autowired
    private QuartzManager quartzManager;
    @Autowired
    private ScheduleJobTaskService scheduleJobTaskService;

    @PostMapping(value = "/pushQuartzJob")
    @ResponseBody
    public String pushQuartzJob(@RequestBody ScheduleJobInfo jobInfo) {
        try {
            if (StringUtils.isEmpty(jobInfo.getJobGroup())) {
                jobInfo.setJobGroup(jobInfo.getJobId());
            }
            quartzManager.processScheduleJob(jobInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "NG";
        }
        return "OK";
    }

    @PostMapping(value = "/addJob")
    @ResponseBody
    public R addJob(@RequestBody ScheduleJobInfo jobInfo) throws Exception {
        log.info("===========开始执行调度=========时间为 " + jobInfo.getJobStartDate());
        CronExpressionItem cronStr = CronExpressionUtil.genCronByJobInfo(jobInfo);
        jobInfo.setCronExpression(cronStr.getCronExpression());
        log.info("======cron表达式========" + cronStr);
        quartzManager.addJob(jobInfo);
        return RUtil.success(cronStr);
    }

    @PostMapping(value = "/modeJob")
    @ResponseBody
    public R modeJob(@RequestBody ScheduleJobInfo jobInfo) throws Exception {
        log.info("===========开始执行调度=========时间为 " + jobInfo.getJobStartDate());
        CronExpressionItem cronStr = CronExpressionUtil.genCronByJobInfo(jobInfo);
        jobInfo.setCronExpression(cronStr.getCronExpression());
        log.info("======cron表达式========" + cronStr);
        quartzManager.modifyJobTime(jobInfo);
        return RUtil.success(cronStr);
    }

    @GetMapping(value = "/stopQuartz")
    @ResponseBody
    public R shutDownQuartz() throws Exception {
        log.info("===========关闭调度test==================");
        quartzManager.shutdownJobs();
        return RUtil.success("关闭成功");
    }

    @GetMapping(value = "/getJobTasks")
    @ResponseBody
    public R getJobTasks() throws Exception {
        List<ScheduleJobInfo> jobPlan = quartzManager.getPlanJobTask();
        List<ScheduleJobInfo> jobRuntime = quartzManager.getRuntimeJobTask();
        jobPlan.addAll(jobRuntime);
        return RUtil.success(jobPlan);
    }

    @GetMapping(value = "/getJobInfo")
    @ResponseBody
    public R getJobInfo(String jobId, String taskType) {
        ScheduleJobInfo params = new ScheduleJobInfo();
        params.setJobId(jobId);
        params.setTaskType(taskType);
        List<ScheduleJobInfo> list = scheduleJobTaskService.queryScheduleJobTask(params);
        if (list.size() > 0) {
            return RUtil.success(list.get(0));
        }
        return RUtil.error("查询失败！");
    }

}
