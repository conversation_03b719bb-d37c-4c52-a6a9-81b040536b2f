package com.jykj.dqm.sso.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 人员表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/31 15:46
 */
@ApiModel(value = "人员表")
@Data
public class SsoSysUser implements Serializable {
    /**
     * 用户工号
     */
    @NotBlank
    @ApiModelProperty(value = "用户工号")
    private String userId;

    /**
     * 集团编码
     */
    @ApiModelProperty(value = "集团编码")
    private String groupCode;

    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    /**
     * 密码
     */
    @NotBlank
    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(hidden = true)
    //@ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(hidden = true)
    //@ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(hidden = true)
    //@ApiModelProperty(value = "登录时间")
    private Date loginTime;

    /**
     * 启用标准(0未用 1启用)
     */
    @ApiModelProperty(hidden = true)
    //@ApiModelProperty(value = "启用标准(0未用 1启用)")
    private String active = "1";

    /**
     * 删除标志(0未删 1删除)
     */
    @ApiModelProperty(value = "删除标志(0未删 1删除)")
    private String deleteFlag;

    /**
     * 用户身份令牌
     */
    @ApiModelProperty(hidden = true)
    //@ApiModelProperty(value = "用户身份令牌")
    private String token;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    private String systemId;

    /**
     * 凭证
     */
    @ApiModelProperty(value = "凭证")
    private String certificate;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 性别
     */
    //@NotBlank
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;

    private static final long serialVersionUID = 1L;
}