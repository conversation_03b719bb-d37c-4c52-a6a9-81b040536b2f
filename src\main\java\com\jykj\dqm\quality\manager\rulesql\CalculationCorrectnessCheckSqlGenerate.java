package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.stereotype.Component;

/**
 * 计算正确性检核SQL生成
 * 乘除是四舍五入
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class CalculationCorrectnessCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    /*
       目前不能跨表，不能确定当前表的结果行和对应的明细表行是否对应
     */
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        //检核表
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        //检核where条件
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        //检核明细字段
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        //统计表字段
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        //明细表字段1
        String stCheckRuleColumn = dataqualityCheckRule.getStCheckRuleColumn();
        //明细表字段2
        String cdValFieldNm = dataqualityCheckRule.getCdValFieldNm();
        //四则运算代码
        String calcRuleCd = dataqualityCheckRule.getCalcRuleCd();
        //将四则运算代码转换为具体的内容
        String calcRuleValue = getCalcRuleValue(calcRuleCd);
        String wCondition;
        if ("1".equals(calcRuleCd) || "2".equals(calcRuleCd)) {
            wCondition = checkRuleColumn + " != " + stCheckRuleColumn + calcRuleValue + cdValFieldNm;
        } else {
            //结果小数位数
            String resultSmlNmrlDgit = dataqualityCheckRule.getResultSmlNmrlDgit();
            wCondition = checkRuleColumn + " != ROUND(" + stCheckRuleColumn + calcRuleValue + cdValFieldNm + "," + resultSmlNmrlDgit + ")";
        }
        //问题明细SQL
        StringBuilder qstDetailFieldSql = new StringBuilder();
        qstDetailFieldSql.append("SELECT " + detailFiled + " FROM " + table);
        qstDetailFieldSql.append(" WHERE " + wCondition);
        //添加Where的条件
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" AND " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }

    /**
     * 将四则运算代码转换为具体的内容
     *
     * @param value 四则运算代码
     * @return 四则运算代码具体的内容
     * <AUTHOR>
     */
    private String getCalcRuleValue(String value) {
        String str;
        switch (value) {
            case "1":
                str = " + ";
                break;
            case "2":
                str = " - ";
                break;
            case "3":
                str = " * ";
                break;
            case "4":
                str = " / ";
                break;
            default:
                throw new RuntimeException("四则运算代码,不是1，2，3，4，请修改");
        }
        return str;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.JSZQX;
    }
}
