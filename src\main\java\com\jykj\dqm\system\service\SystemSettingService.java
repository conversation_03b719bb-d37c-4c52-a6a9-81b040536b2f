package com.jykj.dqm.system.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.system.entity.SysConfig;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 系统设置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 13:48
 */
public interface SystemSettingService {
    /**
     * 获取所有系统设置（webShow为Y的）
     *
     * @return Result
     * <AUTHOR>
     */
    R querySysConfig();

    /**
     * 添加系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    R addSysConfig(List<SysConfig> sysConfigList);

    /**
     * 更新系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    R updateSysConfigs(List<SysConfig> sysConfigList);

    /**
     * 获取所有系统设置（包括webShow为N和Y的）
     *
     * @return Map<String, Object>
     * <AUTHOR>
     */
    R queryAllSysConfig();

    /**
     * 上传医院图标
     *
     * @param file     图片文件
     * @param title    标题（备用）
     * @param logoType
     * @return Result
     * <AUTHOR>
     */
    R setHospitalLogo(MultipartFile file, String title, String logoType);

    /**
     * 获取医院图标地址
     *
     * @param logoType
     * @return 医院图标地址
     * <AUTHOR>
     */
    R getHospitalLogoPath(String logoType);
}