<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.system.dao.SysCodetabTypeMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.system.entity.SysCodetabType">
        <!--@mbg.generated-->
        <!--@Table SYS_CODETAB_TYPE-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode"/>
        <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName"/>
        <result column="TYPE_DESC" jdbcType="VARCHAR" property="typeDesc"/>
        <result column="SYS_ID" jdbcType="VARCHAR" property="sysId"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        TYPE_CODE,
        TYPE_NAME,
        TYPE_DESC,
        SYS_ID,
        CREATE_TIME,
        MODIFY_TIME
    </sql>
</mapper>