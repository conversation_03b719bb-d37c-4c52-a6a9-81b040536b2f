package com.jykj.dqm.metadata.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.dao.MetadataStructureDetailMapper;
import com.jykj.dqm.metadata.dao.MetadataStructureInfoMapper;
import com.jykj.dqm.metadata.entity.DBTreeNode;
import com.jykj.dqm.metadata.entity.DataBaseInfo;
import com.jykj.dqm.metadata.entity.MetadataStructureDTO;
import com.jykj.dqm.metadata.entity.MetadataStructureDetail;
import com.jykj.dqm.metadata.entity.MetadataStructureInfo;
import com.jykj.dqm.metadata.entity.SchemaTable;
import com.jykj.dqm.metadata.service.MetadataStructureInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 元数据结构详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 16:16:43
 */
@Service
public class MetadataStructureInfoServiceImpl extends ServiceImpl<MetadataStructureInfoMapper, MetadataStructureInfo> implements MetadataStructureInfoService {
    @Autowired
    private MetadataStructureInfoMapper metadataStructureInfoMapper;

    @Autowired
    private MetadataStructureDetailMapper metadataStructureDetailMapper;

    @Autowired
    private MetadataDatasourceMapper metadataDatasourceMapper;

    //@Override
    //public Result getLeftTree(MetadataStructureDTO metadataStructureDTO) {
    //    List<MetadataStructureInfo> list = metadataStructureInfoMapper.getAllStructuctureInfo(metadataStructureDTO);
    //    Set<String> dbnames = new HashSet<>();
    //    for (MetadataStructureInfo metadataStructureInfo : list) {
    //        dbnames.add(metadataStructureInfo.getDatabaseName());
    //    }
    //    List<DataBaseInfo> dataBaseInfoList = new ArrayList<>();
    //    DataBaseInfo dataBaseInfo = new DataBaseInfo();
    //    Map<String, SchemaTable> map = new HashMap<>();
    //    for (String dbname : dbnames) {
    //        SchemaTable schemaTable = new SchemaTable();
    //        List<String> table = new ArrayList<>();
    //        List<String> views = new ArrayList<>();
    //        for (MetadataStructureInfo metadataStructureInfo : list) {
    //            if (dbname.equalsIgnoreCase(metadataStructureInfo.getDatabaseName())) {
    //                if ("VIEW".equalsIgnoreCase(metadataStructureInfo.getType())) {
    //                    views.add(metadataStructureInfo.getName());
    //                } else {
    //                    table.add(metadataStructureInfo.getName());
    //                }
    //                schemaTable.setSchema(metadataStructureInfo.getSchema());
    //                map.put(metadataStructureInfo.getSchema(), schemaTable);
    //            }
    //        }
    //        schemaTable.setTables(table.stream().distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList()));
    //        schemaTable.setViews(views.stream().distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList()));
    //        dataBaseInfo.setDataBaseName(dbname);
    //        dataBaseInfo.setSchemas(new ArrayList<>(map.values()));
    //        dataBaseInfoList.add(dataBaseInfo);
    //    }
    //    return ResultUtil.success(dataBaseInfoList);
    //}

    @Override
    public R getLeftTree(MetadataStructureDTO metadataStructureDTO) {
        List<MetadataStructureInfo> list = metadataStructureInfoMapper.getAllStructuctureInfo(metadataStructureDTO);
        Set<String> dbnames = new HashSet<>();
        for (MetadataStructureInfo metadataStructureInfo : list) {
            dbnames.add(metadataStructureInfo.getDatabaseName());
        }
        List<DataBaseInfo> dataBaseInfoList = new ArrayList<>();
        DataBaseInfo dataBaseInfo = new DataBaseInfo();
        Map<String, SchemaTable> map = new HashMap<>();
        for (String dbname : dbnames) {
            SchemaTable schemaTable = new SchemaTable();
            for (MetadataStructureInfo metadataStructureInfo : list) {
                if (dbname.equalsIgnoreCase(metadataStructureInfo.getDatabaseName())) {
                    schemaTable.setSchema(metadataStructureInfo.getSchema());
                    map.put(metadataStructureInfo.getSchema(), schemaTable);
                }
            }
            dataBaseInfo.setDataBaseName(dbname);
            dataBaseInfo.setSchemas(new ArrayList<>(map.values()));
            dataBaseInfoList.add(dataBaseInfo);
        }
        //手动添加PID，ID，用于构造树形结构
        List<DBTreeNode> addList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        typeList.add("TABLE");
        typeList.add("VIEW");
        //typeList.add("PROCEDURE");
        int i = 0;
        for (DataBaseInfo baseInfo : dataBaseInfoList) {
            int dbNum = ++i;
            addTreeList(addList, dbNum, 0, baseInfo.getDataBaseName());
            for (SchemaTable schema : baseInfo.getSchemas()) {
                int schemaNum = ++i;
                addTreeList(addList, schemaNum, dbNum, schema.getSchema());
                for (String type : typeList) {
                    int typeNum = ++i;
                    addTreeList(addList, typeNum, schemaNum, type);
                    //注意添加条件，因为没有保证关系了
                    for (MetadataStructureInfo structureInfo : list) {
                        int tableNum = ++i;
                        if (baseInfo.getDataBaseName().equalsIgnoreCase(structureInfo.getDatabaseName())
                                && schema.getSchema().equalsIgnoreCase(structureInfo.getSchema()) && type.equalsIgnoreCase(structureInfo.getType())) {
                            addTreeList(addList, tableNum, typeNum, structureInfo.getName());
                        }
                    }
                }
            }
        }

        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("pid");
        treeNodeConfig.setNameKey("label");

        List<Tree<Integer>> treeNodes = TreeUtil.build(addList, 0, treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getPid());
                    tree.setWeight(treeNode.getId());
                    tree.setName(treeNode.getName());
                });
        return RUtil.success(treeNodes);
    }

    private void addTreeList(List<DBTreeNode> addList, int id, int pid, String name) {
        DBTreeNode dbTreeNode = new DBTreeNode();
        dbTreeNode.setId(id);
        dbTreeNode.setName(name);
        dbTreeNode.setPid(pid);
        addList.add(dbTreeNode);
    }

    @Override
    public R getRightStructures(MetadataStructureDTO metadataStructureDTO) {
        //MetadataDatasource metadataDatasource = metadataDatasourceMapper.selectById(metadataStructureDTO.getDataSourceId());
        PageHelper.startPage(metadataStructureDTO.getPageNum(), metadataStructureDTO.getPageSize());
        LambdaQueryWrapper<MetadataStructureDetail> params = new LambdaQueryWrapper<>();

        //if (!"PRESTO".equalsIgnoreCase(metadataDatasource.getDatabaseType())) {
        params.eq(MetadataStructureDetail::getDataSourceId, metadataStructureDTO.getDataSourceId());
        params.eq(MetadataStructureDetail::getSchema, metadataStructureDTO.getSchema());
        //}
        params.eq(MetadataStructureDetail::getTableName, metadataStructureDTO.getName());
        List<MetadataStructureDetail> metadataStructureInfos = metadataStructureDetailMapper.selectList(params);
        PageInfo pageInfo = new PageInfo(metadataStructureInfos);
        return RUtil.success(pageInfo);
    }
}


