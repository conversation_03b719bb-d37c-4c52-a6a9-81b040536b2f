<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.system.dao.SysCodetabContentMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.system.entity.SysCodetabContent">
        <!--@mbg.generated-->
        <!--@Table SYS_CODETAB_CONTENT-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode"/>
        <result column="CONTENT_KEY" jdbcType="VARCHAR" property="contentKey"/>
        <result column="CONTENT_VALUE" jdbcType="VARCHAR" property="contentValue"/>
        <result column="CONTENT_DESC" jdbcType="VARCHAR" property="contentDesc"/>
        <result column="CONTENT_SEQ" jdbcType="INTEGER" property="contentSeq"/>
        <result column="SYS_ID" jdbcType="VARCHAR" property="sysId"/>
        <result column="DATA1" jdbcType="VARCHAR" property="data1"/>
        <result column="DATA2" jdbcType="VARCHAR" property="data2"/>
        <result column="DATA3" jdbcType="VARCHAR" property="data3"/>
        <result column="DATA4" jdbcType="VARCHAR" property="data4"/>
        <result column="DATA5" jdbcType="VARCHAR" property="data5"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        TYPE_CODE,
        CONTENT_KEY,
        CONTENT_VALUE,
        CONTENT_DESC,
        CONTENT_SEQ,
        SYS_ID,
        DATA1,
        DATA2,
        DATA3,
        DATA4,
        DATA5
    </sql>

    <select id="getSysCodeValueData" resultType="java.lang.String">
        SELECT DISTINCT CONTENT_KEY
        FROM SYS_CODETAB_CONTENT
        WHERE TYPE_CODE = #{TYPE_CODE}
    </select>
</mapper>