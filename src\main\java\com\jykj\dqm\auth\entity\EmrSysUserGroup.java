package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户分组表
 *
 * @TableName DQM_EMR_SYS_USER_GROUP
 */
@TableName(value = "DQM_EMR_SYS_USER_GROUP")
@Data
@Accessors(chain = true)
public class EmrSysUserGroup implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 登录用户id
     */
    @ApiModelProperty(value = "登录用户id")
    private String loginId;

    /**
     * 登录用户名称
     */
    @ApiModelProperty(value = "登录用户名称")
    @TableField(exist = false)
    private String userName;

    /**
     * 登录用户名称
     */
    @ApiModelProperty(value = "性别（F：女，M：男）")
    @TableField(exist = false)
    private String gender;

    /**
     * 0、病历文档；1、基础数据；2、病历数据；3、质量数据；4、实证材料
     */
    @ApiModelProperty(value = "直接填名字:0、病历文档；1、基础数据；2、病历数据；3、质量数据；4、实证材料")
    private String groupName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

}
