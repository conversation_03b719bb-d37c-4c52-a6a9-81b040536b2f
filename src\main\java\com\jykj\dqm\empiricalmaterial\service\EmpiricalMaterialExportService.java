package com.jykj.dqm.empiricalmaterial.service;

import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathQuery;
public interface EmpiricalMaterialExportService {
    /**
     * 异步导出一个目录
     * @param directoryName 目录名称
     * @param directoryCode 目录编码
     * @param projectId 项目ID
     */
    void asyncExportOneDirectory(String directoryName, String directoryCode,String projectId);

    /**
     * 预览Word文档
     * @param documentPreview 文档预览查询参数
     */
    void previewWord(EmpiricalMaterialFlowPathQuery documentPreview);

    /**
     * 下载文档
     * @param exportRecordId 导出记录ID
     */
    void downloadDoc(String exportRecordId);

    /**
     * 异步导出经验材料
     * @param empiricalMaterialExportDTO 经验材料导出数据传输对象
     * @param username 用户名
     * @param tokenValue 令牌值
     */
    void exportAsync(EmpiricalMaterialExportDTO empiricalMaterialExportDTO, String username, String tokenValue);

    /**
     * 导出全部内容
     * @param exportRecordId 导出记录ID
     */
    void exportAll(String exportRecordId);
}

