server:
  tomcat:
    uri-encoding: UTF-8
    #最小线程数(最小工作线程数，默认10)
    min-spare-threads: 100
    #最大线程数（4核8g内存，线程数800，一般是核数*200。操作系统做线程之间的切换调度是有系统开销的，所以不是越多越好）(最大工作线程数，默认200)
    max-threads: 800
    #最大链接数 （这个参数是指在同一时间，tomcat能够接受的最大连接数。一般这个值要大于(max-threads)+(accept-count)。）(最大连接数，默认为10000 )
    max-connections: 10000
    #最大等待队列长度(最大连接等待数，默认100)
    accept-count: 1000
    #请求头最大长度kb
    max-http-header-size: 102400
    #请请求体最大长度kb
    #max-http-post-size: 2097152
  port: 9080
  #链接建立超时时间
  request-timeout: 60s
  #connection-timeout: 12000
  maxHttpHeaderSize: 102400
  #  #Https证书配置
  ssl:
    key-store: classpath:keystore.p12
    key-store-password: Jykj1994
    key-store-type: PKCS12
    key-alias: tomcat
spring:
  cache:
    type: redis
  # Redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 4
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    # password:
    # 连接超时时间（毫秒）
    timeout: 5000ms
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 10
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 5
        # 连接池中的最小空闲连接
        min-idle: 0
  mvc:
    static-path-pattern=/static/**:
    # 配置策略
    matching-strategy: ant-path-matcher

  servlet:
    multipart:
      location: templates
      max-file-size: 20MB
      max-request-size: 50MB
  output:
    ansi:
      enabled: always
  logging:
    level:
      com.scjy.dqm.*.*mapper: DEBUG
      config: classpath:logback-spring.xml
  quartz:
    #相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: clusteredScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: DQM_QRTZ_
            isClustered: false
            #isClustered: true
            clusterCheckinInterval: 10000
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    #数据库方式
    job-store-type: jdbc
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 去除druid自动配置

  datasource:
    dynamic:
      primary: master #配置默认数据源
      datasource:
        master: #主库
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://*************:3307/EMRM?useUnicode=true&characterEncoding=UTF8&allowMultiQueries=true&rewriteBatchedStatements=true
          username: root
          password: 123456
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
        lic: #h2数据库，命名必须是lic
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: org.h2.Driver
          url: jdbc:h2:tcp://*************:9092/~/lic
          username: mip
          password: Jykj1994
      druid:
        max-wait: 60000
        initial-size: 1
        min-idle: 1
        max-active: 20
        test-while-idle: true
        test-on-return: true
        test-on-borrow: true
        validation-query: select 1 FROM dual
        pool-prepared-statements: true

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: input
      columnFormat: "`%s`"

  configuration:
    map-underscore-to-camel-case: true
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true


workerId: 0
datacenterId: 0

pagehelper:
  helperDialect: mysql
  # 分页合理化参数，默认值为false。当该参数设置为 true 时，
  # pageNum<=0 时会查询第一页， pageNum>pages（超过总数时），会查询最后一页。默认false 时，直接根据参数进行查询。
  reasonable: true
  # 设置不自动分页
  supportMethodsArguments: false
  params: count=countSql

# sa-token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: emrtoken
  # token有效期，单位s 默认30天(2592000), -1代表永不过期 86400(1天)
  timeout: 604800
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 是否在初始化配置时打印版本字符画
  isPrint: false
  # 是否打开自动续签 (如果此值为true, 框架会在每次直接或间接调用getLoginId()时进行一次过期检查与续签操作)
  autoRenew: false
  # 配置Sa-Token单独使用的Redis连接 （此处需要和SSO-Server端连接同一个Redis）
  alone-redis:
    # Redis数据库索引
    database: 4
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    #password: Jykj1994
    # 连接超时时间（毫秒）
    timeout: 2000ms
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 10
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 5
        # 连接池中的最小空闲连接
        min-idle: 0

#增强功能需要通过配置yml配置文件开启增强,自2.0.6开始
knife4j:
  enable: true
  # 开启Swagger的Basic认证功能,默认是false
  basic:
    enable: true
    # Basic认证用户名
    username: admin
    # Basic认证密码
    password: Jykj1994

wx:
  config:
    state: 08bf3395d76d4c51b85c715a9bfad66d
    appId: wwfc443458a409249e
    secret: 5Ks-hsRaMxM1_A8Y8_FajgynbrgWoEM1AdKZOjNyro8
    agentid: 1000010
    qywxRedirectUri: http://weixin.wxlove.xin:8086/ssoapi/wx/qywxLogin
    wxRedirectUri: http://weixin.wxlove.xin:8086/ssoapi/wx/callback
    #二维码相关
    qrCodeAddr: https://open.work.weixin.qq.com
    #企业微信认证地址
    authAddr: https://qyapi.weixin.qq.com

uploadfile:
  #驱动
  driver:
    win-upload-path: C:/emrm_upload_file
    linux-upload-path: /home/<USER>
    filesize: 10
    filetypes:
      - jar
      - JAR
  image:
    win-upload-path: C:/emrm_upload_file/image
    linux-upload-path: /home/<USER>/image
    #单位（M）
    imageSize: 10
    filetypes:
      - image/jpeg
      - image/jpg
      - image/png
      - image/bmp
      - image/pjpeg
      - image/x-png
#自定义参数
custom:
  #线程池配置
  thread-pool:
    core-size: 20
    max-size: 200
    keep-alive-time: 20  #TimeUnit.SECONDS
    queueCapacity: 500

system:
  systemId: EMRM    #系统id
  license:
    subject: EMRM    #系统id
    publicAlias: publicCert
    storePass: 123456a
    publicKeysStorePath: /home/<USER>/licenseKeyStorePath/publicCerts.store    # publicCerts.store文件存放路径
    flag: true    #是否启动自动安装证书

# 加密算法配置
crypto:
  hash-algorithm: MD5  # 可选值: SM3, MD5 - 用于哈希加密（如密码哈希）
  symmetric-algorithm: AES  # 可选值: SM4, AES - 用于对称加密（如数据加密）