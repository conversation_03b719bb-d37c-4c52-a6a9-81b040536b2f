package com.jykj.dqm.emr.task;

import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportService;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import com.jykj.dqm.emr.service.DocumentExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@Slf4j
public class DelayQueueManager implements CommandLineRunner {
    @Resource(name = "threadPoolMonitor")
    ThreadPoolExecutor threadPoolMonitor;

    @Lazy
    @Autowired
    private DocumentExportService documentExportService;

    @Lazy
    @Autowired
    private EmpiricalMaterialExportService exportService;

    private final DelayQueue<DelayTask> delayQueue = new DelayQueue<>();

    /**
     * 加入到延时队列中
     *
     * @param task
     */
    public void put(DelayTask task) {
        log.warn("加入延时任务：{}", task);
        delayQueue.put(task);
    }

    /**
     * 取消延时任务
     *
     * @param task
     * @return
     */
    public boolean remove(DelayTask task) {
        log.warn("取消延时任务：{}", task);
        return delayQueue.remove(task);
    }

    /**
     * 取消延时任务
     *
     * @param taskid
     * @return
     */
    public boolean remove(String taskid) {
        return remove(new DelayTask(new TaskBaseEntity(taskid), 0));
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("初始化延时队列");
        threadPoolMonitor.execute(new Thread(this::excuteThread));
    }

    /**
     * 延时任务执行线程
     */
    private void excuteThread() {
        while (true) {
            try {
                DelayTask task = delayQueue.take();
                String type = task.getData().getType();
                if ("previewExport".equals(type)) {
                    documentExportService.previewExport(task.getData().getIdentifier());
                } else if ("doEMExport".equals(type)) {
                    exportService.exportAll(task.getData().getIdentifier());
                } else {
                    //执行任务
                    processTask(task);
                }
                // 适当的睡眠时间，避免过于频繁地访问数据库 todo
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                break;
            }
        }
    }

    /**
     * 内部执行延时任务
     *
     * @param task
     */
    private void processTask(DelayTask task) {
        //获取任务参数，执行业务task.getData().getIdentifier()
        String identifier = task.getData().getIdentifier();
        Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.processMap;
        processMap.remove(identifier);
        log.warn("执行延时任务：{}-{}", task, identifier);
    }
}