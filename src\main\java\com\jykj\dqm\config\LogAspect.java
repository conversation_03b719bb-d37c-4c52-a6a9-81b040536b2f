package com.jykj.dqm.config;

import cn.dev33.satoken.stp.StpUtil;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.common.OperLog;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.system.service.impl.OperLogServiceImpl;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 日志切面--》记录日志
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Aspect
@Component
public class LogAspect {
    /**
     * 日志工厂
     */
    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);

    @Autowired
    private OperLogServiceImpl operLogService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Lazy
    @Autowired
    private LogAspect logAspect;

    /**
     * 定义切面拦截类（记录controller接口信息）
     */
    @Pointcut("execution(* com.jykj.*..*controller..*(..))")
    public void recordLogInfo() {
    }

    /**
     * 标记注解方式拦截（记录日志信息）
     */
    @Pointcut("@annotation(com.jykj.dqm.config.LogRemark)")
    public void recordLog() {
    }

    /**
     * 切面拦截类记录controller日志
     *
     * @param joinPoint JoinPoint
     * <AUTHOR>
     */
    @Before("recordLogInfo()")
    public void before2(JoinPoint joinPoint) {
        // 这个RequestContextHolder是Springmvc提供来获得请求的东西
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();

        // 记录下请求内容
        logger.info("URL : {} ; HTTP_METHOD : {}", request.getRequestURL().toString(), request.getMethod());
        List<String> list = Arrays.asList("registration", "doLogin", "tokenVerification");
        if (list.contains(joinPoint.getSignature().getName())) {
            logger.info("IP : {}", request.getRemoteAddr());
        } else {
            String loginId = "";
            if (StpUtil.isLogin()) {
                loginId = StpUtil.getLoginIdAsString();
            }
            logger.info("USERID : {} ; IP : {}", loginId, request.getRemoteAddr());
        }
        logger.info("PARAMS : " + Arrays.toString(joinPoint.getArgs()));
        // 下面这个getSignature().getDeclaringTypeName()是获取包+类名的   然后后面的joinPoint.getSignature.getName()获取了方法名
        logger.info("CLASS_METHOD : " + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
        // logger.info("################TARGET: " + joinPoint.getTarget());//返回的是需要加强的目标类的对象
        // logger.info("################THIS: " + joinPoint.getThis());//返回的是经过加强后的代理类的对象
    }

    /**
     * 标记注解方式拦截-》记录日志信息
     *
     * @param joinPoint JoinPoint
     * <AUTHOR>
     */
    @Before("recordLog()")
    public void before(JoinPoint joinPoint) {
        try {
            logAspect.dealLog(joinPoint,StpUtil.getLoginIdAsString());
        } catch (Exception e) {
            logger.error("记录日志异常", e);
        }
    }

    /**
     * 处理日志
     *
     * @param joinPoint JoinPoint
     * <AUTHOR>
     */
    @Async("doDQMExecutor")
    public void dealLog(JoinPoint joinPoint,String userId) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperLog operLog = new OperLog();
        operLog.setOperateTime(new Date());
        LogRemark logRemark = method.getAnnotation(LogRemark.class);
        if (logRemark != null) {
            //注解上的描述
            operLog.setOperateContent(logRemark.operate());
            operLog.setFunctionModule(logRemark.module());
        }
        String name = joinPoint.getSignature().getName();
        //过滤一些不需要记录日志的接口
        if (name.contains("/wx/")) {
            return;
        }
        Map<String, String> map = sysUserMapper.getUserById(userId);
        operLog.setUserId(map.get("LOGIN_ID"));
        operLog.setUserName(map.get("USER_NAME"));
        try {
            operLogService.insertLog(operLog);
            logger.info(operLog.toString());
        } catch (Exception e) {
            logger.error("记录日志异常", e);
        }
    }
}
