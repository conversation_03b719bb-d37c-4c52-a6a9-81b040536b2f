package com.jykj.dqm.metadata.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.MetadataStructureDTO;
import com.jykj.dqm.metadata.service.MetadataStructureInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元数据结构详情
 *
 * <AUTHOR>
 */
@Api(tags = {"元数据结构详情"})
@RestController
@RequestMapping("/metadataStructure")
public class MetadataStructureController {
    public static final String MODULE_NAME = "元数据结构详情";
    @Autowired
    private MetadataStructureInfoService metadataStructureInfoService;

    /**
     * 获取左侧树形结构
     *
     * @param metadataStructureDTO MetadataStructureDTO
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "获取左侧树形结构", notes = "元数据结构详情")
    @PostMapping(value = "/getLeftTree")
    public R getLeftTree(@RequestBody MetadataStructureDTO metadataStructureDTO) {
        return metadataStructureInfoService.getLeftTree(metadataStructureDTO);
    }

    /**
     * 获取右侧表或者视图结构
     *
     * @param metadataStructureDTO MetadataStructureDTO
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "获取右侧表或者视图结构", notes = "元数据结构详情")
    @PostMapping(value = "/getRightStructures")
    public R getRightStructures(@RequestBody MetadataStructureDTO metadataStructureDTO) {
        return metadataStructureInfoService.getRightStructures(metadataStructureDTO);
    }
}
