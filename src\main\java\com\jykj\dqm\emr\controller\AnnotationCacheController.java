package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.manager.generateword.GenerateChapterWord;
import com.jykj.dqm.emr.task.AnnotationCacheCleanupTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 批注信息缓存管理接口
 * 提供缓存统计、清理等管理功能
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/emr/annotation-cache")
@Api(tags = "批注信息缓存管理")
public class AnnotationCacheController {

    @Autowired
    private AnnotationCacheCleanupTask annotationCacheCleanupTask;

    /**
     * 获取缓存统计信息
     */
    @ApiOperation(value = "获取缓存统计信息", notes = "查看当前批注信息缓存的使用情况")
    @GetMapping("/statistics")
    public R<Map<String, Object>> getCacheStatistics() {
        try {
            Map<String, Object> stats = annotationCacheCleanupTask.getCurrentCacheStatistics();
            return RUtil.success(stats);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败：{}", e.getMessage(), e);
            return RUtil.error("获取缓存统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 手动清理指定天数前的缓存
     */
    @ApiOperation(value = "手动清理缓存", notes = "清理指定天数前的批注信息缓存")
    @PostMapping("/cleanup")
    public R<Integer> manualCleanup(
            @ApiParam(value = "清理多少天前的数据", example = "1") @RequestParam(defaultValue = "1") int daysAgo) {
        try {
            if (daysAgo < 0) {
                return RUtil.error("天数参数必须大于等于0");
            }

            int clearedCount = annotationCacheCleanupTask.manualCleanup(daysAgo);
            if (clearedCount >= 0) {
                return RUtil.success(clearedCount);
            } else {
                return RUtil.error("清理缓存时发生异常");
            }
        } catch (Exception e) {
            log.error("手动清理缓存失败：{}", e.getMessage(), e);
            return RUtil.error("手动清理缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清理指定导出记录的缓存
     */
    @ApiOperation(value = "清理指定记录缓存", notes = "清理指定导出记录ID的批注信息缓存")
    @DeleteMapping("/record/{exportRecordId}")
    public R<String> clearSpecificCache(
            @ApiParam(value = "导出记录ID", required = true) @PathVariable String exportRecordId) {
        try {
            GenerateChapterWord.clearAnnotationCache(exportRecordId);
            return RUtil.success("成功清理导出记录[" + exportRecordId + "]的缓存");
        } catch (Exception e) {
            log.error("清理指定记录缓存失败：{}", e.getMessage(), e);
            return RUtil.error("清理指定记录缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清理所有缓存
     */
    @ApiOperation(value = "清理所有缓存", notes = "清理所有批注信息缓存，慎用！")
    @PostMapping("/clear-all")
    public R<String> clearAllCache() {
        try {
            Map<String, Object> beforeStats = GenerateChapterWord.getCacheStatistics();
            GenerateChapterWord.clearAllAnnotationCache();

            log.warn("手动清理了所有批注信息缓存，清理前统计：{}", beforeStats);
            return RUtil.success("成功清理所有缓存");
        } catch (Exception e) {
            log.error("清理所有缓存失败：{}", e.getMessage(), e);
            return RUtil.error("清理所有缓存失败：" + e.getMessage());
        }
    }

    /**
     * 立即执行定时清理任务
     */
    @ApiOperation(value = "立即执行清理任务", notes = "立即执行定时清理任务（清理2天前的数据）")
    @PostMapping("/run-scheduled-cleanup")
    public R<String> runScheduledCleanup() {
        try {
            annotationCacheCleanupTask.cleanupExpiredAnnotationCache();
            return RUtil.success("定时清理任务执行完成");
        } catch (Exception e) {
            log.error("执行定时清理任务失败：{}", e.getMessage(), e);
            return RUtil.error("执行定时清理任务失败：" + e.getMessage());
        }
    }
}
