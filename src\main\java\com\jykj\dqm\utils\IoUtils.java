package com.jykj.dqm.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * Io工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/6/12 13:41:40
 */
@Slf4j
public class IoUtils {
    /**
     * 关闭<br>
     * 关闭失败不会抛出异常
     *
     * @param closeable 被关闭的对象
     */
    public static void close(AutoCloseable closeable) {
        if (null != closeable) {
            try {
                closeable.close();
            } catch (Exception e) {
                // 静默关闭
                log.error(e.getMessage(), e);
            }
        }
    }
}
