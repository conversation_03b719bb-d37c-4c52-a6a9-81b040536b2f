package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 测试一整个规则的所有SQL
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "测试一整个规则的所有SQL")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SqlStatementQueryOneRule {
    ///**
    // * 文档导出记录ID
    // */
    //@NotBlank
    //@ApiModelProperty(value = "文档导出记录ID")
    //private String exportRecordId;
    @NotBlank
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据
     */
    @ApiModelProperty(value = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据")
    @NotBlank
    private String configType;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    @NotBlank
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    @NotBlank
    private String directoryCode;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @NotBlank
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    /**
     * 数据开始时间
     */
    @NotBlank
    @ApiModelProperty(value = "数据开始时间")
    private String dataStartTime;

    /**
     * 数据结束时间
     */
    @NotBlank
    @ApiModelProperty(value = "数据结束时间")
    private String dataEndTime;
}