<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.metadata.dao.MetadataTaskInstanceMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.metadata.entity.MetadataTaskInstance">
        <!--@mbg.generated-->
        <!--@Table DQM_METADATA_TASK_INSTANCE-->
        <id column="TASK_INSTANCE_ID" jdbcType="INTEGER" property="taskInstanceId"/>
        <id column="DATA_SOURCE_ID" jdbcType="INTEGER" property="dataSourceId"/>
        <result column="TASK_GROUP_ID" jdbcType="VARCHAR" property="taskGroupId"/>
        <result column="TASK_STATE" jdbcType="VARCHAR" property="taskState"/>
        <result column="TASK_START_DT" jdbcType="TIMESTAMP" property="taskStartDt"/>
        <result column="TASK_END_DT" jdbcType="TIMESTAMP" property="taskEndDt"/>
        <result column="TASK_RESULT" jdbcType="LONGVARCHAR" property="taskResult"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        TASK_INSTANCE_ID,
        TASK_GROUP_ID,
        DATA_SOURCE_ID,
        TASK_STATE,
        TASK_START_DT,
        TASK_END_DT,
        TASK_RESULT
    </sql>

    <select id="queryTaskAllocationHisInfo" resultType="com.jykj.dqm.metadata.entity.MetadataTaskInstanceVO">
        SELECT m1.*, m2.DATA_SOURCE_NAME as DATA_SOURCE_NAME, m3.JOB_NAME as TASK_GROUP_NAME
        FROM DQM_METADATA_TASK_INSTANCE m1
                 LEFT JOIN DQM_METADATA_DATASOURCE m2 on m1.DATA_SOURCE_ID = m2.DATA_SOURCE_ID
                 LEFT JOIN DQM_SCHEDULE_JOB_TASK m3 on m1.TASK_GROUP_ID = m3.JOB_ID
        <where>
            <if test="dataSourceId != null">
                AND m1.DATA_SOURCE_ID = #{dataSourceId}
            </if>
            <if test="_databaseId == 'mysql' and dataSourceName != null and dataSourceName != ''">
                AND m2.DATA_SOURCE_NAME like CONCAT('%', #{dataSourceName}, '%')
            </if>
            <if test="_databaseId == 'oracle' and dataSourceName != null and dataSourceName != ''">
                and m2.DATA_SOURCE_NAME like '%'|| #{dataSourceName} ||'%'
            </if>
            <if test="taskState != null and taskState != ''">
                AND m1.TASK_STATE = #{taskState}
            </if>
            <if test="taskEndDt != null">
                AND m1.TASK_END_DT &lt;= #{taskEndDt,jdbcType=TIMESTAMP}
            </if>
            <if test="taskStartDt != null">
                AND m1.TASK_START_DT >= #{taskStartDt,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER By m1.TASK_START_DT DESC
    </select>

    <update id="updateTaskState">
        UPDATE DQM_METADATA_TASK_INSTANCE
        SET TASK_STATE='99'
        WHERE TASK_STATE = '00'
        <if test="_databaseId == 'mysql'">
            AND TASK_START_DT &lt; DATE_SUB(SYSDATE(), INTERVAL 1 hour)
        </if>
        <if test="_databaseId == 'oracle'">
            AND TASK_START_DT &lt; SYSDATE - INTERVAL '1' HOUR
        </if>
    </update>
</mapper>