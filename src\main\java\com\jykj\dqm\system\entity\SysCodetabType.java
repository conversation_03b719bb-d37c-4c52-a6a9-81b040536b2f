package com.jykj.dqm.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统码值类型
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:46:51
 */
@ApiModel(value = "系统码值类型")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "SYS_CODETAB_TYPE")
public class SysCodetabType implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 类型编码
     */
    @TableField(value = "TYPE_CODE")
    @ApiModelProperty(value = "类型编码")
    private String typeCode;

    /**
     * 类型名称
     */
    @TableField(value = "TYPE_NAME")
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 描述
     */
    @TableField(value = "TYPE_DESC")
    @ApiModelProperty(value = "描述")
    private String typeDesc;

    /**
     * 系统编码
     */
    @TableField(value = "SYS_ID")
    @ApiModelProperty(value = "系统编码", hidden = true)
    private String sysId = Constant.SYS_NAME;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "MODIFY_TIME")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    private Date modifyTime;
}