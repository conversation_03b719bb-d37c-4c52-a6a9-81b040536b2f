package com.jykj.dqm.metadata.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.CollectionTaskInfoDTO;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceDTO;

/**
 * 元数据采集任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 14:21:26
 */

public interface MetaDataCollectionService {
    R getScheduleJobInfo(CollectionTaskInfoDTO collectionTaskInfoDto);

    R exeScheduleJob(String taskGroupId);

    R deleteTaskAllocationInfo(String taskGroupId);

    R queryTaskAllocationHisInfo(MetadataTaskInstanceDTO metadataTaskInstance);
}
