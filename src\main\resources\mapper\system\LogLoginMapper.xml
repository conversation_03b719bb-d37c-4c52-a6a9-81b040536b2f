<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.system.dao.LogLoginMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.common.LogLogin">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="USERNAME" jdbcType="VARCHAR" property="username"/>
        <result column="LOGIN_ID" jdbcType="VARCHAR" property="loginId"/>
        <result column="USER_ID" jdbcType="VARCHAR" property="userId"/>
        <result column="IP" jdbcType="VARCHAR" property="ip"/>
        <result column="LOGIN_TIME" jdbcType="TIMESTAMP" property="loginTime"/>
        <result column="SYS_ID" jdbcType="VARCHAR" property="sysId"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID,
        USERNAME,
        LOGIN_ID,
        USER_ID,
        IP,
        LOGIN_TIME,
        SYS_ID
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from LOG_LOGIN
        where ID = #{id,jdbcType=INTEGER}
          AND SYS_ID = 'DQM'
    </select>
    <select id="queryLog" resultType="com.jykj.dqm.common.LogLogin" databaseId="mysql">
        SELECT
        <include refid="Base_Column_List"/>
        from LOG_LOGIN WHERE SYS_ID = 'DQM'
        <if test="startTime != null and startTime != ''">
            AND LOGIN_TIME <![CDATA[ >= ]]> str_to_date(#{startTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null and endTime != ''">
            AND LOGIN_TIME <![CDATA[ <= ]]> str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="loginId != null and loginId != ''">
            AND LOGIN_ID LIKE CONCAT('%', #{loginId}, '%')
        </if>
        <if test="username != null and username != ''">
            AND USERNAME LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="ip != null and ip != ''">
            AND IP LIKE CONCAT('%', #{ip}, '%')
        </if>
        ORDER BY LOGIN_TIME DESC
    </select>

    <select id="queryLog" resultType="com.jykj.dqm.common.LogLogin" databaseId="oracle">
        SELECT
        <include refid="Base_Column_List"/>
        from LOG_LOGIN WHERE SYS_ID = 'DQM'
        <if test="startTime != null and startTime != ''">
            AND LOGIN_TIME <![CDATA[ >= ]]> to_date(#{startTime},'yyyy/mm/dd hh24:mi:ss')
        </if>
        <if test="endTime != null and endTime != ''">
            AND LOGIN_TIME <![CDATA[ <= ]]> to_date(#{endTime},'yyyy/mm/dd hh24:mi:ss')
        </if>
        <if test="loginId != null and loginId != ''">
            AND LOGIN_ID LIKE '%'|| #{loginId} ||'%'
        </if>
        <if test="username != null and username != ''">
            AND USERNAME LIKE '%'|| #{username} ||'%'
        </if>
        <if test="ip != null and ip != ''">
            AND IP LIKE '%'|| #{ip} ||'%'
        </if>
        ORDER BY LOGIN_TIME DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from LOG_LOGIN
        where ID = #{id,jdbcType=INTEGER}
          AND SYS_ID = 'DQM'
    </delete>
    <delete id="clearloginLog">
        delete
        from LOG_LOGIN
        where SYS_ID = 'DQM'
          and LOGIN_TIME <![CDATA[ <= ]]> #{date}
    </delete>
    <insert id="insertSelective" parameterType="com.jykj.dqm.common.LogLogin">
        insert into LOG_LOGIN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ID,
            <if test="username != null">
                USERNAME,
            </if>
            <if test="loginId != null">
                LOGIN_ID,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="ip != null">
                IP,
            </if>
            <if test="loginTime != null">
                LOGIN_TIME,
            </if>
            <if test="sysId != null">
                SYS_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=NUMERIC},
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="loginId != null">
                #{loginId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sysId != null">
                #{sysId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jykj.dqm.common.LogLogin">
        update LOG_LOGIN
        <set>
            <if test="username != null">
                USERNAME = #{username,jdbcType=VARCHAR},
            </if>
            <if test="loginId != null">
                LOGIN_ID = #{loginId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                IP = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="loginTime != null">
                LOGIN_TIME = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sysId != null">
                SYS_ID = #{sysId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
          AND SYS_ID = 'DQM'
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jykj.dqm.common.LogLogin">
        update LOG_LOGIN
        set USERNAME   = #{username,jdbcType=VARCHAR},
            LOGIN_ID   = #{loginId,jdbcType=VARCHAR},
            USER_ID    = #{userId,jdbcType=VARCHAR},
            IP         = #{ip,jdbcType=VARCHAR},
            LOGIN_TIME = #{loginTime,jdbcType=TIMESTAMP},
            SYS_ID     = #{sysId,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER}
          AND SYS_ID = 'DQM'
    </update>
</mapper>