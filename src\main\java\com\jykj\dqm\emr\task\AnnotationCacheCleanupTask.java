package com.jykj.dqm.emr.task;

import com.jykj.dqm.emr.manager.generateword.GenerateChapterWord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 批注信息缓存清理定时任务
 * 每天凌晨2点清理1天前的缓存数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01
 */
@Slf4j
@Component
public class AnnotationCacheCleanupTask {

    /**
     * 每天凌晨2点执行缓存清理任务
     * 清理1天前的批注信息缓存
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredAnnotationCache() {
        try {
            log.info("开始执行批注信息缓存清理任务");
            
            // 获取清理前的缓存统计信息
            Map<String, Object> beforeStats = GenerateChapterWord.getCacheStatistics();
            log.info("清理前缓存统计：{}", beforeStats);
            
            // 清理2天前的缓存数据
            int clearedCount = GenerateChapterWord.clearExpiredAnnotationCache(1);
            
            // 获取清理后的缓存统计信息
            Map<String, Object> afterStats = GenerateChapterWord.getCacheStatistics();
            log.info("清理后缓存统计：{}", afterStats);
            
            log.info("批注信息缓存清理任务完成，清理了{}条过期记录", clearedCount);
            
        } catch (Exception e) {
            log.error("执行批注信息缓存清理任务时发生异常：{}", e.getMessage(), e);
        }
    }

    /**
     * 每小时执行一次缓存统计日志
     * 用于监控缓存使用情况
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void logCacheStatistics() {
        try {
            Map<String, Object> stats = GenerateChapterWord.getCacheStatistics();
            if ((Integer) stats.get("annotationCacheSize") > 0) {
                log.info("批注信息缓存统计：{}", stats);
            }
        } catch (Exception e) {
            log.warn("获取缓存统计信息时发生异常：{}", e.getMessage());
        }
    }

    /**
     * 手动触发缓存清理
     * 可通过接口调用进行手动清理
     *
     * @param daysAgo 清理多少天前的数据
     * @return 清理的记录数量
     */
    public int manualCleanup(int daysAgo) {
        try {
            log.info("手动触发批注信息缓存清理，清理{}天前的数据", daysAgo);
            
            Map<String, Object> beforeStats = GenerateChapterWord.getCacheStatistics();
            log.info("清理前缓存统计：{}", beforeStats);
            
            int clearedCount = GenerateChapterWord.clearExpiredAnnotationCache(daysAgo);
            
            Map<String, Object> afterStats = GenerateChapterWord.getCacheStatistics();
            log.info("清理后缓存统计：{}", afterStats);
            
            log.info("手动缓存清理完成，清理了{}条记录", clearedCount);
            return clearedCount;
            
        } catch (Exception e) {
            log.error("手动清理缓存时发生异常：{}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * 获取当前缓存统计信息
     *
     * @return 缓存统计信息
     */
    public Map<String, Object> getCurrentCacheStatistics() {
        return GenerateChapterWord.getCacheStatistics();
    }
}
