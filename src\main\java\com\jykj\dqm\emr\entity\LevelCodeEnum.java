package com.jykj.dqm.emr.entity;

public enum LevelCodeEnum {
    LEVEL_ONE("1", "一"),
    LEVEL_TWO("2", "二"),
    LEVEL_THREE("3", "三"),
    LEVEL_FOUR("4", "四"),
    LEVEL_FIVE("5", "五"),
    LEVEL_SIX("6", "六"),
    LEVEL_SEVEN("7", "七"),
    LEVEL_EIGHT("8", "八");

    private final String code;
    private final String description;

    LevelCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static LevelCodeEnum fromCode(String code) {
        for (LevelCodeEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        throw new IllegalArgumentException("无效的级别代码: " + code);
    }
}