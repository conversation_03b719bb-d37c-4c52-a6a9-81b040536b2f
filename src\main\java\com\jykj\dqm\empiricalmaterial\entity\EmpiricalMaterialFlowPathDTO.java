package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 实证材料流程和资料
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH
 */
@Data
public class EmpiricalMaterialFlowPathDTO implements Serializable {
    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 评价内容Id
     */
    @ApiModelProperty(value = "评价内容Id")
    private String evaluationContentId;

    private List<EmpiricalMaterialFlowPath> empiricalMaterialFlowPaths;
}
