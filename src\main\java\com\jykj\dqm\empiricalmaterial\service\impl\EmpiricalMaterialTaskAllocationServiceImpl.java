package com.jykj.dqm.empiricalmaterial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.dao.SysUserRoleMapper;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.service.EmrSysUserGroupService;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.empiricalmaterial.dao.EmpiricalMaterialTaskAllocationMapper;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectory;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectoryFirst;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectorySecond;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocation;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocationSaveDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialUserListAndTasksVo;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialDirectoryService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialTaskAllocationService;
import com.jykj.dqm.emr.entity.EmrProjectManager;
import com.jykj.dqm.emr.manager.PermissionLevelCodeUtil;
import com.jykj.dqm.emr.service.EmrProjectManagerService;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StpUtilMy;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_TASK_ALLOCATION(实证材料任务分配)】的数据库操作Service实现
 * @createDate 2024-01-22 16:13:16
 */
@Service
public class EmpiricalMaterialTaskAllocationServiceImpl extends ServiceImpl<EmpiricalMaterialTaskAllocationMapper, EmpiricalMaterialTaskAllocation> implements EmpiricalMaterialTaskAllocationService {

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private EmpiricalMaterialDirectoryService empiricalMaterialDirectoryService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private EmrSysUserGroupService emrSysUserGroupService;

    @Autowired
    private EmrProjectManagerService emrProjectManagerService;

    @Override
    public R save(EmpiricalMaterialTaskAllocationSaveDTO taskAllocationSaveDTO) {
        String userAccount = taskAllocationSaveDTO.getUserAccount();
        List<EmpiricalMaterialTaskAllocation> taskAllocations = taskAllocationSaveDTO.getTaskAllocations();
        List<EmpiricalMaterialTaskAllocation> empiricalMaterialTaskAllocationsDb = this.list(new LambdaQueryWrapper<EmpiricalMaterialTaskAllocation>().eq(EmpiricalMaterialTaskAllocation::getPersonInCharge, userAccount).eq(EmpiricalMaterialTaskAllocation::getProjectId, taskAllocationSaveDTO.getProjectId()).le(StrUtil.isNotBlank(taskAllocationSaveDTO.getLevelCode()), EmpiricalMaterialTaskAllocation::getLevelCode, taskAllocationSaveDTO.getLevelCode()));
        this.remove(new LambdaQueryWrapper<EmpiricalMaterialTaskAllocation>().eq(EmpiricalMaterialTaskAllocation::getPersonInCharge, userAccount).eq(EmpiricalMaterialTaskAllocation::getProjectId, taskAllocationSaveDTO.getProjectId()).le(StrUtil.isNotBlank(taskAllocationSaveDTO.getLevelCode()), EmpiricalMaterialTaskAllocation::getLevelCode, taskAllocationSaveDTO.getLevelCode()));
        if (taskAllocations == null || taskAllocations.size() == 0) {
            return RUtil.success();
        }
        Map<String, EmpiricalMaterialTaskAllocation> empiricalMaterialTaskAllocationMap = empiricalMaterialTaskAllocationsDb.stream().collect(Collectors.toMap(item -> item.getDirectoryCode() + "_" + item.getDirectoryName(), item -> item, (k1, k2) -> k1, LinkedHashMap::new));
        taskAllocations.stream().forEach(item -> {
            item.setPersonInCharge(userAccount);
            if (StrUtil.isNotBlank(item.getDirectoryCode()) && item.getDirectoryCode().length() >= 7) {
                item.setLevelCode(item.getDirectoryCode().substring(item.getDirectoryCode().lastIndexOf(".") + 1, 7));
            }
            EmpiricalMaterialTaskAllocation rulePermissionConfiguration = empiricalMaterialTaskAllocationMap.get(item.getDirectoryCode() + "_" + item.getDirectoryName());
            if (rulePermissionConfiguration != null) {
                item.setTaskStatus(rulePermissionConfiguration.getTaskStatus());
            } else {
                //已分配
                item.setTaskStatus("1");
            }
            item.setProjectId(taskAllocationSaveDTO.getProjectId());
        });
        this.saveBatch(taskAllocations);
        return RUtil.success();
    }

    @Override
    public EmpiricalMaterialTaskAllocationSaveDTO getTask(String userAccount, String levelCode, String projectId) {
        //boolean needUserAccount = isNeedUserAccountNew(userAccount, projectId);
        List<EmpiricalMaterialTaskAllocation> list = this.list(new LambdaQueryWrapper<EmpiricalMaterialTaskAllocation>()
                //.eq(needUserAccount, EmpiricalMaterialTaskAllocation::getPersonInCharge, userAccount)
                .eq(StrUtil.isNotBlank(userAccount), EmpiricalMaterialTaskAllocation::getPersonInCharge, userAccount).eq(EmpiricalMaterialTaskAllocation::getProjectId, projectId).le(EmpiricalMaterialTaskAllocation::getLevelCode, levelCode).orderByAsc(EmpiricalMaterialTaskAllocation::getDirectoryCode));
        EmpiricalMaterialTaskAllocationSaveDTO empiricalMaterialTaskAllocationSaveDTO = EmpiricalMaterialTaskAllocationSaveDTO.builder().userAccount(userAccount).projectId(projectId).build();
        if (list == null || list.size() == 0) {
            return empiricalMaterialTaskAllocationSaveDTO;
        }
        empiricalMaterialTaskAllocationSaveDTO.setTaskAllocations(list);
        return empiricalMaterialTaskAllocationSaveDTO;
    }

    private boolean isNeedUserAccount(String userAccount) {
        boolean needUserAccount = true;
        if (StrUtil.isBlank(userAccount)) {
            return false;
        }
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("emr.admin.role");
        if (sysConfigByName != null && StrUtil.isNotBlank(sysConfigByName.getConfigValue())) {
            String[] admins = sysConfigByName.getConfigValue().split(",");
            SysUser info = sysUserMapper.getUserInfoByLoginId(userAccount);
            if (info == null) {
                throw new BusinessException("用户不存在！");
            }
            List<SysRole> sysRoles = sysUserRoleMapper.getAllRoleInfoList(info.getUserId() + "");
            List<SysRole> roleNames = sysRoles.stream().filter(item -> Arrays.asList(admins).contains(item.getRoleName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(roleNames)) {
                needUserAccount = false;
            }
        }
        return needUserAccount;
    }

    private boolean isNeedUserAccountNew(String userAccount, String projectId) {
        boolean needUserAccount = true;
        if (StrUtil.isBlank(userAccount) || StrUtil.isBlank(projectId)) {
            return false;
        }
        EmrProjectManager projectManager = emrProjectManagerService.getById(projectId);
        if (projectManager == null) {
            return false;
        }
        String personInCharge = projectManager.getPersonInCharge();
        //数据库中personInCharge字段用逗号分隔
        //String[] personInChargeArr = personInCharge.split(",");
        //List<String> personInChargeList = Arrays.asList(personInChargeArr);
        //数据库中personInCharge字段用json数组
        List<String> personInChargeList = JSONObject.parseArray(personInCharge, String.class);
        //判断personInChargeArr是否包含当前登录用户
        if (personInChargeList.contains(userAccount)) {
            needUserAccount = false;
        }
        return needUserAccount;
    }

    @Override
    public R getUserListAndTasks(String levelCode, String userName, String userAccount, String projectId) {
        List<EmrSysUserGroup> emrSysUserGroups = emrSysUserGroupService.queryByGroupName("实证材料", userAccount, projectId);

        List<EmpiricalMaterialUserListAndTasksVo> list = new ArrayList<>();
        EmpiricalMaterialUserListAndTasksVo empiricalMaterialUserListAndTasksVo;

        List<EmpiricalMaterialTaskAllocation> empiricalMaterialTaskAllocations = this.list(
                new LambdaQueryWrapper<EmpiricalMaterialTaskAllocation>()
                        .le(StrUtil.isNotBlank(levelCode), EmpiricalMaterialTaskAllocation::getLevelCode, levelCode)
                        .eq(StrUtil.isNotBlank(projectId), EmpiricalMaterialTaskAllocation::getProjectId, projectId)
                        .orderByAsc(EmpiricalMaterialTaskAllocation::getDirectoryCode)
        );
        Map<String, List<EmpiricalMaterialTaskAllocation>> listMap = empiricalMaterialTaskAllocations.stream().collect(Collectors.groupingBy(EmpiricalMaterialTaskAllocation::getPersonInCharge));
        for (EmrSysUserGroup emrSysUserGroup : emrSysUserGroups) {
            userAccount = emrSysUserGroup.getLoginId();
            //已分配+已完成+级别
            empiricalMaterialUserListAndTasksVo = new EmpiricalMaterialUserListAndTasksVo();
            empiricalMaterialUserListAndTasksVo.setUserAccount(userAccount);
            empiricalMaterialUserListAndTasksVo.setUserName(emrSysUserGroup.getUserName());
            List<EmpiricalMaterialTaskAllocation> empiricalMaterialTaskAllocations1 = listMap.get(userAccount);
            if (empiricalMaterialTaskAllocations1 != null && empiricalMaterialTaskAllocations1.size() > 0) {
                // 按照级别分组，并计算每个levelCode级别的taskStatus（1-已分配，2-已完成）数量
                // 首先根据levelCode分组，然后对每个组进行任务状态统计
                Map<String, Map<String, Long>> taskStatusCountByLevelCode = empiricalMaterialTaskAllocations1.stream().collect(Collectors.groupingBy(EmpiricalMaterialTaskAllocation::getLevelCode, Collectors.groupingBy(EmpiricalMaterialTaskAllocation::getTaskStatus, Collectors.counting())));
                // 添加前缀并创建新的Map
                Map<String, Map<String, Long>> resultMap = taskStatusCountByLevelCode.entrySet().stream().collect(Collectors.toMap(entry -> "level" + entry.getKey(), entry -> entry.getValue().entrySet().stream().collect(Collectors.toMap(innerEntry -> "taskStatus" + innerEntry.getKey(), Map.Entry::getValue))));
                empiricalMaterialUserListAndTasksVo.setTaskAllocationMap(resultMap);
                //已分配数量
                long allocatedNum = 0;
                //已完成数量
                long finishedNum = 0;
                for (Map.Entry<String, Map<String, Long>> entry : taskStatusCountByLevelCode.entrySet()) {
                    allocatedNum += entry.getValue().get("1") == null ? 0 : entry.getValue().get("1");
                    finishedNum += entry.getValue().get("2") == null ? 0 : entry.getValue().get("2");
                }
                empiricalMaterialUserListAndTasksVo.setAllocatedNum(allocatedNum + finishedNum);
                empiricalMaterialUserListAndTasksVo.setFinishedNum(finishedNum);
            }
            list.add(empiricalMaterialUserListAndTasksVo);
        }

        long count = empiricalMaterialDirectoryService.count(
                Wrappers.<EmpiricalMaterialDirectory>lambdaQuery()
                        .le(StrUtil.isNotBlank(levelCode), EmpiricalMaterialDirectory::getLevelCode, levelCode)
                        .apply("length(DIRECTORY_CODE) >= {0}", "7")
        );
        Map<String, Object> result = new HashMap<>();
        result.put("allNum", count);
        result.put("empiricalmaterial", list);
        return RUtil.success(result);
    }

    @Override
    public R queryDirectoryTree(String userAccount, String levelCode, String evaluationCategory, boolean needNotAllocationTask, String projectId) {
        Integer allowLevelCode = PermissionLevelCodeUtil.getLevelCode();
        if (allowLevelCode < Integer.parseInt(levelCode)) {
            throw new BusinessException("权限不足，只能操作" + allowLevelCode + "级文档！");
        }
        LambdaQueryWrapper<EmpiricalMaterialDirectory> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.le(EmpiricalMaterialDirectory::getLevelCode, levelCode);
        //添加基本和选择项条件
        if (StrUtil.isNotBlank(evaluationCategory)) {
            lambdaQueryWrapper.and(item -> item.apply("length(DIRECTORY_CODE) <= {0}", "6").or().eq(EmpiricalMaterialDirectory::getEvaluationCategory, evaluationCategory));
        }

        lambdaQueryWrapper.orderByAsc(EmpiricalMaterialDirectory::getDirectoryCode);
        List<EmpiricalMaterialDirectory> list = empiricalMaterialDirectoryService.list(lambdaQueryWrapper);
        list = list.stream().filter(item -> item.getDirectoryCode().length() <= 6 || (StrUtil.isNotBlank(item.getLevelCode()) && item.getLevelCode().equals(levelCode))).collect(Collectors.toList());

        EmpiricalMaterialTaskAllocationSaveDTO rulePermission = getTask("", levelCode, projectId);
        List<EmpiricalMaterialTaskAllocation> rulePermissionList = rulePermission.getTaskAllocations() == null ? new ArrayList<>() : rulePermission.getTaskAllocations();

        List<EmpiricalMaterialTaskAllocation> userAccountOnerlist = rulePermissionList.stream().filter(item -> item.getPersonInCharge().equals(userAccount)).collect(Collectors.toList());

        Set<String> collect;
        if (StrUtil.isNotBlank(userAccount)) {
            collect = userAccountOnerlist.stream().map(item -> item.getDirectoryCode() + item.getDirectoryName()).collect(Collectors.toSet());
        } else {
            //最后的导出目录
            collect = rulePermissionList.stream().map(item -> item.getDirectoryCode() + item.getDirectoryName()).collect(Collectors.toSet());
        }
        Set<String> filterSet = collect;
        List<EmpiricalMaterialDirectory> listFilter = list.stream().filter(item -> item.getDirectoryCode().length() < 7 || filterSet.contains(item.getDirectoryCode() + item.getDirectoryName())).collect(Collectors.toList());

        //添加未匹配的
        if (needNotAllocationTask) {
            //未匹配的
            Set<String> directoryList = rulePermissionList.stream().map(item -> item.getDirectoryCode() + "_" + item.getDirectoryName()).collect(Collectors.toSet());
            List<EmpiricalMaterialDirectory> notAllocationList = list.stream().filter(item -> StrUtil.isBlank(evaluationCategory) || item.getEvaluationCategory().equals(evaluationCategory)).filter(item -> !directoryList.contains(item.getDirectoryCode() + "_" + item.getDirectoryName()) && item.getDirectoryCode().length() >= 7).filter(item -> item.getDirectoryCode().length() >= 7).collect(Collectors.toList());
            listFilter.addAll(notAllocationList);
        }

        //list去重排序
        listFilter = listFilter.stream().distinct().sorted(Comparator.comparing(EmpiricalMaterialDirectory::getDirectoryCode)).collect(Collectors.toList());

        Map<String, List<EmpiricalMaterialTaskAllocation>> listMap = (rulePermissionList == null ? new ArrayList<EmpiricalMaterialTaskAllocation>() : rulePermissionList).stream().collect(Collectors.groupingBy(task -> task.getDirectoryCode() + "_" + task.getDirectoryName()));
        List<EmpiricalMaterialDirectory> firstLevelList = listFilter.stream().filter(directoryConfiguration -> directoryConfiguration.getDirectoryCode().length() <= 2).collect(Collectors.toList());
        List<EmpiricalMaterialDirectoryFirst> documentDirectoryFirstList = new ArrayList<>();
        EmpiricalMaterialDirectoryFirst documentDirectoryFirst;
        List<EmpiricalMaterialDirectorySecond> secondLevels;
        EmpiricalMaterialDirectorySecond documentDirectorySecond;
        Map<String, List<EmpiricalMaterialDirectory>> map = new LinkedHashMap<>();

        Map<String, String> resultMap = getEmrTaskUserMap();
        List<EmpiricalMaterialTaskAllocation> empiricalMaterialTaskAllocations;
        String personInCharge;
        for (EmpiricalMaterialDirectory directoryConfiguration : firstLevelList) {
            documentDirectoryFirst = new EmpiricalMaterialDirectoryFirst();
            String directoryCode = directoryConfiguration.getDirectoryCode();
            documentDirectoryFirst.setDirectoryCode(directoryCode);
            documentDirectoryFirst.setDirectoryName(directoryConfiguration.getDirectoryName());
            documentDirectoryFirst.setSerialNum(directoryConfiguration.getSerialNum());
            secondLevels = new ArrayList<>();
            for (EmpiricalMaterialDirectory item : listFilter) {
                if (!item.getDirectoryCode().equals(directoryCode) && item.getDirectoryCode().startsWith(directoryCode)) {
                    if (item.getDirectoryCode().length() <= 5) {
                        documentDirectorySecond = new EmpiricalMaterialDirectorySecond();
                        documentDirectorySecond.setDirectoryCode(item.getDirectoryCode());
                        documentDirectorySecond.setDirectoryName(item.getDirectoryName());
                        documentDirectorySecond.setSerialNum(item.getSerialNum());
                        documentDirectorySecond.setBusinessProject(item.getBusinessProject());
                        documentDirectorySecond.setProjectNum(item.getProjectNum());
                        //documentDirectoryFirst.setBusinessProject(item.getBusinessProject());
                        secondLevels.add(documentDirectorySecond);
                    } else {
                        //01.03.3
                        item.setTaskStatus("0");
                        empiricalMaterialTaskAllocations = listMap.get(item.getDirectoryCode() + "_" + item.getDirectoryName());
                        if (empiricalMaterialTaskAllocations != null && empiricalMaterialTaskAllocations.get(0) != null) {
                            personInCharge = empiricalMaterialTaskAllocations.get(0).getPersonInCharge();
                            String taskStatus = empiricalMaterialTaskAllocations.get(0).getTaskStatus();
                            item.setTaskStatus(StrUtil.isBlank(taskStatus) ? "0" : taskStatus);
                            item.setPersonInCharge(resultMap.get(personInCharge) + "(" + personInCharge + ")");
                        }
                        String secondLevelKey = item.getDirectoryCode().substring(0, item.getDirectoryCode().lastIndexOf("."));
                        List<EmpiricalMaterialDirectory> structures = map.computeIfAbsent(secondLevelKey, k -> new LinkedList<>());
                        structures.add(item);
                    }
                }
            }
            documentDirectoryFirst.setSecondLevels(secondLevels);
            documentDirectoryFirstList.add(documentDirectoryFirst);
        }

        for (EmpiricalMaterialDirectoryFirst directoryFirst : documentDirectoryFirstList) {
            for (EmpiricalMaterialDirectorySecond secondLevel : directoryFirst.getSecondLevels()) {
                secondLevel.setThirdLevels(map.getOrDefault(secondLevel.getDirectoryCode(), new ArrayList<>()));
            }
        }

        //过滤没有三级菜单的一二级目录
        //java8实现
        List<EmpiricalMaterialDirectoryFirst> result = documentDirectoryFirstList.stream().filter(directoryFirst -> {
            List<EmpiricalMaterialDirectorySecond> directorySecondList = directoryFirst.getSecondLevels().stream().filter(secondLevel -> !secondLevel.getThirdLevels().isEmpty()).collect(Collectors.toList());
            directoryFirst.setSecondLevels(directorySecondList);
            return !directorySecondList.isEmpty();
        }).collect(Collectors.toList());
        return RUtil.success(result);

    }

    @NotNull
    private Map<String, String> getUserMap() {
        List<Map<String, Object>> mapList = sysUserMapper.selectMaps(new LambdaQueryWrapper<SysUser>().select(SysUser::getLoginId, SysUser::getUserName).eq(SysUser::getSysId, Constant.SYS_NAME));
        Map<String, String> resultMap = mapList.stream().collect(Collectors.toMap(record -> (String) record.get("LOGIN_ID"), record -> (String) record.get("USER_NAME"), (oldValue, newValue) -> oldValue));
        return resultMap;
    }

    private Map<String, String> getEmrTaskUserMap() {
        List<EmrSysUserGroup> mapList = emrSysUserGroupService.queryByGroupName(null, null, null);
        Map<String, String> resultMap = mapList.stream().collect(Collectors.toMap(record -> record.getLoginId(), record -> record.getUserName(), (oldValue, newValue) -> oldValue));
        return resultMap;
    }
}
