package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.emr.entity.UserAndTimeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 实证材料导出记录
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_EXPORT_RECORD
 */
@TableName(value = "DQM_EMR_EMPIRICAL_MATERIAL_EXPORT_RECORD")
@Accessors(chain = true)
@Data
public class EmpiricalMaterialExportRecord extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    @TableField(value = "PROJECT_ID")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 导出文档等级
     */
    @ApiModelProperty(value = "导出文档等级")
    private String exportDocumentLevel;

    /**
     * 导出文档模板个数
     */
    @ApiModelProperty(value = "导出文档模板个数")
    private Integer exportDocumentTemplatesNum = 0;

    /**
     * 数据开始时间
     */
    @ApiModelProperty(value = "数据开始时间")
    private String dataStartTime;

    /**
     * 数据结束时间
     */
    @ApiModelProperty(value = "数据结束时间")
    private String dataEndTime;

    /**
     * 导出状态：0、未执行；1、执行中；2、完成;3、异常
     */
    @ApiModelProperty(value = "导出状态：0、未执行；1、执行中；2、成功;3、异常")
    private String exportStatus;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failReason;

    /**
     * 定时导出时间
     */
    @ApiModelProperty(value = "定时导出时间")
    private LocalDateTime timedExportTime;

}
