package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.LevelDictionary;
import com.jykj.dqm.emr.entity.LevelDictionaryQuery;
import com.jykj.dqm.emr.service.LevelDictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 等级字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 9:57:08
 */
@Api(tags = {"等级字典"})
@RestController
@RequestMapping("/emr/levelDictionary")
public class LevelDictionaryController {
    /**
     * 服务对象
     */
    @Resource
    private LevelDictionaryService levelDictionaryService;

    @LogRemark(operate = "新增等级字典", module = "等级字典配置")
    @ApiOperation(value = "新增等级字典", notes = "等级字典配置")
    @PostMapping("/add")
    public R add(@Validated @RequestBody LevelDictionary levelDictionary) {
        return levelDictionaryService.add(levelDictionary);
    }

    @LogRemark(operate = "更新等级字典", module = "等级字典配置")
    @ApiOperation(value = "更新等级字典", notes = "等级字典配置")
    @PostMapping("/update")
    public R update(@Validated @RequestBody LevelDictionary levelDictionary) {
        return levelDictionaryService.update(levelDictionary);
    }

    @LogRemark(operate = "删除等级字典", module = "等级字典配置")
    @ApiOperation(value = "删除等级字典", notes = "等级字典配置")
    @DeleteMapping("/delete")
    public R delete(@RequestParam("ids") List<Long> ids) {
        return levelDictionaryService.delete(ids);
    }

    @ApiOperation(value = "查询等级字典", notes = "等级字典配置")
    @PostMapping("/query")
    public R query(@RequestBody LevelDictionaryQuery levelDictionaryQuery) {
        return levelDictionaryService.query(levelDictionaryQuery);
    }

    @ApiOperation(value = "查询允许的等级字典", notes = "等级字典配置")
    @PostMapping("/queryAllow")
    public R queryAllow(@RequestBody LevelDictionaryQuery levelDictionaryQuery) {
        return levelDictionaryService.queryAllow(levelDictionaryQuery);
    }
}
