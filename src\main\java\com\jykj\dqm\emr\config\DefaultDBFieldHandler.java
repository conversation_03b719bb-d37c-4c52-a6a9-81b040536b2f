package com.jykj.dqm.emr.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.jykj.dqm.utils.StpUtilMy;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 */
@Slf4j
@Component
public class DefaultDBFieldHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        Date date = new Date();
        //某些异步场景无法获取session中的用户信息，需要自己组装
        Object username = this.getFieldValByName("createBy", metaObject);
        if (username == null) {
            username = StpUtilMy.getUserName();
        }
        this.setFieldValByName("createTime", date, metaObject);
        this.setFieldValByName("createBy", username, metaObject);
        this.setFieldValByName("updateTime", date, metaObject);
        this.setFieldValByName("updateBy", username, metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        //某些异步场景无法获取session中的用户信息，需要自己组装
        Object username = this.getFieldValByName("updateBy", metaObject);
        if (username == null) {
            username = StpUtilMy.getUserName();
        }
        this.setFieldValByName("updateBy", username, metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);

    }
}