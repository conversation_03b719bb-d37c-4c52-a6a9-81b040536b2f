package com.jykj.dqm.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataDatasourceQueryDTO;
import com.jykj.dqm.metadata.entity.MetadataDatasourceDeleteDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据源配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:35:34
 */
public interface MetadataDatasourceService extends IService<MetadataDatasource> {
    R selectMetadataDatasourceList(MetadataDatasourceQueryDTO dqmMetadataDatasource);

    R addDatasource(MetadataDatasource dqmMetadataDatasource);

    R updateDatasource(MetadataDatasource dqmMetadataDatasource);

    R deleteDatasource(MetadataDatasourceDeleteDTO dqmMetadataDatasource);

    R testDb(MetadataDatasource dqmMetadataDatasource);

    R uploadDriverFile(MultipartFile file);
}

