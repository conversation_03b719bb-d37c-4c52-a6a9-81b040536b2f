package com.jykj.dqm.auth.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 修改密码SysUser
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/20 14:13
 */
@ApiModel(value = "修改密码SysUser")
@Data
public class SysUserDTO implements Serializable {
    private static final long serialVersionUID = -2347332553394673100L;
    /**
     * 用户id
     */
    @NotBlank
    @ApiModelProperty(value = "用户id", required = true)
    private Integer userId;

    /**
     * 登陆用户id
     */
    @ApiModelProperty(value = "登陆用户id")
    private String loginId;

    /**
     * 密码
     */
    @NotBlank
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    /**
     * 密码
     */
    @NotBlank
    @ApiModelProperty(value = "旧密码", required = true)
    private String oldPassword;
}
