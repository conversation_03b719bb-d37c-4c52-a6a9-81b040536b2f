package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文档规则配置SQL执行结果数据
 *
 * @TableName DQM_EMR_DOCUMENT_RULE_SQL_EXEC_RECORD
 */
@TableName(value = "DQM_EMR_DOCUMENT_RULE_SQL_EXEC_RECORD")
@Data
public class DocumentRuleSqlExecRecord implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * DQM_EMR_DOCUMENT_EXPORT_RECORD表ID
     */
    @ApiModelProperty(value = "DQM_EMR_DOCUMENT_EXPORT_RECORD表ID")
    private String exportRecordId;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;


    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    /**
     * 要求项目
     */
    @ApiModelProperty(value = "要求项目")
    private String requiredProject;

    /**
     * 记录数
     */
    @ApiModelProperty(value = "记录数")
    private Integer recordsNum;

    /**
     * 满足条件记录数（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）
     */
    @ApiModelProperty(value = "满足条件记录数（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）")
    private Integer conditionalRecordsNum;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failureReason;

    /**
     * 执行状态，0成功，1失败
     */
    @ApiModelProperty(value = "执行状态，0成功，1失败")
    private String execStatus = "0";

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String execTime;
}
