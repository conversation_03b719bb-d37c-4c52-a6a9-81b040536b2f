package com.jykj.dqm.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.auth.entity.PermissionVo;
import com.jykj.dqm.auth.entity.SysPermission;

import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public interface SysPermissionService extends IService<SysPermission> {
    /**
     * 新增权限
     *
     * @param sysPermission SysPermission
     * @return Result
     */
    R add(SysPermission sysPermission);

    /**
     * 查询所有权限
     *
     * @return Result
     */
    R queryList();

    /**
     * 根据token获取权限
     *
     * @param token token
     * @return Result
     */
    R getPermission(String token);

    /**
     * 根据角色Id获取权限
     *
     * @param roleId 角色Id
     * @return List<PermissionVo>
     */
    List<PermissionVo> getPermissionByRoleId(String roleId);
}
