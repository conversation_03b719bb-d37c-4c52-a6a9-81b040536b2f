package com.jykj.dqm.metadata.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.MetaDataCollectionMapper;
import com.jykj.dqm.metadata.dao.MetadataTaskInstanceMapper;
import com.jykj.dqm.metadata.entity.CollectionTaskInfoDTO;
import com.jykj.dqm.metadata.entity.MGTaskDto;
import com.jykj.dqm.metadata.entity.MetadataTaskInstance;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceDTO;
import com.jykj.dqm.metadata.service.MetaDataCollectionService;
import com.jykj.dqm.metadata.service.MetadataTaskInstanceService;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.scheduler.QuartzManager;
import com.jykj.dqm.quartz.scheduler.TaskExeUtils;
import com.jykj.dqm.quartz.service.impl.ScheduleJobTaskService;
import com.jykj.dqm.utils.PageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 元数据采集任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 14:21:26
 */
@Service
public class MetaDataCollectionServiceImpl extends ServiceImpl<MetaDataCollectionMapper, ScheduleJobInfo> implements MetaDataCollectionService {
    @Autowired
    private MetaDataCollectionMapper metaDataCollectionDao;

    @Autowired
    private QuartzManager quartzManager;

    @Autowired
    private ScheduleJobTaskService scheduleJobTaskService;

    @Autowired
    private MetadataTaskInstanceService metadataTaskInstanceService;

    @Autowired
    private MetadataTaskInstanceMapper metadataTaskInstanceMapper;

    @Override
    public R getScheduleJobInfo(CollectionTaskInfoDTO collectionTaskInfoDto) {
        PageHelper.startPage(collectionTaskInfoDto.getPageNum(), collectionTaskInfoDto.getPageSize());
        List<ScheduleJobInfo> collectionTaskInfos = metaDataCollectionDao.getScheduleJobInfo(collectionTaskInfoDto);
        PageInfo<ScheduleJobInfo> pageInfo = new PageInfo<>(collectionTaskInfos);
        //方式一：针对属性名称不相同的
        // PageInfo<MGTaskDto> mgTaskDtoPageInfo = PageUtil.pageInfoCopy(pageInfo, MGTaskDto.class, resp1 -> {
        //     //使用orika复制工具将A集合复制到B集合中
        //     MGTaskDto map = MapperUtils.INSTANCE.map(MGTaskDto.class, resp1);
        //     return map;
        // });
        //方式二：针对相同属性名相同的
        PageInfo<MGTaskDto> mgTaskDtoPageInfo = PageUtil.pageInfoCopy(pageInfo, MGTaskDto.class);
        return RUtil.success(mgTaskDtoPageInfo);
    }

    @Override
    public R exeScheduleJob(String taskGroupId) {
        ScheduleJobInfo scheduleJobInfo = this.getOne(new QueryWrapper<ScheduleJobInfo>().eq("JOB_ID", taskGroupId));
        TaskExeUtils.processTask(scheduleJobInfo);
        return RUtil.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteTaskAllocationInfo(String taskGroupId) {
        ScheduleJobInfo scheduleJobInfo = this.getOne(new QueryWrapper<ScheduleJobInfo>().eq("JOB_ID", taskGroupId));
        //看当前任务组是否有任务在执行
        //Map<String, Object> params = new HashMap<>();
        //params.put("TASK_GROUP_ID", taskGroupId);
        //params.put("TASK_STATE", "00");
        //List<MetadataTaskInstance> metadataTaskInstances = metadataTaskInstanceMapper.selectByMap(params);
        //if (CollectionUtil.isNotEmpty(metadataTaskInstances)) {
        //    return ResultUtil.error("任务正在执行中,无法删除！");
        //}
        //删除定时执行的JOB 调用quartzManager
        ScheduleJobInfo job = new ScheduleJobInfo();
        job.setJobId(scheduleJobInfo.getJobId());
        job.setTaskType("MG");
        job.setJobGroup(scheduleJobInfo.getJobGroup());
        quartzManager.removeJob(job);
        //删除任务 DQM_SCHEDULE_JOB_TASK
        scheduleJobTaskService.deleteScheduleJobTask(scheduleJobInfo);
        //删除DQM_SCHEDULE_JOB_TASK_LOG
        scheduleJobTaskService.deleteScheduleJobTaskLog(scheduleJobInfo);
        //删除DQM_METADATA_TASK_INSTANCE
        metadataTaskInstanceService.remove(new QueryWrapper<MetadataTaskInstance>().eq("TASK_GROUP_ID", scheduleJobInfo.getJobId()));
        return RUtil.success("删除成功！");
    }

    @Override
    public R queryTaskAllocationHisInfo(MetadataTaskInstanceDTO metadataTaskInstanceDTO) {
        if (metadataTaskInstanceDTO.getTaskEndDt() != null) {
            metadataTaskInstanceDTO.setTaskEndDt(DateUtil.offsetDay(metadataTaskInstanceDTO.getTaskEndDt(), 1));
        }
        R result = metadataTaskInstanceService.queryTaskAllocationHisInfo(metadataTaskInstanceDTO);
        return result;
    }
}
