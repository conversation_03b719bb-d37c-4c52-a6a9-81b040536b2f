package com.jykj.dqm.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.quality.entity.DataqulityQstnDetail;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据质量问题检核的错误详情记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 10:21:16
 */
@Mapper
public interface DataqulityQstnDetailMapper extends BaseMapper<DataqulityQstnDetail> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DataqulityQstnDetail record);
}