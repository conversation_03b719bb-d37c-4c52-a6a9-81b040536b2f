/*
 * Copyright (c) 2021-2022 蓬安县妇幼 All Rights Reserved.
 */

package com.jykj.dqm.auth.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/20 14:13
 */
@ApiModel(value = "登录类")
@Data
public class LogginForm {
    /**
     * 登陆用户id
     */
    @ApiModelProperty(value = "登陆用户id", required = true)
    private String loginId;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String verificationCode;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phoneNumber;

    /**
     * 验证方式（0：帐密；1：验证码 ；2：账密+验证码；3：企业微信） 默认0密码
     */
    @ApiModelProperty(value = "验证方式", required = true)
    private String identifyType = "0";

    /**
     * redis缓存验证码唯一标识
     */
    @ApiModelProperty(value = "redis缓存验证码唯一标识", required = true)
    private String uuid;

    /**
     * 0:首次认证；1：二次认证
     */
    @ApiModelProperty(value = "0:首次认证；1：二次认证", required = true)
    private String authenticationType = "0";
}
