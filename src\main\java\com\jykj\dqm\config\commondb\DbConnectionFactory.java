package com.jykj.dqm.config.commondb;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.utils.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.KeyedPooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 数据库连接池工厂类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/23 9:18:14
 */
@Slf4j
public class DbConnectionFactory implements KeyedPooledObjectFactory<DbConfig, Connection> {

    @Override
    public PooledObject<Connection> makeObject(DbConfig config) throws Exception {
        // 创建连接
        DbConnectionService dbConnection = new DbConnectionService();
        Connection connection = dbConnection.getConnection(config.getDatabaseUrl(),
                config.getDatabaseUser(), config.getDatabasePwd(), config.getDriverFiles(), config.getDatabaseDriver());
        log.info(config.getDatabaseUrl() + "数据库连接创建成功！");
        return new DefaultPooledObject<>(connection);
    }

    @Override
    public void destroyObject(DbConfig key, PooledObject<Connection> p) {
        Connection conn = p.getObject();
        try {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (Exception e) {
            log.error("关闭数据库连接失败！" + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateObject(DbConfig key, PooledObject<Connection> p) {
        Connection conn = p.getObject();
        Statement statement = null;
        try {
            if (conn != null && !conn.isClosed()) {
                String testSql;
                if (StrUtil.isNotBlank(key.getTestsql())) {
                    testSql = key.getTestsql();
                } else {
                    testSql = "ORACLE".equalsIgnoreCase(key.getDatabaseType()) ? "SELECT 1 FROM DUAL" : "SELECT 1";
                }
                statement = conn.createStatement();
                statement.setQueryTimeout(5);
                ResultSet rs = statement.executeQuery(testSql);
                if (rs.next()) {
                    return true;
                }
            }
        } catch (Throwable e) {
            log.error("验证数据库连接失败！" + e.getMessage(), e);
        } finally {
            IoUtils.close(statement);
        }
        return false;
    }

    /*
    activateObject方法的作用是重新初始化对象实例，在对象从对象池中借出之前确保其处于合适的状态。
    这个方法是由对象池调用的，它通常用于恢复对象到适合客户端使用的状态。这可能包括重置状态信息、清理暂存数据、重新开启暂时关闭的资源等操作。
    当一个对象将要被客户端使用时，activateObject方法负责确保对象处于一个干净且预期的状态，这样可以避免客户端获取到一个包含旧状态或数据遗留的对象实例。
    例如，在数据库连接池中，activateObject方法可能用于确保一个数据库连接是打开的，并验证连接没有失效。
    如果对象无法被成功激活（例如，如果一个数据库连接已经不再有效），那么方法可能抛出一个异常。
     */
    @Override
    public void activateObject(DbConfig key, PooledObject<Connection> pooledObject) {

    }

    /*
    作用: 此方法用于在对象将要被返回到池中空闲对象列表之前对其进行反初始化。
    反初始化意味着清除资源、重置状态、关闭文件句柄等，使对象恢复到一个安全的状态，从而可以安全地被池管理并且在未来重新被借用。
     */
    @Override
    public void passivateObject(DbConfig key, PooledObject<Connection> pooledObject) {

    }
}
