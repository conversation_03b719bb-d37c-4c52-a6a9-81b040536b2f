package com.jykj.dqm.config.commondb;

import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;

import java.sql.Connection;

/**
 * 数据库连接池参数配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/23 9:19:24
 */
public class DbPoolConfig extends GenericKeyedObjectPoolConfig<Connection> {

    /**
     * 连接池每个key最大实例化连接数
     */
    private final static int TOTAL_PER_KEY = 20;

    /**
     * 连接池每个key最大的空闲连接数d
     */
    private final static int MAX_IDLE_PER_KEY = 8;

    /**
     * 连接池每个key最小的空闲连接数
     */
    private final static int MIN_IDLE_PER_KEY = 2;

    /**
     * 获取对象最大等待超时时间，单位是毫秒
     */
    private final static long MAI_WAIT_MILLIS = 10000;

    /**
     * 初始化数量
     */
    public final static long INIT_NUM = 2;

    public DbPoolConfig() {
        setMaxTotalPerKey(TOTAL_PER_KEY);
        setMaxIdlePerKey(MAX_IDLE_PER_KEY);
        setMinIdlePerKey(MIN_IDLE_PER_KEY);
        setMaxWaitMillis(MAI_WAIT_MILLIS);
        // 在从对象池获取对象时是否检测对象有效，true是；默认值是false
        setTestOnBorrow(true);
        // 在向对象池中归还对象时是否检测对象有效，true是，默认值是false
        setTestOnReturn(true);
    }

}

