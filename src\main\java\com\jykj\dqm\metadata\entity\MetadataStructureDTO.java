package com.jykj.dqm.metadata.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 元数据结构详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 16:17:20
 */
@ApiModel(value = "元数据结构(表/视图)详情查询")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataStructureDTO {
    /**
     * 数据源ID
     */
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String databaseName;

    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    private String sysCode;

    /**
     * 系统名称
     */
    @ApiModelProperty(value = "系统名称")
    private String sysName;

    /**
     * SCHEMA
     */
    @ApiModelProperty(value = "SCHEMA")
    private String schema;

    /**
     * 表名或视图名
     */
    @ApiModelProperty(value = "表名")
    private String name;

    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 10;
}