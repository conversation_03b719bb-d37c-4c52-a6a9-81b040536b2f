package com.jykj.dqm.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 术语映射列表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 14:16:49
 */
@ApiModel(value = "术语映射列表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DICT_MAPPINGS")
public class DictMappings implements Serializable {
    /**
     * 序号
     */
    @TableId(value = "MAPPINGS_ID", type = IdType.AUTO)
    @ApiModelProperty(value = "序号")
    private Integer mappingsId;

    /**
     * 术语编码
     */
    @TableField(value = "TERM_ID")
    @ApiModelProperty(value = "术语编码")
    private String termId;

    /**
     * 术语名称
     */
    @TableField(value = "TERM_NAME")
    @ApiModelProperty(value = "术语名称")
    private String termName;

    /**
     * 映射表名
     */
    @TableField(value = "MAPPINGS_TABLE")
    @ApiModelProperty(value = "映射表名")
    private String mappingsTable;

    /**
     * 映射类型
     */
    @TableField(value = "MAPPINGS_TYPE")
    @ApiModelProperty(value = "映射类型")
    private String mappingsType;

    /**
     * 创建人
     */
    @TableField(value = "CREATE_BY")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATE_BY")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除标记
     */
    @TableField(value = "DELETED_FLAG")
    @ApiModelProperty(value = "删除标记")
    private String deletedFlag;

    /**
     * 编码字段
     */
    @TableField(value = "CODE_FIELD")
    @ApiModelProperty(value = "编码字段")
    private String codeField;

    /**
     * 名称字段
     */
    @TableField(value = "NAME_FIELD")
    @ApiModelProperty(value = "名称字段")
    private String nameField;

    /**
     * 匹配方式 0：名称 1：编码
     */
    @TableField(value = "MATCH_TYPE")
    @ApiModelProperty(value = "匹配方式 0：名称 1：编码")
    private String matchType;

    /**
     * 对应值域代码
     */
    @TableField(value = "BD_CODE")
    @ApiModelProperty(value = "对应值域代码")
    private String bdCode;

    /**
     * 对应值域名称
     */
    @TableField(value = "BD_NAME")
    @ApiModelProperty(value = "对应值域名称")
    private String bdName;

    /**
     * 系统编码
     */
    @TableField(value = "SYS_CODE")
    @ApiModelProperty(value = "系统编码")
    private String sysCode;

    /**
     * 系统名称
     */
    @TableField(value = "SYS_NAME")
    @ApiModelProperty(value = "系统名称")
    private String sysName;

    private static final long serialVersionUID = 1L;
}