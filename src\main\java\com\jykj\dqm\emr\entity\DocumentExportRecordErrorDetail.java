package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文档导出报错详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/17 11:10:47
 */
@ApiModel(description = "文档导出报错详情")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DOCUMENT_EXPORT_RECORD_ERROR_DETAIL")
public class DocumentExportRecordErrorDetail implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 文档导出记录ID
     */
    @TableField(value = "EXPORT_RECORD_ID")
    @ApiModelProperty(value = "文档导出记录ID")
    private String exportRecordId;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型")
    private String emrRuleType;

    /**
     * 错误原因
     */
    @TableField(value = "ERROR_REASON")
    @ApiModelProperty(value = "错误原因")
    private String errorReason;

    /**
     * 错误类型
     */
    @TableField(value = "ERROR_TYPE")
    @ApiModelProperty(value = "错误类型：告警、失败")
    private String errorType;

    private static final long serialVersionUID = 1L;
}