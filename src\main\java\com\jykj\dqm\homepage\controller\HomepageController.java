package com.jykj.dqm.homepage.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.homepage.service.IHomepageService;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/14 10:52:01
 */
@Api(tags = {"数据质量首页（旧）"})
@RestController
@RequestMapping("/homepage")
@RequiredArgsConstructor
public class HomepageController {

    private final IHomepageService iHomepageService;

    @ApiOperation(value = "数据分析", notes = "", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "selectDate", value = "yyyy-MM-dd"),
    })
    @GetMapping("/getDataAnalysis")
    public R<?> getDataAnalysis(@RequestParam("selectDate") String selectDate) {
        Map<String, Object> map = iHomepageService.getDataAnalysis(selectDate);
        return RUtil.success(map);
    }

    @ApiOperation(value = "问题数据趋势", notes = "", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "startDate", value = "yyyy-MM-dd HH:mm:ss"),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "endDate", value = "yyyy-MM-dd HH:mm:ss"),
    })
    @GetMapping("/getProblemDataTrends")
    public R<?> getProblemDataTrends(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        Map<String, Object> map = iHomepageService.getProblemDataTrends(startDate, endDate);
        return RUtil.success(map);
    }

    @ApiOperation(value = "系统规则", notes = "", httpMethod = "GET")
    @GetMapping("/getSysCheckRule")
    public R<?> getSysCheckRule() {
        Map<String, Object> map = iHomepageService.getSysCheckRule();
        return RUtil.success(map);
    }

    @ApiOperation(value = "问题占比情况", notes = "", httpMethod = "GET")
    @GetMapping("/getTheQstnProportion")
    public R<?> getTheQstnProportion() {
        Map<String, Object> map = iHomepageService.getTheQstnProportion();
        return RUtil.success(map);
    }

    @ApiOperation(value = "问题排序", notes = "", httpMethod = "GET")
    @GetMapping("/getTheQstnSort")
    public R<?> getTheQstnSort(@RequestParam("limitNum") Integer limitNum) {
        List<DataqulityQstnManagerVO> list = iHomepageService.getTheQstnSort(limitNum);
        return RUtil.success(list);
    }
}
