<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.metadata.dao.MetaDataCollectionMapper">
    <select id="getScheduleJobInfo" resultType="com.jykj.dqm.quartz.entity.ScheduleJobInfo" databaseId="mysql">
        SELECT *
        FROM DQM_SCHEDULE_JOB_TASK t1
        <where>
            <if test="taskName != null and taskName != ''">
                t1.JOB_NAME like CONCAT('%', #{taskName}, '%')
            </if>
            <if test="dataSourceName != null and dataSourceName != ''">
                and t1.JOB_ID in (
                        SELECT c1.TASK_GROUP_ID
                        FROM DQM_TASK_GROUP_SUBTASKS c1
                                     LEFT JOIN DQM_METADATA_DATASOURCE c2 on c1.SUB_TASK_ID = c2.DATA_SOURCE_ID
                        where c2.DATA_SOURCE_NAME like CONCAT('%', #{dataSourceName}, '%')
                        )
            </if>
            <if test="dataSourceId != null and dataSourceId != ''">
                and t1.JOB_ID in (
                SELECT c1.TASK_GROUP_ID
                FROM DQM_TASK_GROUP_SUBTASKS c1
                LEFT JOIN DQM_METADATA_DATASOURCE c2 on c1.SUB_TASK_ID = c2.DATA_SOURCE_ID
                where c2.DATA_SOURCE_ID = #{dataSourceId}
                )
            </if>
            <if test="jobExeType != null and jobExeType != ''">
                and t1.JOB_EXE_TYPE = #{jobExeType}
            </if>
            <if test="taskType != null and taskType != ''">
                and t1.TASK_TYPE = #{taskType}
            </if>
        </where>
    </select>

    <select id="getScheduleJobInfo" resultType="com.jykj.dqm.quartz.entity.ScheduleJobInfo"  databaseId="oracle">
        SELECT *
        FROM DQM_SCHEDULE_JOB_TASK t1
        <where>
            <if test="taskName != null and taskName != ''">
                t1.JOB_NAME like '%'|| #{taskName} ||'%'
            </if>
            <if test="dataSourceName != null and dataSourceName != ''">
                and t1.JOB_ID in (
                SELECT c1.TASK_GROUP_ID
                FROM DQM_TASK_GROUP_SUBTASKS c1
                LEFT JOIN DQM_METADATA_DATASOURCE c2 on c1.SUB_TASK_ID = c2.DATA_SOURCE_ID
                where c2.DATA_SOURCE_NAME like '%'|| #{dataSourceName} ||'%'
                )
            </if>
            <if test="dataSourceId != null and dataSourceId != ''">
                and t1.JOB_ID in (
                SELECT c1.TASK_GROUP_ID
                FROM DQM_TASK_GROUP_SUBTASKS c1
                LEFT JOIN DQM_METADATA_DATASOURCE c2 on c1.SUB_TASK_ID = c2.DATA_SOURCE_ID
                where c2.DATA_SOURCE_ID = #{dataSourceId}
                )
            </if>
            <if test="jobExeType != null and jobExeType != ''">
                and t1.JOB_EXE_TYPE = #{jobExeType}
            </if>
            <if test="taskType != null and taskType != ''">
                and t1.TASK_TYPE = #{taskType}
            </if>
        </where>
    </select>
</mapper>