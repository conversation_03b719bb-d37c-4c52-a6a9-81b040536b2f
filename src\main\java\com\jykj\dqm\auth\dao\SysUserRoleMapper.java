package com.jykj.dqm.auth.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {
    /**
     * 查询用户角色
     *
     * @return List<SysUserRole>
     */
    List<SysUserRole> queryList();

    /**
     * 添加用户角色
     *
     * @param sysUserRole SysUserRole
     */
    void add(@Param("sysUserRole") SysUserRole sysUserRole);

    /**
     * 通过用户Id获取所有角色Id
     *
     * @param userId 用户Id
     * @return 所有角色Id
     */
    List<String> getAllRoleList(@Param("userId") String userId);

    /**
     * 通过用户Id获取所有权限Id
     *
     * @param userId 用户Id
     * @return 所有权限Id
     */
    List<String> getAllPermissionList(@Param("userId")String userId);

    /**
     * 通过用户Id获取所有角色信息
     *
     * @param userId 用户Id
     * @return 所有角色信息
     */
    List<SysRole> getAllRoleInfoList(@Param("userId")String userId);
}
