package com.jykj.dqm.quartz.service;

import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.entity.TaskProcessResult;
import com.jykj.dqm.quartz.service.impl.ScheduleJobTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class JobLogService {
    private final static Logger log = LoggerFactory.getLogger(JobLogService.class);
    @Autowired
    private ScheduleJobTaskService scheduleJobTaskService;

    public void startJobLog(ScheduleJobInfo scheduleJob, String logId) {
        log.info(scheduleJob.getJobName() + " start");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("logId", logId);
        params.put("jobId", scheduleJob.getJobId());
        params.put("taskType", scheduleJob.getTaskType());
        params.put("jobName", scheduleJob.getJobName());
        params.put("jobGroup", scheduleJob.getJobGroup());
        params.put("desc_", scheduleJob.getJobDesc());
        scheduleJobTaskService.addScheduleJobTaskLog(params);
    }

    public void endJobLog(ScheduleJobInfo scheduleJob, String logId, TaskProcessResult result) {
        log.info(scheduleJob.getJobName() + " end");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("logId", logId);
        params.put("desc_", result.toString());
        scheduleJobTaskService.updateScheduleJobTaskLog(params);
    }
}
