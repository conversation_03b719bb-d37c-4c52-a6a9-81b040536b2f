package com.jykj.dqm.system.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 登录日志查询
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@ApiModel(value = "登录日志查询")
@Data
public class LogLoginDTO implements Serializable {
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 登录用户id
     */
    @ApiModelProperty(value = "登录用户id")
    private String loginId;

    /**
     * IP
     */
    @ApiModelProperty(value = "登录IP")
    private String ip;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize;

    /**
     * 系统编号
     */
    @ApiModelProperty(value = "系统编号", hidden = true)
    private String sysId = "DQM";
}