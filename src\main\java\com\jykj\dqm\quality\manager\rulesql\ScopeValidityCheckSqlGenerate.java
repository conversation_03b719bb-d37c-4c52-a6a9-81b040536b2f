package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.stereotype.Component;

/**
 * 范围有效性检核SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class ScopeValidityCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    /*
      字符串  select LOCK_NAME,uat_cd from datacontrol.qrtz_locks_uat_p where (LOCK_NAME>'5' or LOCK_NAME<'4')
      数值   select sum,uat_cd from datacontrol.qrtz_locks_uat_p where (sum>11 or sum<=8)
      日期   select uat_dt,LOCK_NAME from datacontrol.qrtz_locks_uat_p where (uat_dt>=str_to_date('2000-01-04','%Y-%m-%d') or uat_dt<str_to_date('2000-01-02','%Y-%m-%d'))
     */
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        //检核表
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        //检核where条件
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        //检核明细字段
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        //检核字段
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        //值域(范围有效性)：0数值、1日期 ,2字符串
        String checkObjectType = dataqualityCheckRule.getCheckObjectType();
        //上限数值
        String maxValue = dataqualityCheckRule.getMaxValue();
        //下限数值
        String minValue = dataqualityCheckRule.getMinValue();
        //上限数值是否包含 0包含 1不包含
        String maxValueContain = dataqualityCheckRule.getMaxValueContain();
        //下限数值是否包含 0包含 1不包含
        String minValueContain = dataqualityCheckRule.getMinValueContain();
        //反着来，因为是查询不满足条件的
        String maxSign = ">=";
        if ("0".equals(maxValueContain)) {
            maxSign = ">";
        }
        String minSign = "<=";
        if ("0".equals(minValueContain)) {
            minSign = "<";
        }
        String dbType = dataqualityCheckRule.getDbType();
        //问题明细字段SQL
        StringBuilder qstDetailFieldSql = new StringBuilder();
        qstDetailFieldSql.append("select " + detailFiled + " from " + table);
        if (checkObjectType.equals("0")) {
            //数值
            qstDetailFieldSql.append(" where (" + checkRuleColumn + maxSign + maxValue + " OR " + checkRuleColumn + minSign + minValue + ")");
        } else if (checkObjectType.equals("1")) {
            //日期
            qstDetailFieldSql.append(" where (" + checkRuleColumn + maxSign + getSqlDateFunction(dbType, maxValue) + " OR " + checkRuleColumn + minSign + getSqlDateFunction(dbType, minValue) + ")");
        } else {
            //字符串
            qstDetailFieldSql.append(" where (" + checkRuleColumn + maxSign + "'" + maxValue + "'" + " OR " + checkRuleColumn + minSign + "'" + minValue + "'" + ")");
        }
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" and " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.FWYXX;
    }
}
