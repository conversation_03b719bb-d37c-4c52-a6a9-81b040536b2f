package com.jykj.dqm.system.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.system.entity.DictMappingsContentDTO;
import com.jykj.dqm.system.entity.DictMappingsDTO;

/**
 * 字典管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 11:11:20
 */
public interface DictManagementService {
    /**
     * 查询字典管理列表接口
     *
     * @param dictMappingDTO DictMappingsDTO
     * @return 字典管理列表
     * <AUTHOR>
     */
    R getDictManagementList(DictMappingsDTO dictMappingDTO);

    /**
     * 同步主数据系统数据
     * <p>
     * 如果是本库，直接同步
     * 如果是其他库，根据dataSoureceId建立连接
     * 同步DQM_DICT_MAPPINGS
     * 同步MDM_DICT_MAPPINGS_CONTENT
     *
     * @return Result
     * <AUTHOR>
     */
    R syncDictData();

    /**
     * 查询字典数据接口
     *
     * @param dictMappingsContentDTO DictMappingsContentDTO
     * @return 字典数据
     * <AUTHOR>
     */
    R getDictDataList(DictMappingsContentDTO dictMappingsContentDTO);

    /**
     * 查询所有的系统信息
     *
     * @return 所有的系统信息
     * <AUTHOR>
     */
    R getSysInfoList();
}
