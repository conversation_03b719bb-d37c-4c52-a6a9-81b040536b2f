//package com.jykj.dqm.config.schedule;
//
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.TaskScheduler;
//import org.springframework.scheduling.Trigger;
//import org.springframework.scheduling.TriggerContext;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.support.CronTrigger;
//
//import java.util.Date;
//import java.util.concurrent.ScheduledFuture;
//
//@Slf4j
//@Data
//@Configuration
//@EnableScheduling
//public class MyCronJob implements SchedulerObjectInterface {
//    private String cron = "0 */1 * * * ?";
//
//    private ScheduledFuture future;
//
//    @Autowired
//    private TaskScheduler scheduler;
//
//
//    @Override
//    public void start() {
//        log.info("进入start方法");
//        future = scheduler.schedule(new Runnable() {
//            @Override
//            public void run() {
//                System.out.println("  Hello World! " + new Date());
//            }
//        }, new Trigger() {
//            @Override
//            public Date nextExecutionTime(TriggerContext triggerContext) {
//                System.out.println("cron++++" + cron);
//                CronTrigger trigger = new CronTrigger(cron);
//                return trigger.nextExecutionTime(triggerContext);
//            }
//        });
//
//    }
//
//    @Override
//    public void stop() {
//        future.cancel(false);
//    }
//}