package com.jykj.dqm.quality.manager.rulesql;

/**
 * 规则小类枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/31 9:25:48
 */
public enum CheckRuleTypeEnum {
    WYX("WYX", "唯一性"),
    WZX("WZX", "完整性"),
    ZTWZX("ZTWZX", "整体完整性"),
    TJWZX("TJWZX", "条件完整性"),
    JSX("JSX", "及时性"),
    CDGFX("CDGFX", "长度规范性"),
    YZX("YZX", "一致性（主外键）"),
    FWYXX("FWYXX", "范围有效性"),
    DMYXX("DMYXX", "代码有效性"),
    JSZQX("JSZQX", "计算正确性"),
    ZDYWZX("ZDYWZX", "自定义完整性"),
    ZDYYZX("ZDYYZX", "自定义一致性"),
    ZDYZQX("ZDYZQX", "自定义准确性"),
    ZDYYXX("ZDYYXX", "自定义有效性"),
    ZDYGFX("ZDYGFX", "自定义规范性"),
    ZDYJSX("ZDYJSX", "自定义及时性"),
    ZDY("ZDY", "自定义");

    private String type;
    private String value;

    CheckRuleTypeEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public String getValue() {
        return value;
    }
}
