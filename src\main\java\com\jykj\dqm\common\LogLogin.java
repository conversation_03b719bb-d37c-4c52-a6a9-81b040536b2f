package com.jykj.dqm.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@ApiModel(value = "登录日志")
@Data
public class LogLogin implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private int id;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 登录用户id
     */
    @ApiModelProperty(value = "登录用户id")
    private String loginId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * IP
     */
    @ApiModelProperty(value = "登录IP")
    private String ip;

    /**
     * 登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "登录时间")
    private Date loginTime;

    /**
     * 系统编号
     */
    @ApiModelProperty(value = "系统编号",hidden = true)
    private String sysId = "DQM";

    private static final long serialVersionUID = 1L;
}