package com.jykj.dqm.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 元数据结构详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 17:11:10
 */
@ApiModel(value = "元数据结构详情")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_METADATA_STRUCTURE_DETAIL")
public class MetadataStructureDetail {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 元数据结构表ID
     */
    @TableField(value = "STRUCTURE_INFO_ID")
    @ApiModelProperty(value = "元数据结构表ID")
    private Integer structureInfoId;

    /**
     * 数据源ID
     */
    @TableField(value = "DATA_SOURCE_ID")
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据库名称
     */
    @TableField(value = "DATABASE_NAME")
    @ApiModelProperty(value = "数据库名称")
    private String databaseName;

    /**
     * SCHEMA
     */
    @TableField(value = "SCHEMA", keepGlobalFormat = true)
    @ApiModelProperty(value = "SCHEMA")
    private String schema;

    /**
     * 表名
     */
    @TableField(value = "TABLE_NAME", keepGlobalFormat = true)
    @ApiModelProperty(value = "表名")
    private String tableName;

    /**
     * 字段名
     */
    @TableField(value = "NAME", keepGlobalFormat = true)
    @ApiModelProperty(value = "字段名")
    private String name;

    /**
     * 字段名注释
     */
    @TableField(value = "REMARKS", keepGlobalFormat = true)
    @ApiModelProperty(value = "字段名注释")
    private String remarks;

    /**
     * 字段类型
     */
    @TableField(value = "TYPE", keepGlobalFormat = true)
    @ApiModelProperty(value = "字段类型")
    private String type;

    /**
     * 是否为空：true/false
     */
    @TableField(value = "NULLABLE", keepGlobalFormat = true)
    @ApiModelProperty(value = "是否为空：true/false")
    private String nullable;

    /**
     * 是否为PK：true/false
     */
    @TableField(value = "PK")
    @ApiModelProperty(value = "是否为PK：true/false")
    private String pk;

    /**
     * 字段长度
     */
    @TableField(value = "LENGTH", keepGlobalFormat = true) //配合全局设置
    @ApiModelProperty(value = "字段长度")
    private String length;

    /**
     * 精度
     */
    @TableField(value = "SCALE", keepGlobalFormat = true)
    @ApiModelProperty(value = "精度")
    private String scale;

    /**
     * 默认值
     */
    @TableField(value = "DEFAULTVALUE")
    @ApiModelProperty(value = "默认值")
    private String defaultvalue;
}