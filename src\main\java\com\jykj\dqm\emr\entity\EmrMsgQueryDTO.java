package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 电子病历评级相关信息
 *
 * @TableName DQM_EMR_MSG
 */
@TableName(value = "DQM_EMR_MSG")
@Data
public class EmrMsgQueryDTO extends MyPageInfo {
    /**
     * 消息类型（行业新闻、行业标准、政策文件）
     */
    @ApiModelProperty(value = "消息类型（行业新闻、行业标准、政策文件）")
    private String msgType;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    private String msgTitle;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String msgContent;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String sender;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;
}
