package com.jykj.dqm.quartz.service;

import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.entity.TaskProcessResult;

public interface TaskJobInterface {
    /**
     * 返回任务的类型编码
     *
     * @return
     */
    String getTaskType();

    /***
     * 返回任务的说明
     * @return
     */
    String getTaskDesc();

    /***
     * 任务的处理
     * @param jobData 任务执行参数
     */
    TaskProcessResult processTask(ScheduleJobInfo jobData);
}
