package com.jykj.dqm.quality.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.quality.dao.DataqualityCheckRuleMapper;
import com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检核规则执行服务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/25 13:40:00
 */
@Slf4j
@Service
public class QualityCheckService {
    @Autowired
    private DataqualityCheckRuleMapper dataqualityCheckRuleMapper;

    @Autowired
    private DataqualityTaskInstanceMapper dataqualityTaskInstanceMapper;

    @Autowired
    ExecRuleCheckTask execRuleCheckTask;

    public R exeRuleCheckTask(Map<String, Object> params) {
        DataqualityCheckRule dataqualityCheckRule = getCheckRules(params);
        if (dataqualityCheckRule != null) {
            //开始执行任务
            log.info("开始执行任务:{}", dataqualityCheckRule);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("TASK_GROUP_ID", params.get("sysCode"));
            paramMap.put("TASK_STATE", "00");
            paramMap.put("CHECK_RULE_ID", dataqualityCheckRule.getCheckRuleId());
            List<DataqualityTaskInstance> dataqualityTaskInstances = dataqualityTaskInstanceMapper.selectByMap(paramMap);
            if (CollectionUtil.isNotEmpty(dataqualityTaskInstances)) {
                return RUtil.error(1, "任务正在执行中...");
            }
            execRuleCheckTask.exeTask(params, dataqualityCheckRule);
        }
        return RUtil.success();
    }

    private DataqualityCheckRule getCheckRules(Map<String, Object> params) {
        DataqualityCheckRule dataqualityCheckRule = dataqualityCheckRuleMapper.selectOne(
                new QueryWrapper<DataqualityCheckRule>().eq("CHECK_RULE_ID", params.get("checkRuleId"))
        );
        return dataqualityCheckRule;
    }

}
