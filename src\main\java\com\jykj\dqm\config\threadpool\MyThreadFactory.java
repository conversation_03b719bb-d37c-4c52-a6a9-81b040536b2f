/**
 * @Title : 线程池ThreadFactory配置
 * @Package : com.scjy.mdm.config
 * @Description :MyThreadFactory
 * <AUTHOR> 黄杰
 * @DateTime : 2021/8/17 11:29
 */
package com.jykj.dqm.config.threadpool;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池ThreadFactory配置
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public class MyThreadFactory {
    static class SelfDefinedThreadFactory implements ThreadFactory {
        /**
         * 线程池计数
         */
        final AtomicInteger threadNumber = new AtomicInteger(1);

        /**
         * 线程池名称前缀
         */
        final String namePrefix;

        /**
         * 设置前缀
         *
         * @param namePrefix 线程池名称前缀
         */
        SelfDefinedThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix + "-";
        }

        /**
         * 创建线程
         *
         * @param r Runnable
         * @return Thread
         * <AUTHOR>
         */
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            if (t.isDaemon()) {
                t.setDaemon(true);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
}
