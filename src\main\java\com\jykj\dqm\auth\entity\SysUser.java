package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 15:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "SYS_USER")
@ApiModel
public class SysUser implements Serializable {
    /**
     * 集团编码
     */
    @ApiModelProperty(value = "集团编码")
    @TableField(value = "GROUP_CODE")
    private String groupCode = Constant.GROUP_CODE;
    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    @TableField(value = "ORGANIZATION_CODE")
    private String organizationCode = Constant.ORGANIZATION_CODE;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableId(type = IdType.INPUT)
    @TableField(value = "USER_ID")
    private Integer userId;
    /**
     * 登陆用户id
     */
    @ApiModelProperty(value = "登陆用户id", required = true)
    @TableField(value = "LOGIN_ID")
    private String loginId;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", required = true)
    @TableField(value = "PASSWORD")
    private String password;
    /**
     * 密码失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "密码失效时间")
    @TableField(value = "PASSWORD_EXPIRATION_DATE")
    private String passwordExpirationDate;
    /**
     * 密码强制修改标志
     */
    @ApiModelProperty(value = "密码强制修改标志")
    @TableField(value = "PASSWORD_RESET")
    private Integer passwordReset;
    /**
     * 登陆ip
     */
    @ApiModelProperty(value = "登陆ip")
    @TableField(value = "LOGIN_IP")
    private String loginIp;
    /**
     * 上次登录ip
     */
    @ApiModelProperty(value = "上次登录ip")
    @TableField(value = "LAST_LOGIN_DATE")
    private String lastLoginIp;
    /**
     * 上次登录时间
     */
    @ApiModelProperty(value = "上次登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "LAST_LOGIN_IP")
    private String lastLoginDate;
    /**
     * 验证登录失败次数
     */
    @ApiModelProperty(value = "验证登录失败次数")
    @TableField(value = "FAILED_LOGIN_ATTEMPTS")
    private Integer failedLoginAttempts = 0;
    /**
     * 用户状态
     */
    @ApiModelProperty(value = "用户状态")
    @TableField(value = "USER_STATUS")
    private Integer userStatus;
    /**
     * 状态修改时间
     */
    @ApiModelProperty(value = "状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "MODIFIED_DATE")
    private String modifiedDate;
    /**
     * 员工id"
     */
    @ApiModelProperty(value = "员工id")
    @TableField(value = "EMPLOYMENT_ID")
    private Long employmentId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "USER_NAME")
    private String userName;
    /**
     * 角色
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "角色")
    private List<SysRole> list;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @TableField(value = "MOBILE")
    private String mobile;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME")
    private String createTime;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField(value = "EMAIL")
    private String email;
    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    @TableField(value = "SYS_ID")
    private String sysId = Constant.SYS_NAME;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @TableField(value = "GENDER")
    private String gender;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    @TableField(value = "ID_CARD")
    private String idCard;

    /**
     * 验证方式（0：帐密+验证码 ；1：短信验证码;2:账密+短信验证码） 默认0密码
     */
    @ApiModelProperty(value = "验证方式（0：帐密+验证码 ；1：短信验证码;2:账密+短信验证码，如果是多种就用逗号隔开传值")
    @TableField(value = "IDENTIFY_TYPE")
    private String identifyType = "0";

    /**
     * 账号启用日期，默认现在
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")    //后端-->前端。
    @ApiModelProperty(value = "账号启用日期，默认现在")
    @TableField(value = "ACCOUNT_ACTIVATION_DATE")
    private Date accountActivationDate;

    /**
     * 账号失效日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")    //后端-->前端。
    @ApiModelProperty(value = "账号失效日期")
    @TableField(value = "ACCOUNT_EXPIRATION_DATE")
    private Date accountExpirationDate;

    /**
     * token过期时间（单位小时，默认2小时）
     */
    @ApiModelProperty(value = "token过期时间（单位小时，默认2小时）")
    @TableField(value = "TOKEN_EXPIRATION_TIME")
    private int tokenExpirationTime = 2;

    /**
     * 企业微信账户类
     */
    @TableField(exist = false)
    private UserWxAccount userWxAccount;

    /**
     * 企业微信账户
     */
    @TableField(exist = false)
    private String wxAccount;
}
