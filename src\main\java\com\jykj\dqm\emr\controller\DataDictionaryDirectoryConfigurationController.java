package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryConfiguration;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryConfigurationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 评级文档指标统计配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/9/26 11:56:26
 */

@Api(tags = {"评级文档指标统计配置"})
@RestController
@RequestMapping("/emr/dataDictionaryDirectoryConfiguration")
public class DataDictionaryDirectoryConfigurationController {
    @Autowired
    private DataDictionaryDirectoryConfigurationService dataDictionaryDirectoryConfigurationService;

    @LogRemark(operate = "新增基础数据字典", module = "基础数据字典")
    @ApiOperation(value = "新增基础数据字典", notes = "基础数据字典")
    @PostMapping("/addBase")
    public R addBase(@Validated @RequestBody DataDictionaryDirectoryConfiguration dataDictionaryDirectoryConfiguration) {
        return dataDictionaryDirectoryConfigurationService.addBase(dataDictionaryDirectoryConfiguration);
    }

    @LogRemark(operate = "批量更新基础数据字典", module = "基础数据字典")
    @ApiOperation(value = "批量更新基础数据字典（注意只传修改的，不是全部）", notes = "基础数据字典")
    @PostMapping("/batchUpdateBase")
    public R batchUpdateBase(@Validated @RequestBody List<DataDictionaryDirectoryConfiguration> dataDictionaryDirectoryConfigurations) {
        return dataDictionaryDirectoryConfigurationService.batchUpdateBase(dataDictionaryDirectoryConfigurations);
    }

    @LogRemark(operate = "删除基础数据字典", module = "基础数据字典")
    @ApiOperation(value = "删除基础数据字典", notes = "基础数据字典")
    @DeleteMapping("/deleteBase")
    public R deleteBase(@RequestParam("ids") List<Long> ids) {
        return dataDictionaryDirectoryConfigurationService.deleteBase(ids);
    }

    @ApiOperation(value = "获取基础数据字典", notes = "基础数据字典")
    @GetMapping("/getBase")
    public R getBase() {
        return dataDictionaryDirectoryConfigurationService.getBase();
    }

    @ApiOperation(value = "获取病历|质量数据字典", notes = "病历|质量数据字典")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "directoryType", value = "目录类型：2、病历数据，3、质量数据", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "directoryCode", value = "目录编码，为空查左侧列表，不为空查子级", required = false)
    })
    @GetMapping("/getMedicaRecordsOrQuality")
    public R getMedicaRecordsAndQuality(Integer directoryType, String directoryCode) {
        return dataDictionaryDirectoryConfigurationService.getMedicaRecordsAndQuality(directoryType, directoryCode);
    }
}
