package com.jykj.dqm.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.metadata.entity.MetadataStructureDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 元数据结构详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 17:11:10
 */
@Mapper
public interface MetadataStructureDetailMapper extends BaseMapper<MetadataStructureDetail> {
    void saveBatch(List<MetadataStructureDetail> metadataStructureDetails);
}