package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文档导出记录规则详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/29 15:42:09
 */
@ApiModel(description = "文档导出记录规则详情")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DOCUMENT_EXPORT_RECORD_RULE_DETAIL")
public class DocumentExportRecordRuleDetail extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 文档导出记录ID
     */
    @TableField(value = "EXPORT_RECORD_ID")
    @ApiModelProperty(value = "文档导出记录ID")
    private String exportRecordId;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型")
    private String emrRuleType;

    /**
     * 问题数据备注1
     */
    @TableField(value = "PROBLEM_DATA_REMARKS1")
    @ApiModelProperty(value = "问题数据备注1-文中说明")
    private String problemDataRemarks1;

    /**
     * 问题数据备注2
     */
    @TableField(value = "PROBLEM_DATA_REMARKS2")
    @ApiModelProperty(value = "问题数据备注2-SQL注解")
    private String problemDataRemarks2;

    /**
     * 问题数据备注3
     */
    @TableField(value = "PROBLEM_DATA_REMARKS3")
    @ApiModelProperty(value = "问题数据备注3-原因说明")
    private String problemDataRemarks3;

    /**
     * 问题数据备注4
     */
    @TableField(value = "PROBLEM_DATA_REMARKS4")
    @ApiModelProperty(value = "问题数据备注4-整改措施")
    private String problemDataRemarks4;

    private static final long serialVersionUID = 1L;
}