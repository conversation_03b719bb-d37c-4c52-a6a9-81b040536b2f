package com.jykj.dqm.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import com.jykj.dqm.auth.entity.EmrSysUserGroupVo;
import com.jykj.dqm.common.R;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_SYS_USER_GROUP(用户分组表)】的数据库操作Service
 * @createDate 2024-03-15 17:56:21
 */
public interface EmrSysUserGroupService extends IService<EmrSysUserGroup> {

    R save(EmrSysUserGroupVo emrSysUserGroupVo);

    List<EmrSysUserGroup> queryByGroupName(String groupName, String userAccount,String projectId);
}
