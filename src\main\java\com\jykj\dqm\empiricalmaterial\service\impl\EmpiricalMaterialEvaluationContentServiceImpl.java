package com.jykj.dqm.empiricalmaterial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.empiricalmaterial.dao.EmpiricalMaterialEvaluationContentMapper;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectory;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContent;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContentDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContentQuery;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialDirectoryService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialEvaluationContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_EVALUATION_CONTENT(实证材料评价)】的数据库操作Service实现
 * @createDate 2024-01-22 14:50:50
 */
@Service
public class EmpiricalMaterialEvaluationContentServiceImpl extends ServiceImpl<EmpiricalMaterialEvaluationContentMapper, EmpiricalMaterialEvaluationContent> implements EmpiricalMaterialEvaluationContentService {
    @Autowired
    private EmpiricalMaterialDirectoryService empiricalMaterialDirectoryService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R save(EmpiricalMaterialEvaluationContentDTO evaluationContent) {
        if (StrUtil.isBlank(evaluationContent.getDirectoryCode()) || StrUtil.isBlank(evaluationContent.getDirectoryName())) {
            return RUtil.error("目录编码或目录名称不能为空");
        }
        //先检查evaluationContent.getEvaluationContents()是否重复
        long count = evaluationContent.getEvaluationContents().stream().filter(item -> StrUtil.isNotBlank(item.getEvaluationContent())).distinct().count();
        if (count != evaluationContent.getEvaluationContents().stream().filter(item -> StrUtil.isNotBlank(item.getEvaluationContent())).count()) {
            return RUtil.error("评价内容不能重复");
        }

        //先删除
        this.remove(Wrappers.lambdaQuery(EmpiricalMaterialEvaluationContent.class).eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, evaluationContent.getDirectoryCode()).eq(EmpiricalMaterialEvaluationContent::getDirectoryName, evaluationContent.getDirectoryName()));
        List<EmpiricalMaterialEvaluationContent> evaluationContents = evaluationContent.getEvaluationContents();
        if (CollUtil.isNotEmpty(evaluationContents)) {
            int i = 1;
            for (EmpiricalMaterialEvaluationContent content : evaluationContents) {
                if (StrUtil.isBlank(content.getEvaluationContent())) {
                    continue;
                }
                content.setId(null);
                content.setDirectoryCode(evaluationContent.getDirectoryCode());
                content.setDirectoryName(evaluationContent.getDirectoryName());
                content.setEvaluationCategory(evaluationContent.getEvaluationCategory());
                content.setSerialNum(i + "");
                i++;
            }
            //再新增
            this.saveBatch(evaluationContents);
        }
        //更新目录备注
        empiricalMaterialDirectoryService.update(Wrappers.lambdaUpdate(EmpiricalMaterialDirectory.class)
                .set(EmpiricalMaterialDirectory::getRemarksDesc, evaluationContent.getRemarksDesc())
                .eq(EmpiricalMaterialDirectory::getDirectoryCode, evaluationContent.getDirectoryCode())
                .eq(EmpiricalMaterialDirectory::getDirectoryName, evaluationContent.getDirectoryName()));
        return RUtil.success();
    }

    @Override
    public R query(EmpiricalMaterialEvaluationContentQuery evaluationContent) {
        List<EmpiricalMaterialEvaluationContent> materialEvaluationContents =
                this.list(Wrappers.lambdaQuery(EmpiricalMaterialEvaluationContent.class)
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, evaluationContent.getDirectoryCode())
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, evaluationContent.getDirectoryName()));
        return RUtil.success(materialEvaluationContents);
    }
}




