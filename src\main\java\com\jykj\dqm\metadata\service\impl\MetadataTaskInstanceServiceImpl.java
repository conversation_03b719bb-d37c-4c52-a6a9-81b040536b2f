package com.jykj.dqm.metadata.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.MetadataTaskInstanceMapper;
import com.jykj.dqm.metadata.entity.MetadataTaskInstance;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceDTO;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceVO;
import com.jykj.dqm.metadata.service.MetadataTaskInstanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采集任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 13:53:47
 */
@Service
public class MetadataTaskInstanceServiceImpl extends ServiceImpl<MetadataTaskInstanceMapper, MetadataTaskInstance> implements MetadataTaskInstanceService {
    @Autowired
    private MetadataTaskInstanceMapper metadataTaskInstanceMapper;

    @Override
    public R queryTaskAllocationHisInfo(MetadataTaskInstanceDTO metadataTaskInstanceDTO) {
        PageHelper.startPage(metadataTaskInstanceDTO.getPageNum(), metadataTaskInstanceDTO.getPageSize());
        List<MetadataTaskInstanceVO> metadataTaskInstances = metadataTaskInstanceMapper.queryTaskAllocationHisInfo(metadataTaskInstanceDTO);
        PageInfo pageInfo = new PageInfo(metadataTaskInstances);
        return RUtil.success(pageInfo);
    }
}
