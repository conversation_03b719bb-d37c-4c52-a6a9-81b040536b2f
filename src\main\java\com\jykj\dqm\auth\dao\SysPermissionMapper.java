package com.jykj.dqm.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.auth.entity.SysPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermission> {
    /**
     * 添加权限
     *
     * @param sysPermission SysPermission
     */
    void add(@Param("sysPermission") SysPermission sysPermission);

    /**
     * 查询权限
     *
     * @return List<SysPermission>
     */
    List<SysPermission> queryList();

    Set<SysPermission> getSysPermissionList(@Param("permissionIds") Set<String> permissionIdSet);
}
