package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.DocumentExporQueryDTO;
import com.jykj.dqm.emr.entity.DocumentExportEachDoc;
import com.jykj.dqm.emr.entity.DocumentExportRecord;
import com.jykj.dqm.emr.entity.DocumentExportRecordQuery;
import com.jykj.dqm.emr.entity.DocumentPreview;

/**
 * 文档导出记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:47:11
 */
public interface DocumentExportRecordService extends IService<DocumentExportRecord> {
    R query(DocumentExportRecordQuery documentExportRecordQuery);

    void previewWord(DocumentPreview documentPreview);

    R delete(String exportRecordId);

    R getErrorDetail(String exportRecordId);

    R getProblemDataRemarks(String exportRecordId);

    R getOneProblemDataRemark(DocumentExportEachDoc documentExportEachDoc);

    R getProblemDataHistoryRemarks(DocumentExporQueryDTO documentExportEachDoc);
}
