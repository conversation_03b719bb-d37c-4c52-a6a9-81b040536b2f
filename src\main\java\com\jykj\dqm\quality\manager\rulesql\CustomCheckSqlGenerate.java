package com.jykj.dqm.quality.manager.rulesql;

import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.stereotype.Component;

/**
 * 自定义检核SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/27 14:29:11
 */
@Component
public class CustomCheckSqlGenerate implements CheckSqlGenarate {
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        SqlUtils.checkSql(dataqualityCheckRule.getQuestionNmSql());
        SqlUtils.checkSql(dataqualityCheckRule.getTotalNmSql());
        SqlUtils.checkSql(dataqualityCheckRule.getPbSubsidiarySql());

        CheckSqlDTO checkSqlDTO = new CheckSqlDTO();
        checkSqlDTO.setQuestionNmSql(dataqualityCheckRule.getQuestionNmSql());
        checkSqlDTO.setTotalNmSql(dataqualityCheckRule.getTotalNmSql());
        checkSqlDTO.setQstDetailFieldSql(dataqualityCheckRule.getPbSubsidiarySql());
        return checkSqlDTO;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.ZDY;
    }
}
