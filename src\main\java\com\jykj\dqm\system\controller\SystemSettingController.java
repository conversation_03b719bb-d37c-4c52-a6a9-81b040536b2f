package com.jykj.dqm.system.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.system.service.SystemSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 系统设置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 13:47
 */
@Api(tags = {"系统设置"})
@RestController
@RequestMapping("/system")
public class SystemSettingController {

    @Autowired
    private SystemSettingService systemSettingService;

    /**
     * 获取所有系统设置（webShow为Y的）
     *
     * @return 系统设置
     * <AUTHOR>
     */
    @ApiOperation(value = "获取所有系统设置（webShow为Y的）", notes = "")
    @GetMapping("/querySysConfig")
    public R querySysConfig() {
        return systemSettingService.querySysConfig();
    }

    /**
     * 获取所有系统设置（包括webShow为N和Y的）
     *
     * @return 系统设置
     * <AUTHOR>
     */
    @ApiOperation(value = "获取所有系统设置（包括webShow为N和Y的）", notes = "")
    @GetMapping("/queryAllSysConfig")
    public R queryAllSysConfig() {
        return systemSettingService.queryAllSysConfig();
    }

    /**
     * 添加系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "添加系统设置", notes = "")
    @LogRemark(operate = "添加系统设置", module = "系统设置")
    @PostMapping("/addSysConfigs")
    public R addSysConfigs(@RequestBody List<SysConfig> sysConfigList) {
        return systemSettingService.addSysConfig(sysConfigList);
    }

    /**
     * 更新系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "更新系统设置", notes = "")
    @LogRemark(operate = "更新系统设置", module = "系统设置")
    @PostMapping("/updateSysConfigs")
    public R updateSysConfigs(@RequestBody List<SysConfig> sysConfigList) {
        return systemSettingService.updateSysConfigs(sysConfigList);
    }

    /**
     * 上传图片
     *
     * @param file  图片文件
     * @param title 标题（备用）
     * @param title logo类型
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "上传医院图标", notes = "")
    @LogRemark(operate = "上传医院图标", module = "系统设置")
    @PostMapping("/setHospitalLogo")
    public R setHospitalLogo(@RequestParam("file") MultipartFile file, String title, String logoType) {
        return systemSettingService.setHospitalLogo(file, title, logoType);
    }

    /**
     * 获取医院图标地址
     *
     * @return 医院图标地址
     * <AUTHOR>
     */
    @ApiOperation(value = "获取医院图标地址", notes = "")
    @GetMapping("/getHospitalLogoPath")
    public R getHospitalLogoPath(@RequestParam("logoType") String logoType) {
        return systemSettingService.getHospitalLogoPath(logoType);
    }
}
