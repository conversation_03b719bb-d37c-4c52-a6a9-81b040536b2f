package com.jykj.dqm.system.dao;

import com.jykj.dqm.common.LogLogin;
import com.jykj.dqm.system.entity.LogLoginDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * 登录日志mapper
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Mapper
public interface LogLoginMapper {
    /**
     * 物理删除登录日志
     *
     * @param id id
     * @return 变更条数
     * <AUTHOR>
     */
    int deleteByPrimaryKey(int id);

    /**
     * 插入登录日志
     *
     * @param record LogLogin
     * @return 变更条数
     * <AUTHOR>
     */
    int insertSelective(LogLogin record);

    /**
     * 根据id查询登录日志
     *
     * @param id id
     * @return LogLogin
     * <AUTHOR>
     */
    LogLogin selectByPrimaryKey(int id);

    /**
     * 更新登录日志（带if-test）
     *
     * @param record LogLogin
     * @return 变更条数
     * <AUTHOR>
     */
    int updateByPrimaryKeySelective(LogLogin record);

    /**
     * 更新登录日志（不带if-test）
     *
     * @param record LogLogin
     * @return 变更条数
     * <AUTHOR>
     */
    int updateByPrimaryKey(LogLogin record);

    /**
     * 清除登录日志
     *
     * @param date Date
     * @return 变更条数
     * <AUTHOR>
     */
    int clearloginLog(Date date);

    /**
     * 查询登录日志
     *
     * @param logLoginDTO LogLoginDTO
     * @return List<LogLogin>
     * <AUTHOR>
     */
    List<LogLogin> queryLog(LogLoginDTO logLoginDTO);
}