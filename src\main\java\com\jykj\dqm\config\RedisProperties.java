package com.jykj.dqm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * redis配置类型
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/4/1 14:17
 */
@ConfigurationProperties(
        prefix = "spring.redis"
)
@Configuration
@Data
public class RedisProperties {
    private String host;
    private String port;
    private String password;
    private int database;
}
