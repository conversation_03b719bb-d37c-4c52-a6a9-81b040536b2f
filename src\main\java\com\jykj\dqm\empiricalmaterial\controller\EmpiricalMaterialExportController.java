package com.jykj.dqm.empiricalmaterial.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecord;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathQuery;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportRecordService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportService;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.IdUtils;
import com.jykj.dqm.utils.NumberMyUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StpUtilMy;
import com.jykj.dqm.utils.SystemUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实证材料导出
 *
 * <AUTHOR>
 * @version 1.0
 * @Date
 */
@Api(tags = {"实证材料导出"})
@RestController
@Slf4j
@RequestMapping("/emm/empiricalmaterial/export")
public class EmpiricalMaterialExportController {
    @Autowired
    private EmpiricalMaterialExportService empiricalMaterialExportService;

    @Autowired
    private EmpiricalMaterialExportRecordService empiricalMaterialExportRecordService;

    /**
     * 预览文档
     *
     * @param documentPreview EmpiricalMaterialFlowPathQuery
     * <AUTHOR>
     */
    @ApiOperation(value = "预览文档", notes = "实证材料导出")
    @PostMapping("/previewWord")
    public void previewWord(@Validated @RequestBody EmpiricalMaterialFlowPathQuery documentPreview) {
        empiricalMaterialExportService.previewWord(documentPreview);
    }


    @LogRemark(operate = "导出文档（异步执行）", module = "实证材料导出")
    @ApiOperation(value = "导出文档（异步执行），会返回ID", notes = "实证材料导出")
    @PostMapping("/exportAsync")
    public R exportAsync(@Validated @RequestBody EmpiricalMaterialExportDTO empiricalMaterialExportDTO) {
        //校验当前是否有导出任务
        Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.emprocessMap;
        if (processMap != null && processMap.get(StpUtilMy.getTokenValue()) != null) {
            List<String> ids = new ArrayList<>();
            try {
                Map<String, Object> map = processMap.get(StpUtilMy.getTokenValue());
                ids = map.keySet().stream().collect(Collectors.toList());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (ids.size() > 0) {
                EmpiricalMaterialExportRecord exportRecordId = empiricalMaterialExportRecordService.getById(ids.get(0));
                if (exportRecordId != null && "0,1".contains(exportRecordId.getExportStatus())) {
                    throw new BusinessException("当前文档正在导出中，同一时间只能导出一份文档，请稍后重试！");
                }
            }
        }
        processMap.remove(StpUtilMy.getTokenValue());
        String username = StpUtilMy.getUserName();
        String id = IdUtils.getID();
        String empiricalMaterialExportDTOId = empiricalMaterialExportDTO.getId();
        if (StrUtil.isNotBlank(empiricalMaterialExportDTOId)) {
            String parent = SystemUtils.getFilePath();
            String filePath = parent + "/MaterialFlowPathFiles/" + empiricalMaterialExportDTOId + "/";
            List<String> fileNames = FileUtil.listFileNames(filePath);
            long count = fileNames.stream().filter(item -> item.contains("基本项") || item.contains("选择项")).count();
            if (count == 2) {
                TransmittableThreadLocalManager.emprocessMap.put(StpUtilMy.getTokenValue(), MapUtil.builder(new HashMap<String, Object>()).put(empiricalMaterialExportDTOId, 1.0).build());
                return RUtil.success(empiricalMaterialExportDTOId);
            }
            id = empiricalMaterialExportDTOId;
        }

        empiricalMaterialExportDTO.setUniqueId(id);
        empiricalMaterialExportService.exportAsync(empiricalMaterialExportDTO, username, StpUtilMy.getTokenValue());
        return RUtil.success(id);
    }

    /**
     * 异步导出文档进度
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果
     * <AUTHOR>
     */
    @ApiOperation(value = "异步导出文档进度（-1表示失败，1表示完成）,建议5秒调一次", notes = "实证材料导出")
    @GetMapping("/exportProcess")
    public R exportProcess(@RequestParam(value = "exportRecordId", required = false) String exportRecordId) {
        String tokenValue = StpUtilMy.getTokenValue();
        Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.emprocessMap;
        Map<String, Object> result = new HashMap<>();
        if (processMap == null || CollUtil.isEmpty(processMap.get(tokenValue))) {
            result.put("process", 0);
            result.put("exportRecordId", exportRecordId);
            return RUtil.success(result);
        }
        Map<String, Object> entity = processMap.get(tokenValue);
        List<Object> values = new ArrayList<>(entity.values());
        List<Object> keys = new ArrayList<>(entity.keySet());
        Double process = (Double) values.get(0);
        Double one = new Double("1.0");
        Double minusOne = new Double("-1.0");
        result.put("process", NumberMyUtil.round(process, 4));
        result.put("exportRecordId", keys.get(0));
        if (process != null && (one.compareTo(process) == 0 || minusOne.compareTo(process) == 0)) {
            processMap.remove(tokenValue);
        }
        return RUtil.success(result);
    }

    /**
     * 下载异步导出的文档
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果
     * <AUTHOR>
     */
    @ApiOperation(value = "下载异步导出的文档", notes = "实证材料导出", produces = "application/octet-stream")
    @GetMapping("/downloadDoc")
    public void downloadDoc(@RequestParam("exportRecordId") String exportRecordId) {
        empiricalMaterialExportService.downloadDoc(exportRecordId);
    }
}
