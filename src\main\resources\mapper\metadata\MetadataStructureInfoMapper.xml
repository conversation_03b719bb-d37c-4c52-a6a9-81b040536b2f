<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.metadata.dao.MetadataStructureInfoMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.metadata.entity.MetadataStructureInfo">
        <!--@mbg.generated-->
        <!--@Table DQM_METADATA_STRUCTURE_INFO-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="DATA_SOURCE_ID" jdbcType="INTEGER" property="dataSourceId"/>
        <result column="DATABASE_NAME" jdbcType="VARCHAR" property="databaseName"/>
        <result column="SCHEMA" jdbcType="VARCHAR" property="schema"/>
        <result column="CATALOG" jdbcType="VARCHAR" property="catalog"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List" databaseId="oracle">
        <!--@mbg.generated-->
        ID,
        DATA_SOURCE_ID,
        DATABASE_NAME,
        "SCHEMA",
        "CATALOG",
        "NAME",
        REMARKS,
        "TYPE",
        CREATE_TIME
    </sql>

    <sql id="Base_Column_List" databaseId="mysql">
        <!--@mbg.generated-->
        ID,
        DATA_SOURCE_ID,
        DATABASE_NAME,
        `SCHEMA`,
        `CATALOG`,
        `NAME`,
        REMARKS,
        `TYPE`,
        CREATE_TIME
    </sql>

    <select id="getAllStructuctureInfo" resultMap="BaseResultMap" databaseId="oracle">
        SELECT ID, DATA_SOURCE_ID, DATABASE_NAME, "SCHEMA", "NAME", "TYPE"
        FROM DQM_METADATA_STRUCTURE_INFO
        WHERE DATA_SOURCE_ID = #{dataSourceId}
        ORDER BY "NAME"
    </select>

    <select id="getAllStructuctureInfo" resultMap="BaseResultMap"  databaseId="mysql">
        SELECT ID, DATA_SOURCE_ID, DATABASE_NAME, `SCHEMA`, `NAME`, `TYPE`
        FROM DQM_METADATA_STRUCTURE_INFO
        WHERE DATA_SOURCE_ID = #{dataSourceId}
        ORDER BY `NAME`
    </select>
</mapper>