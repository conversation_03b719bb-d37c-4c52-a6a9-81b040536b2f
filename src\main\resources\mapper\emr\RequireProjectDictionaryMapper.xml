<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.emr.dao.RequireProjectDictionaryMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.emr.entity.RequireProjectDictionary">
        <!--@mbg.generated-->
        <!--@Table dqm_emr_require_project_dictionary-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DIRECTORY_CODE" jdbcType="VARCHAR" property="directoryCode"/>
        <result column="DIRECTORY_NAME" jdbcType="VARCHAR" property="directoryName"/>
        <result column="EMR_RULE_TYPE" jdbcType="VARCHAR" property="emrRuleType"/>
        <result column="REQUIRE_PROJECT_NAME" jdbcType="VARCHAR" property="requireProjectName"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="HEADER_NAME1" jdbcType="VARCHAR" property="headerName1"/>
        <result column="HEADER_NAME2" jdbcType="VARCHAR" property="headerName2"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        DIRECTORY_CODE,
        DIRECTORY_NAME,
        EMR_RULE_TYPE,
        REQUIRE_PROJECT_NAME,
        CREATE_BY,
        CREATE_TIME,
        UPDATE_BY,
        UPDATE_TIME,
        HEADER_NAME1,
        HEADER_NAME2
    </sql>

    <select id="query" resultMap="BaseResultMap">
        SELECT t1.DIRECTORY_CODE,
               t1.DIRECTORY_NAME,
               t1.EMR_RULE_TYPE,
               t2.ID,
               t2.REQUIRE_PROJECT_NAME,
               t2.UPDATE_TIME,
               t2.CREATE_TIME,
               t2.CREATE_BY,
               t2.UPDATE_BY,
               t2.HEADER_NAME1,
               t2.HEADER_NAME2,
               t2.HOSPITAL_PROJECT_NAME
        from DQM_EMR_DOCUMENT_DIRECTORY_CONFIGURATION t1
                 left join DQM_EMR_REQUIRE_PROJECT_DICTIONARY t2
                           on t1.DIRECTORY_CODE = t2.DIRECTORY_CODE and t1.DIRECTORY_NAME = t2.DIRECTORY_NAME and
                              t1.EMR_RULE_TYPE = t2.EMR_RULE_TYPE
        <where>
            t1.EMR_RULE_TYPE is not null and length(t1.DIRECTORY_CODE) > 5
            <if test="directoryCode != null and directoryCode != ''">
                and t1.DIRECTORY_CODE = #{directoryCode}
            </if>
            <if test="directoryName != null and directoryName != ''">
                and t1.DIRECTORY_NAME = #{directoryName}
            </if>
            <if test="emrRuleType != null and emrRuleType != ''">
                and t1.EMR_RULE_TYPE = #{emrRuleType}
            </if>
            <if test="_databaseId == 'mysql' and requireProjectName != null and requireProjectName != ''">
                and t2.REQUIRE_PROJECT_NAME like CONCAT('%'
                    , #{requireProjectName}
                    , '%')
            </if>
            <if test="_databaseId == 'mysql' and startTime != null and requireProjectName != ''">
                and t2.UPDATE_TIME <![CDATA[ >= ]]> str_to_date(#{startTime}
                    , '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="_databaseId == 'mysql' and endTime != null and endTime != ''">
                and t2.UPDATE_TIME <![CDATA[ <= ]]> str_to_date(#{endTime}
                    , '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="_databaseId == 'oracle' and requireProjectName != null and requireProjectName != ''">
                and t2.REQUIRE_PROJECT_NAME like '%'|| #{requireProjectName} || '%'
            </if>
            <if test="_databaseId == 'oracle' and startTime != null and requireProjectName != ''">
                and t2.UPDATE_TIME <![CDATA[ >= ]]> to_date(#{startTime}
                    , 'yyyy/mm/dd hh24:mi:ss')
            </if>
            <if test="_databaseId == 'oracle' and endTime != null and endTime != ''">
                and t2.UPDATE_TIME <![CDATA[ <= ]]> to_date(#{endTime}
                    , 'yyyy/mm/dd hh24:mi:ss')
            </if>
        </where>
        ORDER BY t1.DIRECTORY_CODE
    </select>
</mapper>