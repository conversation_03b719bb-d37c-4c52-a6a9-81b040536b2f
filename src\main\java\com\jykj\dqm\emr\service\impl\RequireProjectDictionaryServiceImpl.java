package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.RequireProjectDictionaryMapper;
import com.jykj.dqm.emr.entity.RequireProjectDictionary;
import com.jykj.dqm.emr.entity.RequireProjectDictionaryQuery;
import com.jykj.dqm.emr.service.RequireProjectDictionaryService;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Pattern;

/**
 * 要求项目字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/20 16:25:51
 */
@Service
public class RequireProjectDictionaryServiceImpl extends ServiceImpl<RequireProjectDictionaryMapper, RequireProjectDictionary> implements RequireProjectDictionaryService {
    @Autowired
    private RequireProjectDictionaryMapper requireProjectDictionaryMapper;

    @Override
    public R add(RequireProjectDictionary requireProjectDictionary) {
        String requireProjectName = requireProjectDictionary.getRequireProjectName();
        String hospitalProjectName = requireProjectDictionary.getHospitalProjectName();
        checkTwoProjectName(hospitalProjectName, requireProjectName);
        requireProjectDictionary.setRequireProjectName(StringUtil.removeSpacesFromText(requireProjectName));
        requireProjectDictionary.setHospitalProjectName(StringUtil.removeSpacesFromText(hospitalProjectName));
        this.save(requireProjectDictionary);
        return RUtil.success();
    }

    private void checkTwoProjectName(String hospitalProjectName, String requireProjectName) {
        if (StrUtil.isNotBlank(hospitalProjectName) && StrUtil.isNotBlank(requireProjectName)) {
            Pattern pattern = Pattern.compile(Constant.REQUIRE_PROJECT_NAME_SEPARATOR + "(?!([^（]*）[^（]*$))");
            // 使用正则表达式分割字符串
            String[] hospitalProjectNames = pattern.split(hospitalProjectName);
            String[] requireProjectNames = pattern.split(requireProjectName);
            if (hospitalProjectNames.length != requireProjectNames.length) {
                throw new BusinessException("医院项目名称与要求项目名称数量不匹配");
            }
        }
    }

    @Override
    public R update(RequireProjectDictionary requireProjectDictionary) {
        String requireProjectName = requireProjectDictionary.getRequireProjectName();
        String hospitalProjectName = requireProjectDictionary.getHospitalProjectName();
        checkTwoProjectName(hospitalProjectName, requireProjectName);
        requireProjectDictionary.setRequireProjectName(StringUtil.removeSpacesFromText(requireProjectName));
        requireProjectDictionary.setHospitalProjectName(StringUtil.removeSpacesFromText(hospitalProjectName));
        this.saveOrUpdate(requireProjectDictionary);
        return RUtil.success();
    }

    @Override
    public R delete(List<Long> ids) {
        this.removeBatchByIds(ids);
        return RUtil.success();
    }

    @Override
    public R query(RequireProjectDictionaryQuery requireProjectDictionaryQuery) {
        PageHelper.startPage(requireProjectDictionaryQuery.getPageNum(), requireProjectDictionaryQuery.getPageSize());
        if (StrUtil.isNotBlank(requireProjectDictionaryQuery.getEndTime())) {
            requireProjectDictionaryQuery.setEndTime(requireProjectDictionaryQuery.getEndTime() + " 23:59:59");
        }
        List<RequireProjectDictionary> list = requireProjectDictionaryMapper.query(requireProjectDictionaryQuery);
        PageInfo<RequireProjectDictionary> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }
}
