package com.jykj.dqm.quartz.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CronExpressionItem implements Serializable {
    private static final long serialVersionUID = 7593048211718934929L;
    private String cronExpression;
    private String cronDesc;
    private List<String> cronExecTimes;
}
