package com.jykj.dqm.config.cache;


import cn.dev33.satoken.stp.StpUtil;
import com.google.common.collect.Lists;
import com.jykj.dqm.auth.dao.SysUserRoleMapper;
import com.jykj.dqm.auth.entity.SysRole;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 缓存key生成策略keyGenerator，同时添加用户权限
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/12/2 11:29:00
 */
@Component
public class MyKeyGenerator implements KeyGenerator {
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public Object generate(Object target, Method method, Object... params) {
        List<String> list = Lists.newArrayList();
        ////用户权限标识（目前不需要根据角色区分缓存，需要时解开注释） todo
        //String roleFlag = getRoleFlag();
        //if (Strings.isNotBlank(roleFlag)) {
        //    list.add(roleFlag);
        //}
        //方法名称
        list.add(method.getName());
        //参数
        if (params.length > 0) {
            Object param = params[0];
            // 参数为map自定义key=方法名+map的key-value值
            if (param instanceof Map) {
                Map<String, Object> map = (Map<String, Object>) param;
                for (String key : map.keySet()) {
                    list.add(String.format("%s-%s", key, map.get(key)));
                }
            } else {
                for (Object o : params) {
                    list.add(String.valueOf(o));
                }
            }
        }

        return StringUtils.join(list.toArray(), ":");
    }

    /**
     * 计算数据权限的唯一标识
     *
     * @return 权限名称字符串，逗号分割
     * <AUTHOR>
     */
    private String getRoleFlag() {
        try {
            // 获取角色
            String userId = StpUtil.getLoginIdAsString();
            List<SysRole> sysUserRoles;
            Object cacheList = redisTemplate.opsForValue().get("EMRMAUTH:RoleInfos:" + userId);
            if (cacheList != null) {
                sysUserRoles = (List<SysRole>) cacheList;
            } else {
                sysUserRoles = sysUserRoleMapper.getAllRoleInfoList(userId);
                redisTemplate.opsForValue().set("EMRMAUTH:RoleInfos:" + userId, sysUserRoles);
            }
            if (sysUserRoles == null || sysUserRoles.size() == 0) {
                return null;
            }
            List<String> roleNameList = sysUserRoles.stream().map(sysRole -> sysRole.getRoleName()).collect(Collectors.toList());
            String roleNames = String.join("_", roleNameList);
            return roleNames;
        } catch (Exception e) {
            return null;
        }
    }
}
