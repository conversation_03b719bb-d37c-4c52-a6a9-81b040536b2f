package com.jykj.dqm.auth.controller;

import com.jykj.dqm.auth.entity.SysPermission;
import com.jykj.dqm.auth.service.SysPermissionService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Api(tags = {"权限管理"})
@RestController
@RequestMapping("/sysPermission")
public class SysPermissionController {
    public static final String MODUE_NAME = "权限管理";

    @Autowired
    private SysPermissionService sysPermissionService;

    /**
     * 新增权限
     *
     * @param sysPermission SysPermission
     * @return Result
     */
    @ApiOperation(value = "添加权限", notes = "")
    @LogRemark(operate = "添加权限", module = MODUE_NAME)
    @PostMapping("/add")
    public R add(@RequestBody SysPermission sysPermission) {
        return sysPermissionService.add(sysPermission);
    }

    /**
     * 修改权限
     *
     * @param sysPermission SysPermission
     * @return Result
     */
    @ApiOperation(value = "修改权限", notes = "")
    @LogRemark(operate = "修改权限", module = MODUE_NAME)
    @PostMapping("/update")
    public R update(@RequestBody SysPermission sysPermission) {
        return RUtil.success(sysPermissionService.updateById(sysPermission));
    }

    /**
     * 删除权限
     *
     * @param sysPermissionId 系统权限ID
     * @return Result
     */
    @ApiOperation(value = "删除权限", notes = "")
    @LogRemark(operate = "删除权限", module = MODUE_NAME)
    @PostMapping("/delete")
    public R delete(String sysPermissionId) {
        return RUtil.success(sysPermissionService.removeById(sysPermissionId));
    }

    /**
     * 查询所有权限
     *
     * @return Result
     */
    @ApiOperation(value = "查询所有权限", notes = "")
    @GetMapping("/list")
    public R list() {
        return sysPermissionService.queryList();
    }

    /**
     * 通过ID查询权限
     *
     * @param sysPermissionId 系统权限ID
     * @return Result
     */
    @ApiOperation(value = "通过ID查询权限", notes = "")
    @GetMapping("/queryById")
    public R queryById(String sysPermissionId) {
        return RUtil.success(sysPermissionService.getById(sysPermissionId));
    }

    /**
     * 根据token获取权限
     *
     * @param token 凭证
     * @return Result
     */
    @ApiOperation(value = "根据token获取权限", notes = "")
    @GetMapping("/getPermission")
    public R getPermission(@RequestHeader("token") String token) {
        return sysPermissionService.getPermission(token);
    }

    /**
     * 根据角色Id获取权限
     *
     * @param roleId 角色Id
     * @return List<PermissionVo>
     */
    @ApiOperation(value = "根据角色Id获取权限", notes = "")
    @GetMapping("/getPermissionByRoleId")
    public R getPermissionByRoleId(String roleId) {
        return RUtil.success(sysPermissionService.getPermissionByRoleId(roleId));
    }
}
