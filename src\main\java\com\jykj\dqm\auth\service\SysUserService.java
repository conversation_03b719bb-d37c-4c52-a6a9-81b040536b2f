package com.jykj.dqm.auth.service;


import com.jykj.dqm.auth.entity.LogginForm;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.entity.SysUserDTO;
import com.jykj.dqm.auth.entity.UserQueryForm;
import com.jykj.dqm.common.R;

import java.util.Map;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public interface SysUserService {
    /**
     * 登录
     *
     * @param loggingForm LoggingForm
     * @return 登录结果
     * <AUTHOR>
     */
    R login(LogginForm loggingForm);

    /**
     * 登出
     *
     * @param token token凭证
     * @return Result
     * <AUTHOR>
     */
    R loginOut(String token);

    /**
     * 添加用户
     *
     * @param sysUser SysUser
     */
    R add(SysUser sysUser);

    /**
     * 更新用户状态
     *
     * @param sysUser SysUser
     */
    R updateUserStatus(SysUser sysUser);

    /**
     * 更新用户信息
     *
     * @param sysUser SysUser
     * @return Result
     */
    R updateUser(SysUser sysUser);

    /**
     * 删除用户
     *
     * @param sysUser SysUser
     * @return Result
     */
    R deleteUser(SysUser sysUser);

    /**
     * 查询用户
     *
     * @param userQueryForm UserQueryForm
     * @return List<SysUser>
     */
    R queryList(UserQueryForm userQueryForm);

    /**
     * 重置密码
     *
     * @param sysUser SysUser
     */
    R updateUserPassword(SysUser sysUser);

    /**
     * 获取用户信息
     *
     * @param token token
     * @return Result
     */
    R getUserInfo(String token);

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    R getUserName();

    /**
     * 通过用户ID，获取用户名
     *
     * @param id 用户ID
     * @return 用户名
     */
    String getUserNameById(String id);

    /**
     * 检查用户信息的状态，包括密码是否过期，账号是否停用
     *
     * @return 登录结果
     * <AUTHOR>
     */
    R checkUserInfoStatus();

    /**
     * 检查token是否过期,没过期就重新获取Token
     *
     * @return token
     * <AUTHOR>
     */
    R checkToken();

    /**
     * 根据用户账号获取登录方式信息
     *
     * @param loginId 用户账号
     * @return Result 返回验证方式（0：帐密；1：验证码 ；2：账密+验证码）;如果是1,2，会返回脱敏的手机号；如果是0，会返回验证码Base64编码
     */
    R<Map<String, Object>> getIdentifyInfo(String loginId);

    /**
     * 根据用户账号发送短信验证码
     *
     * @param loginId 用户账号
     * @return
     * <AUTHOR>
     */
    R sendPin(String loginId);

    /**
     * 检查验证码是否正确
     *
     * @param pin  验证码
     * @param uuid redis缓存验证码唯一标识
     * @return Result
     * <AUTHOR>
     */
    R checkPin(String pin, String uuid);

    /**
     * 校验用户信息，获取token
     *
     * @param wxAccount 用户企业微信号
     * @return token
     * <AUTHOR>
     */
    R validateWxInterface(String wxAccount);

    /**
     * 根据获取静态验证码
     *
     * @return Result 验证码Base64编码
     * <AUTHOR>
     */
    R getImagePin();

    /**
     * 通过旧密码更新密码
     *
     * @param sysUser SysUserDTO
     * @return Result
     * <AUTHOR>
     */
    R updateUserPasswordByOldPS(SysUserDTO sysUser);

    R queryUserInfoList(UserQueryForm userQueryForm);
}
