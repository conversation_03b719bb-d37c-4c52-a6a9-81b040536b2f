package com.jykj.dqm.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.auth.entity.SysRolePermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Mapper
public interface SysRolePermissionMapper extends BaseMapper<SysRolePermission> {
    /**
     * 新增角色权限
     *
     * @param sysRolePermission SysRolePermission
     */
    void add(@Param("sysRolePermission") SysRolePermission sysRolePermission);

    /**
     * 查询角色权限
     *
     * @return List<SysRolePermission>
     */
    List<SysRolePermission> queryList();

    /**
     * 通过角色Id删除角色权限
     *
     * @param roleId 角色Id
     */
    void deleteByRoleId(@Param("roleId") String roleId);
}
