package com.jykj.dqm.system.service;

import com.jykj.dqm.common.OperLog;
import com.jykj.dqm.common.OperLogForm;
import com.jykj.dqm.common.R;

public interface OperLogService {
    /**
     * 查询操作日志接口
     *
     * @param operLogQuery OperLogForm
     * @return Result
     * <AUTHOR>
     */
    R queryLog(OperLogForm operLogQuery);

    /**
     * 插入操作日志
     *
     * @param operLog OperLog
     * @return Result
     * <AUTHOR>
     */
    R insertLog(OperLog operLog);
}
