package com.jykj.dqm.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 系统码值内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:53:39
 */
@ApiModel(value = "系统码值内容")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "SYS_CODETAB_CONTENT")
public class SysCodetabContent implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 类型编码
     */
    @TableField(value = "TYPE_CODE")
    @ApiModelProperty(value = "类型编码")
    private String typeCode;

    /**
     * 键
     */
    @TableField(value = "CONTENT_KEY")
    @ApiModelProperty(value = "键")
    private String contentKey;

    /**
     * 值
     */
    @TableField(value = "CONTENT_VALUE")
    @ApiModelProperty(value = "值")
    private String contentValue;

    /**
     * 内容描述
     */
    @TableField(value = "CONTENT_DESC")
    @ApiModelProperty(value = "内容描述")
    private String contentDesc;

    /**
     * 排序
     */
    @TableField(value = "CONTENT_SEQ")
    @ApiModelProperty(value = "排序")
    private Integer contentSeq;

    /**
     * 系统编码
     */
    @TableField(value = "SYS_ID")
    @ApiModelProperty(value = "系统编码", hidden = true)
    private String sysId = Constant.SYS_NAME;

    /**
     * 预留字段1
     */
    @TableField(value = "DATA1")
    @ApiModelProperty(value = "预留字段1")
    private String data1;

    /**
     * 预留字段2
     */
    @TableField(value = "DATA2")
    @ApiModelProperty(value = "预留字段2")
    private String data2;

    /**
     * 预留字段3
     */
    @TableField(value = "DATA3")
    @ApiModelProperty(value = "预留字段3")
    private String data3;


    /**
     * 预留字段4
     */
    @TableField(value = "DATA4")
    @ApiModelProperty(value = "预留字段4")
    private String data4;


    /**
     * 预留字段5
     */
    @TableField(value = "DATA5")
    @ApiModelProperty(value = "预留字段5")
    private String data5;
}