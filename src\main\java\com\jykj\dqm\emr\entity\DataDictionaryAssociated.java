package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据字典关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/11/1 10:03:41
 */
@ApiModel(description = "数据字典关联关系")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DATA_DICTIONARY_ASSOCIATED")
public class DataDictionaryAssociated {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 数据字典目录配置表的目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "数据字典目录配置表的目录编码")
    private String directoryCode;

    /**
     * 数据字典目录配置表的目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "数据字典目录配置表的目录名称")
    private String directoryName;

    /**
     * 数据字典目录配置表的目录ID
     */
    @TableField(value = "DIRECTORY_ID")
    @ApiModelProperty(value = "数据字典目录配置表的目录ID")
    private String directoryId;

    /**
     * 关联表的目录编码
     */
    @TableField(value = "ASSOCIATED_DIRECTORY_CODE")
    @ApiModelProperty(value = "关联表的目录编码")
    private String associatedDirectoryCode;

    /**
     * 关联表的目录名称
     */
    @TableField(value = "ASSOCIATED_DIRECTORY_NAME")
    @ApiModelProperty(value = "关联表的目录名称")
    private String associatedDirectoryName;

    /**
     * 关联表的目录规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @TableField(value = "ASSOCIATED_EMR_RULE_TYPE")
    @ApiModelProperty(value = "关联表的目录规则类型（1：一致性、2：完整性、3：整合性、4：及时性）")
    private String associatedEmrRuleType;

    /**
     * 要求项目名称
     */
    @TableField(value = "REQUIRE_PROJECT_NAME")
    @ApiModelProperty(value = "要求项目名称")
    private String requireProjectName;

    /**
     * 关联数据的所属类别(0:总记录数；1：满足条件记录数)
     */
    @TableField(value = "ASSOCIATED_DATA_CATEGORY")
    @ApiModelProperty(value = "关联数据的所属类别(0:总记录数；1：满足条件记录数)")
    private String associatedDataCategory;

    /**
     * 运算符（0：+；1：-）
     */
    @TableField(value = "OPERATOR_TYPE")
    @ApiModelProperty(value = "运算符（0：+；1：-）")
    private String operatorType;


    /**
     * 数据类型（0：总记录数；1：符合要求的记录数）
     */
    @TableField(value = "DATA_TYPE")
    @ApiModelProperty(value = "数据类型（0：总记录数；1：符合要求的记录数）")
    private String dataType = "0";
}