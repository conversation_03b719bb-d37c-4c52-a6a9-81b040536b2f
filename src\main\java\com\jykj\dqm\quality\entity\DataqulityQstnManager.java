package com.jykj.dqm.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 质量问题管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 10:33:34
 */
@ApiModel(value = "质量问题管理")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DATAQULITY_QSTN_MANAGER")
public class DataqulityQstnManager implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "PK_ID", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Integer pkId;

    /**
     * 问题名称
     */
    @TableField(value = "DATA_QLTY_QSTN_NM")
    @ApiModelProperty(value = "问题名称")
    private String dataQltyQstnNm;

    /**
     * 问题产生系统编码
     */
    @TableField(value = "QSTN_PRDUS_SYS_CD")
    @ApiModelProperty(value = "问题产生系统编码")
    private String qstnPrdusSysCd;

    /**
     * 问题产生系统名称
     */
    @TableField(value = "QSTN_PRDUS_SYS_NM")
    @ApiModelProperty(value = "问题产生系统名称")
    private String qstnPrdusSysNm;

    /**
     * 数据库名称
     */
    @TableField(value = "DB_NM")
    @ApiModelProperty(value = "数据库名称")
    private String dbNm;

    /**
     * 检核规则编号（关联规则表获取规则名称、规则大类、规则小类、表名、字段名、检查SQL或其他附加信息）
     */
    @TableField(value = "DATA_QLTY_RVW_RULE_NO")
    @ApiModelProperty(value = "检核规则编号（关联规则表获取规则名称、规则大类、规则小类、表名、字段名、检查SQL或其他附加信息）")
    private String dataQltyRvwRuleNo;

    /**
     * 数据总记录数量
     */
    @TableField(value = "DATA_TOTAL_NUM")
    @ApiModelProperty(value = "数据总记录数量")
    private Integer dataTotalNum;

    /**
     * 数据质量问题数量
     */
    @TableField(value = "DATA_QSTN_NUM")
    @ApiModelProperty(value = "数据质量问题数量")
    private Integer dataQstnNum;

    /**
     * 问题检测时间
     */
    @TableField(value = "QSTN_CHECK_TIME")
    @ApiModelProperty(value = "问题检测时间")
    private Date qstnCheckTime;

    /**
     * 问题分类代码 （1、存量数据问题 2、新增数据问题）
     */
    @TableField(value = "QSTN_CLSF_CD")
    @ApiModelProperty(value = "问题分类代码 （1、存量数据问题 2、新增数据问题）")
    private String qstnClsfCd;

    /**
     * 问题原因分类代码（1、需求问题、2、程序缺陷问题、3、历史迁移问题、4、系统性能问题、5、人工操作问题、6、标准缺失问题、7、流程缺失问题、8、其他原因、9、非数据问题）
     */
    @TableField(value = "QSTN_RSN_CLSF_CD")
    @ApiModelProperty(value = "问题原因分类代码（1、需求问题、2、程序缺陷问题、3、历史迁移问题、4、系统性能问题、5、人工操作问题、6、标准缺失问题、7、流程缺失问题、8、其他原因、9、非数据问题）")
    private String qstnRsnClsfCd;

    /**
     * 整改方案类型代码（1、业务系统优化、2、存量数据修复、3、数据补录、4、新增或变更数据标准、5、流程制度优化）
     */
    @TableField(value = "RCTFCTN_SCHEM_TYP_CD")
    @ApiModelProperty(value = "整改方案类型代码（1、业务系统优化、2、存量数据修复、3、数据补录、4、新增或变更数据标准、5、流程制度优化）")
    private String rctfctnSchemTypCd;

    /**
     * 整改意见
     */
    @TableField(value = "RCTFCTN_OPINIONS")
    @ApiModelProperty(value = "整改意见")
    private String rctfctnOpinions;

    /**
     * 问题整改状态代码（1、关闭、2、处理中、3、待处理）
     */
    @TableField(value = "QSTN_RCTFCTN_STUS_CD")
    @ApiModelProperty(value = "问题整改状态代码（1、关闭、2、处理中、3、待处理）")
    private String qstnRctfctnStusCd;

    /**
     * 问题处理时间
     */
    @TableField(value = "QSTN_HANDLE_TIME")
    @ApiModelProperty(value = "问题处理时间")
    private Date qstnHandleTime;

    /**
     * 推送消息接收方
     */
    @TableField(value = "PUSH_MESSAGE_RECEIVER")
    @ApiModelProperty(value = "推送消息接收方（JsonString）", hidden = true)
    private String pushMessageReceiver;

    @TableField(exist = false)
    @ApiModelProperty(value = "推送消息接收方实体类")
    private List<PushMsgReceiver> pushMessageReceivers;

    @TableField(exist = false)
    @ApiModelProperty(value = "操作类型，1关闭，2处理，3撤销关闭，4撤销处理")
    private String operationType;

    /**
     * 总数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "首页指标")
    private int total;

    private static final long serialVersionUID = 1L;
}