package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 规则sql生成基类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:13:12
 */
@Service
public class BaseCheckSqlGenerator {
    protected CheckSqlDTO addTotalAndQstCheckSql(String table, String checkRuleWhere, StringBuilder qstDetailFieldSql) {
        //总数SQL
        String totalNmSql = "select count(*) from " + table + " where 1=1 ";
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            totalNmSql = totalNmSql + " and " + checkRuleWhere;
        }
        //问题SQL
        String questionNmSql = "select count(*) from (" + qstDetailFieldSql + " ) T";
        CheckSqlDTO checkSqlDTO = new CheckSqlDTO();
        checkSqlDTO.setTotalNmSql(totalNmSql);
        checkSqlDTO.setQuestionNmSql(questionNmSql);
        checkSqlDTO.setQstDetailFieldSql(qstDetailFieldSql.toString());
        return checkSqlDTO;
    }

    /**
     * sql获取小数点后面的字符串长度
     *
     * @param column 字段
     * @param dbType 数据库类型
     * @return 数点后面的字符串长度
     * <AUTHOR>
     */
    protected String getStrAfterDecimalPointLength(String column, String dbType) {
        String str = "";
        if ("ORACLE".equalsIgnoreCase(dbType) || "TERADATA".equals(dbType)) {
            str = "substr(" + column + ",instr(" + column + ",'.'))";
            //处理没有'.'的场景
            str = "(case instr(" + column + ",'.') when 0 then 0 ELSE " + getLengthSql(str, dbType) + " end)";
        } else if ("MYSQL".equalsIgnoreCase(dbType)) {
            str = "SUBSTRING_INDEX(" + column + ",'.',-1)";
            str = "(case instr(" + column + ",'.') when 0 then 0 ELSE " + getLengthSql(str, dbType) + " end)";
        } else if ("SQLSERVER".equalsIgnoreCase(dbType)) {
            //select SUBSTRING(PRICE, CHARINDEX('.',PRICE)+1, LEN(PRICE)-CHARINDEX('.',PRICE)) FROM SYS_USER
            str = "SUBSTRING(CAST(" + column + " AS varchar(50)), CHARINDEX('.'," + column + ")+1, LEN(" + column + ")-CHARINDEX('.'," + column + "))";
            str = "(case CHARINDEX(CAST('.'," + column + " AS varchar(50))) when 0 then 0 ELSE " + getLengthSql(str, dbType) + " end)";
        } else if ("HIVE".equalsIgnoreCase(dbType)) {
            /*
              !!!考虑了没有小数点的场景
              select case locate('.', '12.2')
                when  0  then  0  ELSE split('12.2','\\.')[1]
                end
                from default.t_test
             */
            str = "(case locate('.', " + column + ") when  0  then 0  ELSE split(" + column + ",'\\.')[1] end)";
        }
        return str;
    }

    /**
     * 拼接获取长度的SQL
     *
     * @param column 字段
     * @param dbType 数据库类型
     * @return 获取长度的SQL
     * <AUTHOR>
     */
    protected String getLengthSql(String column, String dbType) {
        String str = "";
        if ("ORACLE".equalsIgnoreCase(dbType) || "TERADATA".equalsIgnoreCase(dbType)) {
            str = "LENGTH(" + column + ")";
        } else if ("MYSQL".equalsIgnoreCase(dbType)) {
            str = "LENGTH(" + column + ")";
        } else if ("SQLSERVER".equalsIgnoreCase(dbType)) {
            str = "LEN(" + column + ")";
        } else if ("HIVE".equalsIgnoreCase(dbType)) {
            str = "LENGTH('' || " + column + ")";
        }
        return str;
    }

    /**
     * 获取日期装换方法
     *
     * @param dbType    数据库类型
     * @param dateValue 时间字段值
     * @return 日期装换方法
     * <AUTHOR>
     */
    public String getSqlDateFunction(String dbType, String dateValue) {
        String dateFun = "'" + dateValue + "'";
        if ("ORACLE".equals(dbType) || "TERADATA".equalsIgnoreCase(dbType)) {
            dateFun = "to_date('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        } else if ("MYSQL".equalsIgnoreCase(dbType)) {
            dateFun = "str_to_date('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        } else if ("SQLSERVER".equalsIgnoreCase(dbType)) {
            dateFun = "Cast('" + dateValue + "' as datetime)";
        } else if ("HIVE".equalsIgnoreCase(dbType)) {
            dateFun = "to_date('" + dateValue + "')";
        } else if ("PRESTO".equalsIgnoreCase(dbType)) {
            dateFun = "date_parse('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        }
        return dateFun;
    }

    private String getDateFormatStyle(String dbType, int len) {
        String style = "";
        if ("ORACLE".equals(dbType) || "TERADATA".equals(dbType)) {
            style = "'yyyy-MM-dd hh24:mi:ss'";
            if (len < 11) {
                style = "'yyyy-MM-dd'";
            }
        } else if ("MYSQL".equalsIgnoreCase(dbType) || "PRESTO".equalsIgnoreCase(dbType)) {
            style = "'%Y-%m-%d %T'";
            if (len < 11) {
                style = "'%Y-%m-%d'";
            }
        }
        return style;
    }


    /**
     * 获取日期转String方法
     *
     * @param dbType 数据库类型
     * @param column 字段
     * @return 期转String方法
     * <AUTHOR>
     */
    protected String getDate2StrFunction(String dbType, String column) {
        String dateFun = "";
        if ("ORACLE".equalsIgnoreCase(dbType) || "TERADATA".equalsIgnoreCase(dbType)) {
            //to_char(column,'yyyy/MM/dd')
            dateFun = "to_char(" + column + ",'yyyy/MM/dd')";
        } else if ("MYSQL".equalsIgnoreCase(dbType)) {
            // DATE_FORMAT(create_time,'%Y/%m/%d')
            dateFun = "DATE_FORMAT(" + column + ",'%Y/%m/%d')";
        } else if ("SQLSERVER".equalsIgnoreCase(dbType)) {
            //CONVERT(varchar(100), GETDATE(), 111)
            dateFun = "CONVERT(varchar(100)," + column + ",111)";
        } else if ("HIVE".equalsIgnoreCase(dbType)) {
            dateFun = "DATE_FORMAT(" + column + ",'yyyy/MM/dd')";
        }
        return dateFun;
    }

    /**
     * 连接字段
     *
     * @param dbType 数据库类型
     * @param fields 多个字段，逗号分隔
     * @return 拼接后的字段
     * <AUTHOR>
     */
    protected String getConcatField(String dbType, String fields) {
        if (!fields.contains(",")) {
            return fields;
        }
        String fieldStr = "";
        if ("ORACLE".equalsIgnoreCase(dbType) || "TERADATA".equalsIgnoreCase(dbType) || "HIVE".equalsIgnoreCase(dbType)) {
            fieldStr = fields.replace(",", "||");
        } else if ("MYSQL".equalsIgnoreCase(dbType)) {
            fieldStr = "CONCAT_WS(','," + fields + ")";
        } else if ("SQLSERVER".equalsIgnoreCase(dbType)) {
            String[] fieldArray = fields.split(",");
            StringBuilder sb = new StringBuilder();
            for (String filed : fieldArray) {
                sb.append("CAST('" + filed + "' as varchar(100))" + "+'_'+");
            }
            fieldStr = sb.length() == 0 ? "" : sb.substring(0, sb.lastIndexOf("+'_'+"));
        }
        return fieldStr;
    }

    /**
     * 获取where条件
     *
     * @param dataqualityCheckRule DataqualityCheckRuleDTO
     * @return where条件
     * <AUTHOR>
     */
    protected String getWhereStr(DataqualityCheckRuleDTO dataqualityCheckRule) {
        String checkRuleWhere = dataqualityCheckRule.getCheckRuleWhere();
        if (StringUtils.isNotEmpty(checkRuleWhere)) {
            checkRuleWhere = checkRuleWhere.trim();
        }
        return checkRuleWhere;
    }
}
