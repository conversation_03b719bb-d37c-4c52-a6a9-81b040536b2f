<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quality.dao.DataqualityCheckRuleMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.quality.entity.DataqualityCheckRule">
        <!--@mbg.generated-->
        <!--@Table DQM_DATAQUALITY_CHECK_RULE-->
        <id column="CHECK_RULE_ID" jdbcType="INTEGER" property="checkRuleId"/>
        <id column="CHECK_RULE_NAME" jdbcType="VARCHAR" property="checkRuleName"/>
        <result column="CHECK_RULE_DESC" jdbcType="VARCHAR" property="checkRuleDesc"/>
        <result column="CHECK_RULE_STATUS" jdbcType="CHAR" property="checkRuleStatus"/>
        <result column="STAND_TITLE" jdbcType="VARCHAR" property="standTitle"/>
        <result column="SEND_MAIL" jdbcType="VARCHAR" property="sendMail"/>
        <result column="QST_TYPE" jdbcType="VARCHAR" property="qstType"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="COMM_DATE" jdbcType="VARCHAR" property="commDate"/>
        <result column="STOP_DATE" jdbcType="VARCHAR" property="stopDate"/>
        <result column="CHECK_RULE_SOURCE" jdbcType="VARCHAR" property="checkRuleSource"/>
        <result column="RULE_NO" jdbcType="VARCHAR" property="ruleNo"/>
        <result column="CHECK_RULE_FATHER_TYPE" jdbcType="VARCHAR" property="checkRuleFatherType"/>
        <result column="CHECK_RULE_TYPE" jdbcType="VARCHAR" property="checkRuleType"/>
        <result column="RULE_SORC_SYS_NM" jdbcType="VARCHAR" property="ruleSorcSysNm"/>
        <result column="CHECK_DATASOURCE" jdbcType="VARCHAR" property="checkDatasource"/>
        <result column="SYS_CODE" jdbcType="VARCHAR" property="sysCode"/>
        <result column="SYS_NAME" jdbcType="VARCHAR" property="sysName"/>
        <result column="DB_NM" jdbcType="VARCHAR" property="dbNm"/>
        <result column="CHECK_SCHEMA" jdbcType="VARCHAR" property="checkSchema"/>
        <result column="ST_CHECK_SCHEMA" jdbcType="VARCHAR" property="stCheckSchema"/>
        <result column="CHECK_RULE_TABLE_OR_VIEW" jdbcType="VARCHAR" property="checkRuleTableOrView"/>
        <result column="CHECK_RULE_TABLE_OR_VIEW_CN" jdbcType="VARCHAR" property="checkRuleTableOrViewCn"/>
        <result column="ST_CHECK_RULE_TABLE_OR_VIEW" jdbcType="VARCHAR" property="stCheckRuleTableOrView"/>
        <result column="ST_CHECK_RULE_TABLE_OR_VIEW_CN" jdbcType="VARCHAR" property="stCheckRuleTableOrViewCn"/>
        <result column="IS_TABLE_OR_VIEW" jdbcType="VARCHAR" property="isTableOrView"/>
        <result column="ST_IS_TABLE_OR_VIEW" jdbcType="VARCHAR" property="stIsTableOrView"/>
        <result column="CHECK_RULE_COLUMN" jdbcType="VARCHAR" property="checkRuleColumn"/>
        <result column="CHECK_RULE_COLUMN_CN" jdbcType="VARCHAR" property="checkRuleColumnCn"/>
        <result column="ST_CHECK_RULE_COLUMN" jdbcType="VARCHAR" property="stCheckRuleColumn"/>
        <result column="ST_CHECK_RULE_COLUMN_CN" jdbcType="VARCHAR" property="stCheckRuleColumnCn"/>
        <result column="CHECK_RULE_WHERE" jdbcType="VARCHAR" property="checkRuleWhere"/>
        <result column="PB_SUBSIDIARY_COLUMNS" jdbcType="VARCHAR" property="pbSubsidiaryColumns"/>
        <result column="CHECK_OBJECT_TYPE" jdbcType="VARCHAR" property="checkObjectType"/>
        <result column="MAX_VALUE" jdbcType="VARCHAR" property="maxValue"/>
        <result column="MIN_VALUE" jdbcType="VARCHAR" property="minValue"/>
        <result column="COLUMNS_LENGTH" jdbcType="VARCHAR" property="columnsLength"/>
        <result column="MAX_VALUE_CONTAIN" jdbcType="VARCHAR" property="maxValueContain"/>
        <result column="MIN_VALUE_CONTAIN" jdbcType="VARCHAR" property="minValueContain"/>
        <result column="ISNULLTAG" jdbcType="VARCHAR" property="isnulltag"/>
        <result column="RVW_STDD_SORC_CD" jdbcType="VARCHAR" property="rvwStddSorcCd"/>
        <result column="DT_EXEC_COND_DESCR" jdbcType="VARCHAR" property="dtExecCondDescr"/>
        <result column="PRCSN_EXEC_COND_CD" jdbcType="VARCHAR" property="prcsnExecCondCd"/>
        <result column="RESULT_SML_NMRL_DGIT" jdbcType="VARCHAR" property="resultSmlNmrlDgit"/>
        <result column="FIELD_DATA_TYP" jdbcType="VARCHAR" property="fieldDataTyp"/>
        <result column="CD_VAL_FIELD_NM" jdbcType="VARCHAR" property="cdValFieldNm"/>
        <result column="CALC_RULE_CD" jdbcType="VARCHAR" property="calcRuleCd"/>
        <result column="CD_VAL" jdbcType="VARCHAR" property="cdVal"/>
        <result column="QUESTION_NM_SQL" jdbcType="VARCHAR" property="questionNmSql"/>
        <result column="TOTAL_NM_SQL" jdbcType="VARCHAR" property="totalNmSql"/>
        <result column="PB_SUBSIDIARY_SQL" jdbcType="VARCHAR" property="pbSubsidiarySql"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        CHECK_RULE_ID,
        CHECK_RULE_NAME,
        CHECK_RULE_STATUS,
        CHECK_RULE_DESC,
        STAND_TITLE,
        SEND_MAIL,
        QST_TYPE,
        CREATE_DATE,
        UPDATE_DATE,
        COMM_DATE,
        STOP_DATE,
        CHECK_RULE_SOURCE,
        RULE_NO,
        CHECK_RULE_FATHER_TYPE,
        CHECK_RULE_TYPE,
        RULE_SORC_SYS_NM,
        CHECK_DATASOURCE,
        SYS_CODE,
        SYS_NAME,
        DB_NM,
        CHECK_SCHEMA,
        ST_CHECK_SCHEMA,
        CHECK_RULE_TABLE_OR_VIEW,
        CHECK_RULE_TABLE_OR_VIEW_CN,
        ST_CHECK_RULE_TABLE_OR_VIEW,
        ST_CHECK_RULE_TABLE_OR_VIEW_CN,
        IS_TABLE_OR_VIEW,
        ST_IS_TABLE_OR_VIEW,
        CHECK_RULE_COLUMN,
        CHECK_RULE_COLUMN_CN,
        ST_CHECK_RULE_COLUMN,
        ST_CHECK_RULE_COLUMN_CN,
        CHECK_RULE_WHERE,
        PB_SUBSIDIARY_COLUMNS,
        CHECK_OBJECT_TYPE,
        MAX_VALUE,
        MIN_VALUE,
        COLUMNS_LENGTH,
        MAX_VALUE_CONTAIN,
        MIN_VALUE_CONTAIN,
        ISNULLTAG,
        RVW_STDD_SORC_CD,
        DT_EXEC_COND_DESCR,
        PRCSN_EXEC_COND_CD,
        RESULT_SML_NMRL_DGIT,
        FIELD_DATA_TYP,
        CD_VAL_FIELD_NM,
        CALC_RULE_CD,
        CD_VAL,
        QUESTION_NM_SQL,
        TOTAL_NM_SQL,
        PB_SUBSIDIARY_SQL
    </sql>

    <select id="ruleList" resultType="com.jykj.dqm.quality.entity.DataqualityCheckRule">
        SELECT DQM_DATAQUALITY_CHECK_RULE.*,
        (SELECT COUNT(*) FROM DQM_DATAQUALITY_TASK_INSTANCE t1 WHERE t1.CHECK_RULE_ID = DQM_DATAQUALITY_CHECK_RULE.CHECK_RULE_ID) as recodeCount
        from DQM_DATAQUALITY_CHECK_RULE
        <where>
            SYS_CODE = #{sysCode}
            and DB_NM = #{dbNm}
            <if test="qstType != null and qstType != ''">
                AND QST_TYPE = #{qstType}
            </if>
            <if test="checkRuleTableOrView != null and checkRuleTableOrView != ''">
                AND CHECK_RULE_TABLE_OR_VIEW = #{checkRuleTableOrView}
            </if>
            <if test="checkRuleColumn != null and checkRuleColumn != ''">
                AND CHECK_RULE_COLUMN = #{checkRuleColumn}
            </if>
            <if test="checkRuleFatherType != null and checkRuleFatherType != ''">
                AND CHECK_RULE_FATHER_TYPE = #{checkRuleFatherType}
            </if>
            <if test="checkRuleType != null and checkRuleType != ''">
                AND CHECK_RULE_TYPE = #{checkRuleType}
            </if>
            <if test="_databaseId == 'mysql' and checkRuleName != null and checkRuleName != ''">
                AND CHECK_RULE_NAME like CONCAT('%', #{checkRuleName}, '%')
            </if>
            <if test="_databaseId == 'oracle' and checkRuleName != null and checkRuleName != ''">
                and CHECK_RULE_NAME like '%' || #{checkRuleName} || '%'
            </if>
            ORDER BY CHECK_RULE_ID DESC
        </where>
    </select>

    <select id="getTableFromAllExcludeNotContain" resultType="java.lang.String">
        SELECT DISTINCT NAME
        FROM DQM_METADATA_STRUCTURE_INFO
        WHERE DATA_SOURCE_ID = #{dataSourceId}

        <if test="_databaseId == 'mysql'">
            AND NAME NOT IN (SELECT DISTINCT IFNULL(TABLE_NAME, '1')
            FROM DQM_METADATA_STRUCTURE_CHOOSE_RESULT
            WHERE DATA_SOURCE_ID = #{dataSourceId})
        </if>
        <if test="_databaseId == 'oracle'">
            AND NAME NOT IN (SELECT DISTINCT NVL(TABLE_NAME, '1')
            FROM DQM_METADATA_STRUCTURE_CHOOSE_RESULT
            WHERE DATA_SOURCE_ID = #{dataSourceId})
        </if>
        <if test="_databaseId == 'mysql' and tableName != null and tableName != ''">
            AND NAME like CONCAT('%', #{tableName}, '%')
        </if>
        <if test="_databaseId == 'oracle' and tableName != null and tableName != ''">
            and NAME like '%' || #{tableName} || '%'
        </if>
        ORDER BY NAME
    </select>

    <select id="getTableFromChooseResult" resultType="java.lang.String">
        SELECT DISTINCT TABLE_NAME
        FROM DQM_METADATA_STRUCTURE_CHOOSE_RESULT
        WHERE DATA_SOURCE_ID = #{dataSourceId}
            AND CHOOSE_RESULT = '0'
        <if test="_databaseId == 'mysql' and tableName != null and tableName != ''">
            and TABLE_NAME like CONCAT('%', #{tableName}, '%')
        </if>
        <if test="_databaseId == 'oracle' and tableName != null and tableName != ''">
            and TABLE_NAME like '%' || #{tableName} || '%'
        </if>
        ORDER BY TABLE_NAME
    </select>

    <select id="getTableField" resultType="java.util.Map">
        SELECT NAME "fieldName", REMARKS "fieldNameCn", TYPE "fieldType", LENGTH "fieldlength"
        FROM DQM_METADATA_STRUCTURE_DETAIL
        WHERE DATA_SOURCE_ID = #{dataSourceId}
        <if test="tableName != null and tableName != ''">
            and TABLE_NAME = #{tableName}
        </if>
        <if test="_databaseId == 'mysql' and fieldName != null and fieldName != ''">
            and NAME like CONCAT('%', #{fieldName}, '%')
        </if>
        <if test="_databaseId == 'oracle' and fieldName != null and fieldName != ''">
            and NAME like '%' || #{fieldName} || '%'
        </if>
    </select>

    <select id="getAllTypeTableNum" resultType="java.util.Map">
        SELECT CHOOSE_RESULT "type", COUNT(*) "total"
        FROM DQM_METADATA_STRUCTURE_CHOOSE_RESULT
        WHERE DATA_SOURCE_ID = #{dataSourceId}
        GROUP BY CHOOSE_RESULT
    </select>

    <select id="getContainTableNum" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM DQM_METADATA_STRUCTURE_CHOOSE_RESULT
        WHERE DATA_SOURCE_ID = #{dataSourceId}
          AND CHOOSE_RESULT = '0'
    </select>

    <select id="getDictTable" resultType="java.util.Map">
        SELECT DISTINCT TERM_ID "termId", TERM_NAME "termName", MAPPINGS_TABLE "mappingTabal"
        FROM DQM_DICT_MAPPINGS
        WHERE MAPPINGS_TABLE IS NOT NULL
        <if test="_databaseId == 'mysql' and tableName != null and tableName != ''">
            and MAPPINGS_TABLE like CONCAT('%', #{tableName}, '%')
        </if>
        <if test="_databaseId == 'oracle' and tableName != null and tableName != ''">
            and MAPPINGS_TABLE like '%' || #{tableName} || '%'
        </if>
    </select>

    <select id="getDictTableFiled" resultType="java.util.Map" databaseId="mysql">
        SELECT COLUMN_NAME              "fieldName",
               DATA_TYPE                "fieldType",
               COLUMN_COMMENT           "fieldNameCn",
               CHARACTER_MAXIMUM_LENGTH "fieldlength"
        FROM INFORMATION_SCHEMA.COLUMNS
        Where TABLE_NAME = 'DQM_DICT_MAPPINGS_CONTENT'
        <if test="fieldName != null and fieldName != ''">
            and COLUMN_NAME like CONCAT('%', #{fieldName}, '%')
        </if>
    </select>

    <select id="getDictTableFiled" resultType="java.util.Map" databaseId="oracle">
        SELECT t.column_name      "fieldName",
               t.data_type        "fieldType",
               t.DATA_LENGTH      "fieldlength",
               (SELECT comments
                FROM user_col_comments
                where table_name = 'DQM_DICT_MAPPINGS_CONTENT'
                  and OWNER = 'MIP'
                  and column_name = t.COLUMN_NAME
                  and Rownum = 1) "fieldNameCn"
        from all_tab_columns t
        where t.table_name = 'DQM_DICT_MAPPINGS_CONTENT'
            and t.OWNER = 'MIP'
        <if test="fieldName != null and fieldName != ''">
            and COLUMN_NAME like '%' || #{fieldName} || '%'
        </if>
    </select>

    <select id="getSysCheckRule" resultType="java.util.Map">
        SELECT SYS_CODE, SYS_NAME, COUNT(1) TOTAL
        FROM DQM_DATAQUALITY_CHECK_RULE
        GROUP BY SYS_CODE,SYS_NAME
        ORDER BY TOTAL DESC
    </select>
</mapper>