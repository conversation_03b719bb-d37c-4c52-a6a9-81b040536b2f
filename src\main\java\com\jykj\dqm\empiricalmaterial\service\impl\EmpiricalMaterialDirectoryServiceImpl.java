package com.jykj.dqm.empiricalmaterial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.empiricalmaterial.dao.EmpiricalMaterialDirectoryMapper;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectory;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectoryQuery;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectoryTree;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContent;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPath;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocation;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialDirectoryService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialEvaluationContentService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialFlowPathService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialTaskAllocationService;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryConfiguration;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryConfigurationService;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.NumberMyUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_DIRECTORY(实证材料目录)】的数据库操作Service实现
 * @createDate 2024-01-22 13:40:46
 */
@Service
public class EmpiricalMaterialDirectoryServiceImpl extends ServiceImpl<EmpiricalMaterialDirectoryMapper, EmpiricalMaterialDirectory>
        implements EmpiricalMaterialDirectoryService {
    @Autowired
    private EmpiricalMaterialEvaluationContentService evaluationContentService;

    @Autowired
    private EmpiricalMaterialTaskAllocationService empiricalMaterialTaskService;

    @Autowired
    private EmpiricalMaterialFlowPathService empiricalMaterialFlowPathService;

    @Autowired
    private DataDictionaryDirectoryConfigurationService dataDictionaryDirectoryConfigurationService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R add(EmpiricalMaterialDirectory empiricalMaterialDirectory) {
        dealSerialNum(empiricalMaterialDirectory);
        this.save(empiricalMaterialDirectory);
        //评价内容
        addEvaluationContents(empiricalMaterialDirectory);
        //检测-如果是一级二级标题，需要创建病历目录的一级二级
        if (empiricalMaterialDirectory.getDirectoryCode().length() <= 5) {
            DataDictionaryDirectoryConfiguration mapped = MapperUtils.INSTANCE.map(DataDictionaryDirectoryConfiguration.class, empiricalMaterialDirectory);
            mapped.setId(empiricalMaterialDirectory.getId());
            mapped.setDirectoryType("2");
            dataDictionaryDirectoryConfigurationService.addBase(mapped);
        }
        return RUtil.success();
    }

    private void dealSerialNum(EmpiricalMaterialDirectory empiricalMaterialDirectory) {
        //处理序号
        String directoryCode = empiricalMaterialDirectory.getDirectoryCode();
        String[] titleNums = directoryCode.split("\\.");
        if (titleNums.length == 1) {
            //一级标题
            empiricalMaterialDirectory.setSerialNum(NumberMyUtil.intToChinese(Integer.parseInt(titleNums[0])));
        }
        if (titleNums.length == 2) {
            //二级标题
            empiricalMaterialDirectory.setSerialNum(Integer.parseInt(titleNums[1]) + "");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(EmpiricalMaterialDirectory empiricalMaterialDirectory) {
        dealSerialNum(empiricalMaterialDirectory);
        EmpiricalMaterialDirectory dbData = this.getById(empiricalMaterialDirectory.getId());
        this.updateById(empiricalMaterialDirectory);
        //评价内容
        addEvaluationContents(empiricalMaterialDirectory);
        //修改目录编码或目录名称导致的关联修改
        if (StrUtil.isNotBlank(empiricalMaterialDirectory.getDirectoryCode())
                && StrUtil.isNotBlank(empiricalMaterialDirectory.getDirectoryCode())
                && (!dbData.getDirectoryCode().equals(empiricalMaterialDirectory.getDirectoryCode()) || !dbData.getDirectoryName().equals(empiricalMaterialDirectory.getDirectoryName()))) {
            dealUpdateDirectoryCodeOrName(empiricalMaterialDirectory, dbData);
        }
        //检测-如果是一级二级标题，需要创建病历目录的一级二级
        if (empiricalMaterialDirectory.getDirectoryCode().length() <= 5) {
            DataDictionaryDirectoryConfiguration mapped = MapperUtils.INSTANCE.map(DataDictionaryDirectoryConfiguration.class, empiricalMaterialDirectory);
            mapped.setDirectoryType("2");
            dataDictionaryDirectoryConfigurationService.batchUpdateBase(Arrays.asList(mapped));
        }
        return RUtil.success();
    }

    private void addEvaluationContents(EmpiricalMaterialDirectory empiricalMaterialDirectory) {
        if (CollUtil.isNotEmpty(empiricalMaterialDirectory.getEvaluationContents())) {
            //先删除
            evaluationContentService.remove(Wrappers.lambdaQuery(EmpiricalMaterialEvaluationContent.class)
                    .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                    .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, empiricalMaterialDirectory.getDirectoryName()));
            List<EmpiricalMaterialEvaluationContent> evaluationContents = empiricalMaterialDirectory.getEvaluationContents();
            if (CollUtil.isNotEmpty(evaluationContents)) {
                int i = 1;
                for (EmpiricalMaterialEvaluationContent content : evaluationContents) {
                    content.setId(null);
                    content.setDirectoryCode(empiricalMaterialDirectory.getDirectoryCode());
                    content.setDirectoryName(empiricalMaterialDirectory.getDirectoryName());
                    content.setEvaluationCategory(empiricalMaterialDirectory.getEvaluationCategory());
                    content.setSerialNum(i + "");
                    i++;
                }
                //再新增
                evaluationContentService.saveBatch(evaluationContents);
            }
        }
    }

    private void dealUpdateDirectoryCodeOrName(EmpiricalMaterialDirectory empiricalMaterialDirectory, EmpiricalMaterialDirectory dbData) {
        //修改评价内容
        evaluationContentService.update(
                Wrappers.lambdaUpdate(EmpiricalMaterialEvaluationContent.class)
                        .set(EmpiricalMaterialEvaluationContent::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                        .set(EmpiricalMaterialEvaluationContent::getDirectoryName, empiricalMaterialDirectory.getDirectoryName())
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, dbData.getDirectoryCode())
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, dbData.getDirectoryName()));
        //修改任务分配
        empiricalMaterialTaskService.update(
                Wrappers.lambdaUpdate(EmpiricalMaterialTaskAllocation.class)
                        .set(EmpiricalMaterialTaskAllocation::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                        .set(EmpiricalMaterialTaskAllocation::getDirectoryName, empiricalMaterialDirectory.getDirectoryName())
                        .eq(EmpiricalMaterialTaskAllocation::getDirectoryCode, dbData.getDirectoryCode())
                        .eq(EmpiricalMaterialTaskAllocation::getDirectoryName, dbData.getDirectoryName()));
        //修改证明材料上传
        empiricalMaterialFlowPathService.update(
                Wrappers.lambdaUpdate(EmpiricalMaterialFlowPath.class)
                        .set(EmpiricalMaterialFlowPath::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                        .set(EmpiricalMaterialFlowPath::getDirectoryName, empiricalMaterialDirectory.getDirectoryName())
                        .eq(EmpiricalMaterialFlowPath::getDirectoryCode, dbData.getDirectoryCode())
                        .eq(EmpiricalMaterialFlowPath::getDirectoryName, dbData.getDirectoryName()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delete(List<Long> ids) {
        RLock lock = null;
        boolean result = false;
        //获取分布式锁
        try {
            lock = redissonClient.getLock("EmpiricalMaterialDirectoryServiceImpl.delete");
            result = lock.tryLock(4, TimeUnit.MINUTES);
            if (!result) {
                throw new RuntimeException("当前有人在操作，稍后再试");
            }
            List<EmpiricalMaterialDirectory> empiricalMaterialDirectories = this.listByIds(ids);
            String directoryCode;
            for (EmpiricalMaterialDirectory empiricalMaterialDirectory : empiricalMaterialDirectories) {
                directoryCode = empiricalMaterialDirectory.getDirectoryCode();
                if (directoryCode.length() > 5) {
                    //三级直接删除
                    this.removeById(empiricalMaterialDirectory);
                    deleteAllRelation(empiricalMaterialDirectory);
                } else {
                    //关联的病历文档目录病历文档存在三级目录（已配置），不能删除！
                    List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService.list(
                            new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                                    .apply("length(DIRECTORY_CODE) > {0}", "5")
                                    .likeRight(DataDictionaryDirectoryConfiguration::getDirectoryCode, directoryCode)
                                    .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, "2")

                    );
                    if (CollUtil.isNotEmpty(list)) {
                        throw new RuntimeException("关联的病历数据目录病历文档存在三级目录（已配置），不能删除！");
                    } else {
                        dataDictionaryDirectoryConfigurationService.remove(Wrappers.lambdaQuery(DataDictionaryDirectoryConfiguration.class)
                                .likeRight(DataDictionaryDirectoryConfiguration::getDirectoryCode, directoryCode)
                                .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, "2"));
                    }

                    //一二级级联删除
                    LambdaQueryWrapper<EmpiricalMaterialDirectory> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.likeRight(EmpiricalMaterialDirectory::getDirectoryCode, directoryCode);
                    List<EmpiricalMaterialDirectory> list1 = this.list(queryWrapper);
                    for (EmpiricalMaterialDirectory materialDirectory : list1) {
                        deleteAllRelation(materialDirectory);
                    }
                    this.removeByIds(list1);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return RUtil.error(e.getMessage());
        } finally {
            if (result) {
                lock.unlock();
            }
        }
        return RUtil.success();
    }

    private void deleteAllRelation(EmpiricalMaterialDirectory empiricalMaterialDirectory) {
        if (empiricalMaterialDirectory == null || empiricalMaterialDirectory.getDirectoryCode().length() < 7) {
            return;
        }
        //评价内容
        evaluationContentService.remove(
                Wrappers.lambdaUpdate(EmpiricalMaterialEvaluationContent.class)
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, empiricalMaterialDirectory.getDirectoryName()));
        //任务分配
        empiricalMaterialTaskService.remove(
                Wrappers.lambdaUpdate(EmpiricalMaterialTaskAllocation.class)
                        .eq(EmpiricalMaterialTaskAllocation::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                        .eq(EmpiricalMaterialTaskAllocation::getDirectoryName, empiricalMaterialDirectory.getDirectoryName()));
        //证明材料上传
        empiricalMaterialFlowPathService.remove(
                Wrappers.lambdaUpdate(EmpiricalMaterialFlowPath.class)
                        .eq(EmpiricalMaterialFlowPath::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                        .eq(EmpiricalMaterialFlowPath::getDirectoryName, empiricalMaterialDirectory.getDirectoryName()));
    }

    @Override
    public R query(EmpiricalMaterialDirectoryQuery empiricalMaterialDirectory) {
        LambdaQueryWrapper<EmpiricalMaterialDirectory> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.likeRight(StrUtil.isNotBlank(empiricalMaterialDirectory.getDirectoryCode()), EmpiricalMaterialDirectory::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                .like(StrUtil.isNotBlank(empiricalMaterialDirectory.getDirectoryName()), EmpiricalMaterialDirectory::getDirectoryName, empiricalMaterialDirectory.getDirectoryName())
                .eq(StrUtil.isNotBlank(empiricalMaterialDirectory.getLevelCode()), EmpiricalMaterialDirectory::getLevelCode, empiricalMaterialDirectory.getLevelCode())
                .eq(StrUtil.isNotBlank(empiricalMaterialDirectory.getEvaluationCategory()), EmpiricalMaterialDirectory::getEvaluationCategory, empiricalMaterialDirectory.getEvaluationCategory());
        lambdaQueryWrapper.orderByAsc(EmpiricalMaterialDirectory::getDirectoryCode);
        List<EmpiricalMaterialDirectory> list = this.list(lambdaQueryWrapper);
        String directoryCode = empiricalMaterialDirectory.getDirectoryCode();
        if (StrUtil.isBlank(directoryCode)) {
            return dealListInOne(list, directory -> directory.getDirectoryCode().length() <= 2, true, directoryCode);
        } else if (directoryCode.length() == 2) {
            return dealListInOne(list, directory -> directory.getDirectoryCode().length() == 5, true, directoryCode);
        } else {
            return dealListInOne(list, directory -> directory.getDirectoryCode().length() > 5, false, directoryCode);
        }
    }

    private R dealListInOne(List<EmpiricalMaterialDirectory> list, Predicate<? super EmpiricalMaterialDirectory> predicate, boolean hasChildren, String directoryCode) {
        List<EmpiricalMaterialDirectory> firstLevelList = list.stream().filter(predicate).collect(Collectors.toList());
        List<EmpiricalMaterialDirectoryTree> empiricalMaterialDirectoryTrees = new ArrayList<>();
        EmpiricalMaterialDirectoryTree empiricalMaterialDirectoryTree;
        for (EmpiricalMaterialDirectory empiricalMaterialDirectory : firstLevelList) {
            empiricalMaterialDirectoryTree = new EmpiricalMaterialDirectoryTree();
            empiricalMaterialDirectoryTree.setDirectoryCode(empiricalMaterialDirectory.getDirectoryCode());
            empiricalMaterialDirectoryTree.setDirectoryName(empiricalMaterialDirectory.getDirectoryName());
            empiricalMaterialDirectoryTree.setSerialNum(empiricalMaterialDirectory.getSerialNum());
            empiricalMaterialDirectoryTree.setBusinessProject(empiricalMaterialDirectory.getBusinessProject());
            empiricalMaterialDirectoryTree.setEvaluationCategory(empiricalMaterialDirectory.getEvaluationCategory());
            empiricalMaterialDirectoryTree.setLevelCode(empiricalMaterialDirectory.getLevelCode());
            empiricalMaterialDirectoryTree.setId(empiricalMaterialDirectory.getId());
            empiricalMaterialDirectoryTree.setHasChildren(hasChildren);
            empiricalMaterialDirectoryTree.setParentCode(directoryCode);
            empiricalMaterialDirectoryTree.setRemarksDesc(empiricalMaterialDirectory.getRemarksDesc());
            empiricalMaterialDirectoryTree.setProjectNum(empiricalMaterialDirectory.getProjectNum());
            if (!hasChildren) {
                List<EmpiricalMaterialEvaluationContent> materialEvaluationContents = evaluationContentService.list(Wrappers.lambdaQuery(EmpiricalMaterialEvaluationContent.class)
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, empiricalMaterialDirectory.getDirectoryCode())
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, empiricalMaterialDirectory.getDirectoryName())
                );
                empiricalMaterialDirectoryTree.setEvaluationContents(materialEvaluationContents);
            }
            empiricalMaterialDirectoryTrees.add(empiricalMaterialDirectoryTree);
        }
        return RUtil.success(empiricalMaterialDirectoryTrees);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R batchUpdateMy(List<EmpiricalMaterialDirectory> empiricalMaterialDirectories) {
        if (CollUtil.isEmpty(empiricalMaterialDirectories)) {
            return RUtil.success();
        }
        Map<String, EmpiricalMaterialDirectory> directoryMap = this.listByIds(empiricalMaterialDirectories.stream().map(EmpiricalMaterialDirectory::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(item -> item.getId(), item -> item, (existing, replacement) -> existing));
        EmpiricalMaterialDirectory dbData;
        for (EmpiricalMaterialDirectory empiricalMaterialDirectory : empiricalMaterialDirectories) {
            dbData = directoryMap.get(empiricalMaterialDirectory.getId());
            if (dbData == null) {
                continue;
            }
            dealSerialNum(empiricalMaterialDirectory);
            //评价内容
            addEvaluationContents(empiricalMaterialDirectory);
            //修改目录编码或目录名称导致的关联修改
            if (StrUtil.isNotBlank(empiricalMaterialDirectory.getDirectoryCode())
                    && StrUtil.isNotBlank(empiricalMaterialDirectory.getDirectoryCode())
                    && (!dbData.getDirectoryCode().equals(empiricalMaterialDirectory.getDirectoryCode()) || !dbData.getDirectoryName().equals(empiricalMaterialDirectory.getDirectoryName()))) {
                dealUpdateDirectoryCodeOrName(empiricalMaterialDirectory, dbData);
            }
            //检测-如果是一级二级标题，需要创建病历目录的一级二级
            if (empiricalMaterialDirectory.getDirectoryCode().length() <= 5) {
                DataDictionaryDirectoryConfiguration mapped = MapperUtils.INSTANCE.map(DataDictionaryDirectoryConfiguration.class, empiricalMaterialDirectory);
                mapped.setDirectoryType("2");
                dataDictionaryDirectoryConfigurationService.batchUpdateBase(Arrays.asList(mapped));
            }
        }
        this.updateBatchById(empiricalMaterialDirectories);
        return RUtil.success();
    }
}