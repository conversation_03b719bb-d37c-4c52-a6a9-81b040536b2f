package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文档目录配置查询
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "文档目录配置查询")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentDirectoryConfigurationQuery {
    /**
     * 目录
     */
    @ApiModelProperty(value = "目录")
    private String directory;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码，likeRight形式查询，eg：like 01%")
    private String directoryCode;

    /**
     * 等级关联
     */
    @TableField(value = "LEVEL_CODE")
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性）")
    private String emrRuleType;
}