package com.jykj.dqm.quartz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jykj.dqm.quartz.dao.ScheduleJobTaskDao;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ScheduleJobTaskService {
    @Autowired
    private ScheduleJobTaskDao scheduleJobTaskDao;

    public List<ScheduleJobInfo> queryScheduleJobTask(ScheduleJobInfo params) {
        return scheduleJobTaskDao.queryScheduleJobTask(params);
    }

    public int addScheduleJobTask(ScheduleJobInfo params) {
        return scheduleJobTaskDao.addScheduleJobTask(params);
    }

    public int updateScheduleJobTask(ScheduleJobInfo params) {
        return scheduleJobTaskDao.updateScheduleJobTask(params);
    }

    public int deleteScheduleJobTask(ScheduleJobInfo params) {
        return scheduleJobTaskDao.delete(new QueryWrapper<ScheduleJobInfo>().eq("JOB_ID", params.getJobId()));
    }

    public int addScheduleJobTaskLog(Map<String, Object> params) {
        return scheduleJobTaskDao.addScheduleJobTaskLog(params);
    }

    public int updateScheduleJobTaskLog(Map<String, Object> params) {
        return scheduleJobTaskDao.updateScheduleJobTaskLog(params);
    }

    public int deleteScheduleJobTaskLog(ScheduleJobInfo params) {
        return scheduleJobTaskDao.deleteScheduleJobTaskLog(params);
    }
}
