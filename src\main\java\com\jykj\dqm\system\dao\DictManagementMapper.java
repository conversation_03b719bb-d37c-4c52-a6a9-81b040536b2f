package com.jykj.dqm.system.dao;

import com.jykj.dqm.system.entity.DictMappings;
import com.jykj.dqm.system.entity.DictMappingsContent;
import com.jykj.dqm.system.entity.DictMappingsContentDTO;
import com.jykj.dqm.system.entity.DictMappingsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 字典管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 11:13:03
 */
@Mapper
public interface DictManagementMapper {
    List<DictMappings> queryDictList(DictMappingsDTO dictMappingDTO);

    /**
     * 同步相同库的DictMappings表
     *
     * <AUTHOR>
     */
    void syncDictMappingsData();

    /**
     * 同步相同库的DictMappingsContent表
     *
     * <AUTHOR>
     */
    void syncDictMappingsContentData();

    /**
     * 通过存储过程先清除表数据，再同步DictMappings+DictMappingsContent表
     */
    void syncDictDataByProcedure();

    /**
     * 同步相同表结构的表
     *
     * @param sourceTableName 来源表
     * @param targetTableName 目标表
     * <AUTHOR>
     */
    void syncDictData(@Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName);

    /**
     * 清除表数据
     *
     * @param tableName 表名
     * <AUTHOR>
     */
    void truncateTable(@Param("tableName") String tableName);

    /**
     * 查询字典数据接口
     *
     * @param dictMappingsContentDTO DictMappingsContentDTO
     * @return 字典数据
     * <AUTHOR>
     */
    List<DictMappingsContent> queryDictDataList(DictMappingsContentDTO dictMappingsContentDTO);

    /**
     * 查询所有的系统信息
     *
     * @return 所有的系统信息
     * <AUTHOR>
     */
    List<Map<String, String>> getSysInfoList();
}
