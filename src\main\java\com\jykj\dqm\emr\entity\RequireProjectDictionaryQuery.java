package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 要求项目字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/20 16:23:09
 */
@ApiModel(description = "要求项目字典查询")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RequireProjectDictionaryQuery extends MyPageInfo {
    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    /**
     * 要求项目名称
     */
    @ApiModelProperty(value = "要求项目名称")
    @NotBlank
    private String requireProjectName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    private static final long serialVersionUID = 1L;
}