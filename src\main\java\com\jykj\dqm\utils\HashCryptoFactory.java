package com.jykj.dqm.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 哈希加密工厂类，用于统一管理哈希加密算法（SM3和MD5）
 *
 * <AUTHOR>
 * @version 1.00
 */
@Component
public class HashCryptoFactory {

    /**
     * 默认使用的哈希加密算法，可通过配置文件指定
     */
    private static String defaultAlgorithm = "SM3";

    /**
     * 从配置文件中读取默认哈希加密算法
     * 
     * @param algorithm 配置的算法名称
     */
    @Value("${crypto.hash-algorithm:SM3}")
    public void setDefaultAlgorithm(String algorithm) {
        defaultAlgorithm = algorithm;
    }

    /**
     * 获取默认哈希加密工具
     *
     * @return 默认哈希加密工具
     */
    public static CryptoUtil getDefaultCrypto() {
        return getCrypto(defaultAlgorithm);
    }

    /**
     * 根据算法名称获取哈希加密工具
     *
     * @param algorithm 算法名称（MD5/SM3）
     * @return 哈希加密工具
     */
    public static CryptoUtil getCrypto(String algorithm) {
        if ("MD5".equalsIgnoreCase(algorithm)) {
            return new MD5Util();
        } else if ("SM3".equalsIgnoreCase(algorithm)) {
            return new SM3Util();
        } else {
            // 默认返回SM3
            return new SM3Util();
        }
    }

    /**
     * 使用默认哈希加密算法加密字符串
     *
     * @param str 待加密的字符串
     * @return 加密后的字符串
     */
    public static String encrypt(String str) {
        return getDefaultCrypto().encrypt(str);
    }
}