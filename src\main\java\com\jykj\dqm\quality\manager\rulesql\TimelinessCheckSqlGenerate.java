package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 及时性SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class TimelinessCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    /*
         2021-02-23 10:09:28
         select COUNT(1) from datacontrol.qrtz_locks_uat_p where uat_dt= '2021-02-24'
         select count(*) from datacontrol.qrtz_locks_uat_p where 1=1
         select COUNT(1) from datacontrol.qrtz_locks_uat_p where uat_dt= '2021-02-24'
     */
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        //检核表
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        //检核where条件
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        //检核明细字段
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        //检核字段
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        //0：T 1：T-1 2：T-2 3：T-3
        String dtExecCondDescr = dataqualityCheckRule.getDtExecCondDescr();

        //问题明细字段SQL(比较特殊，异常场景是没有数据的)
        StringBuilder qstDetailFieldSql = new StringBuilder();
        String dateStr;
        if ("0".equals(dtExecCondDescr)) {
            dateStr = DateUtil.format(new Date(), "yyyy/MM/dd");
        } else if ("1".equals(dtExecCondDescr)) {
            dateStr = DateUtil.format(DateUtil.yesterday(), "yyyy/MM/dd");
        } else if ("2".equals(dtExecCondDescr)) {
            dateStr = DateUtil.format(DateUtil.offsetDay(new DateTime(), -2), "yyyy/MM/dd");
        } else if ("3".equals(dtExecCondDescr)) {
            dateStr = DateUtil.format(DateUtil.offsetDay(new DateTime(), -3), "yyyy/MM/dd");
        } else {
            throw new BusinessException("dtExecCondDescr字段参数错误！0：T 1：T-1 2：T-2 3：T-3");
        }
        qstDetailFieldSql.append("select count(*) from " + table + " where "
                + getDate2StrFunction(dataqualityCheckRule.getDbType(), checkRuleColumn) + "= '" + dateStr + "'");
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" and " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.JSX;
    }
}
