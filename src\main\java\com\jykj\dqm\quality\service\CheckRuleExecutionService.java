package com.jykj.dqm.quality.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.quality.entity.CheckRuleExecutionDTO;
import com.jykj.dqm.quality.entity.CheckRuleExecutionQueryDTO;
import com.jykj.dqm.quality.entity.CheckRuleTableOrFieldQueryDTO;
import com.jykj.dqm.quality.entity.QCTaskDTO;

/**
 * 规则执行
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/26 15:55:10
 */
public interface CheckRuleExecutionService {
    /**
     * 获取所有规则的系统
     *
     * @return Result
     * <AUTHOR>
     */
    R getAllSystem();

    /**
     * 规则执行设置(先设置是否是整体执行（0：单独设置 1：整体设置），如果是整体，还需要设置是否覆盖（是否覆盖：0：不覆盖 1：覆盖）)
     *
     * @param qcTaskDTO QCTaskDTO
     * @return Result
     * <AUTHOR>
     */
    R configRule(QCTaskDTO qcTaskDTO);

    /**
     * 根据系统，以及其他条件查询对应的规则
     *
     * @param checkRuleExecutionQueryDTO CheckRuleExecutionQueryDTO
     * @return Result
     * <AUTHOR>
     */
    R ruleRunInfoList(CheckRuleExecutionQueryDTO checkRuleExecutionQueryDTO);

    R exeAllRuleBySys(CheckRuleExecutionDTO checkRuleExecutionDTO);

    R exeOneRule(CheckRuleExecutionDTO checkRuleExecutionDTO);

    R getTable(CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO);

    R getTableField(CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO);

    R getProgress(String groupId);
}
