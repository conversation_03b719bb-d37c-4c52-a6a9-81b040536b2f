/**
 * @Title : LogLoginController
 * @Package : com.scjy.mdm.module.syssetting.controller
 * @Description :登录日志controller
 * <AUTHOR> 黄杰
 * @DateTime : 2021/8/18 19:03
 */
package com.jykj.dqm.system.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.system.entity.LogLoginDTO;
import com.jykj.dqm.system.service.LogLoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录日志controller
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Api(tags = {"登录日志"})
@RestController
@RequestMapping("/loglogin/")
public class LogLoginController {
    @Autowired
    private LogLoginService logLoginService;

    /**
     * 查询登录日志接口
     *
     * @param logLoginDTO LogLoginDTO
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "查询登录日志接口", notes = "日志管理")
    @PostMapping("/query")
    public R queryLog(@RequestBody LogLoginDTO logLoginDTO) {
        return logLoginService.queryLog(logLoginDTO);
    }
}
