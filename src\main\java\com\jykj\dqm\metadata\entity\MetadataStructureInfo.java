package com.jykj.dqm.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 元数据结构(表/视图)信息表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 16:17:20
 */
@ApiModel(value = "元数据结构(表/视图)信息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_METADATA_STRUCTURE_INFO")
public class MetadataStructureInfo {
    /**
     * 采集ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "采集ID")
    private Integer id;

    /**
     * 数据源ID
     */
    @TableField(value = "DATA_SOURCE_ID")
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据库名称
     */
    @TableField(value = "DATABASE_NAME")
    @ApiModelProperty(value = "数据库名称")
    private String databaseName;

    /**
     * SCHEMA
     */
    @TableField(value = "SCHEMA",keepGlobalFormat = true)
    @ApiModelProperty(value = "SCHEMA")
    private String schema;

    /**
     * CATALOG
     */
    @TableField(value = "CATALOG",keepGlobalFormat = true)
    @ApiModelProperty(value = "CATALOG")
    private String catalog;

    /**
     * 表名
     */
    @TableField(value = "NAME",keepGlobalFormat = true)
    @ApiModelProperty(value = "表名")
    private String name;

    /**
     * 表名注释
     */
    @TableField(value = "REMARKS")
    @ApiModelProperty(value = "表名注释")
    private String remarks;

    /**
     * TABLE/VIEW
     */
    @TableField(value = "TYPE",keepGlobalFormat = true)
    @ApiModelProperty(value = "TABLE/VIEW")
    private String type;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}