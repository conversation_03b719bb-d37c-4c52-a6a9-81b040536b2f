package com.jykj.dqm.metadata.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据源子任务展示
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:35:34
 */
@ApiModel(value = "数据源子任务展示")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataDatasourceSubTaskInfoVO {
    /**
     * 任务组和子任务ID
     */
    @ApiModelProperty(value = "任务组和子任务ID")
    private Integer taskSubTaskId;

    /**
     * 数据源ID
     */
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String dataSourceDescribe;

    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "数据库类型")
    private String databaseType;

    /**
     * 数据库驱动
     */
    @ApiModelProperty(value = "数据库驱动")
    private String databaseDriver;

    /**
     * 数据库访问URL
     */
    @ApiModelProperty(value = "数据库访问URL")
    private String databaseUrl;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String databaseUser;

    /**
     * 密码(加密后)
     */
    @ApiModelProperty(value = "密码(加密后)")
    private String databasePwd;

    /**
     * 采集数据库名称
     */
    @ApiModelProperty(value = "采集数据库名称")
    private String databaseName;

    /**
     * SCHEMA
     */
    @ApiModelProperty(value = "SCHEMA")
    private String databaseSchema;

    /**
     * 测试语句
     */
    @ApiModelProperty(value = "测试语句")
    private String testsql;

    /**
     * 驱动包路径
     */
    @ApiModelProperty(value = "驱动包路径")
    private String driverFiles;

    /**
     * 任务组ID
     */
    @ApiModelProperty(value = "任务组ID")
    private String taskGroupId;

    /**
     * 采集系统代码
     */
    @ApiModelProperty(value = "采集系统代码")
    private String sysCode;

    /**
     * 采集系统名称
     */
    @ApiModelProperty(value = "采集系统名称")
    private String sysName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")    //前端-->后端显示
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    private Date updateTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operationPerson;
}