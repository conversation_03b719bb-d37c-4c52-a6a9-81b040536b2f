package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.DocumentDirectoryConfigurationMapper;
import com.jykj.dqm.emr.entity.DataDictionaryAssociated;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfigurationQuery;
import com.jykj.dqm.emr.entity.DocumentDirectoryFirstTree;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.entity.DocumentRuleSqlExecRecord;
import com.jykj.dqm.emr.entity.RequireProjectDictionary;
import com.jykj.dqm.emr.entity.RulePermissionConfiguration;
import com.jykj.dqm.emr.service.DataDictionaryAssociatedService;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DocumentDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DocumentRuleConfigurationService;
import com.jykj.dqm.emr.service.DocumentRuleSqlExecRecordService;
import com.jykj.dqm.emr.service.RequireProjectDictionaryService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.NumberMyUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 文档目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@Service
public class DocumentDirectoryConfigurationServiceImpl extends ServiceImpl<DocumentDirectoryConfigurationMapper, DocumentDirectoryConfiguration> implements DocumentDirectoryConfigurationService {
    @Autowired
    private DataDictionaryDirectoryConfigurationService dataDictionaryDirectoryConfigurationService;
    @Autowired
    private DataDictionaryAssociatedService dataDictionaryAssociatedService;
    @Autowired
    private RulePermissionConfigurationService rulePermissionConfigurationService;
    @Autowired
    private DocumentRuleConfigurationService documentRuleConfigurationService;
    @Autowired
    private RequireProjectDictionaryService requireProjectDictionaryService;
    @Autowired
    private DocumentRuleSqlExecRecordService documentRuleSqlExecRecordService;
    @Autowired
    private RedissonClient redissonClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R add(DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        dealSerialNum(documentDirectoryConfiguration);
        this.save(documentDirectoryConfiguration);
        //检测-如果是一级二级标题，需要创建病历目录的一级二级
        if (documentDirectoryConfiguration.getDirectoryCode().length() <= 5) {
            DataDictionaryDirectoryConfiguration mapped = MapperUtils.INSTANCE.map(DataDictionaryDirectoryConfiguration.class, documentDirectoryConfiguration);
            mapped.setDirectoryType("3");
            dataDictionaryDirectoryConfigurationService.addBase(mapped);
        }
        return RUtil.success();
    }

    private void dealSerialNum(DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        //处理序号
        String directoryCode = documentDirectoryConfiguration.getDirectoryCode();
        String[] titleNums = directoryCode.split("\\.");
        if (titleNums.length == 1) {
            //一级标题
            documentDirectoryConfiguration.setSerialNum(NumberMyUtil.intToChinese(Integer.parseInt(titleNums[0])));
        }
        if (titleNums.length == 2) {
            //二级标题
            documentDirectoryConfiguration.setSerialNum(Integer.parseInt(titleNums[1]) + "");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        dealSerialNum(documentDirectoryConfiguration);
        DocumentDirectoryConfiguration dbConfiguration = this.getById(documentDirectoryConfiguration.getId());
        if (dbConfiguration.getDirectoryCode().length() >= 7) {
            //更新要求项目字典
            requireProjectDictionaryService.update(
                    Wrappers.lambdaUpdate(RequireProjectDictionary.class)
                            .set(RequireProjectDictionary::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                            .set(RequireProjectDictionary::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                            .set(RequireProjectDictionary::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                            .eq(RequireProjectDictionary::getDirectoryCode, dbConfiguration.getDirectoryCode())
                            .eq(RequireProjectDictionary::getDirectoryName, dbConfiguration.getDirectoryName())
                            .eq(RequireProjectDictionary::getEmrRuleType, dbConfiguration.getEmrRuleType())
            );

            //更新任务分配
            rulePermissionConfigurationService.update(
                    Wrappers.lambdaUpdate(RulePermissionConfiguration.class)
                            .set(RulePermissionConfiguration::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                            .set(RulePermissionConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                            .set(RulePermissionConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                            .eq(RulePermissionConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                            .eq(RulePermissionConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
                            .eq(RulePermissionConfiguration::getEmrRuleType, dbConfiguration.getEmrRuleType())
            );

            //更新关联表：DQM_EMR_DATA_DICTIONARY_ASSOCIATED
            dataDictionaryAssociatedService.update(Wrappers.lambdaUpdate(DataDictionaryAssociated.class)
                    .set(DataDictionaryAssociated::getAssociatedDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                    .set(DataDictionaryAssociated::getAssociatedDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                    .set(DataDictionaryAssociated::getAssociatedEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                    .eq(DataDictionaryAssociated::getAssociatedDirectoryCode, dbConfiguration.getDirectoryCode())
                    .eq(DataDictionaryAssociated::getAssociatedDirectoryName, dbConfiguration.getDirectoryName())
                    .eq(DataDictionaryAssociated::getAssociatedEmrRuleType, dbConfiguration.getEmrRuleType())
            );

            //规则配置
            documentRuleConfigurationService.update(Wrappers.lambdaUpdate(DocumentRuleConfiguration.class)
                    .set(DocumentRuleConfiguration::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                    .set(DocumentRuleConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                    .set(DocumentRuleConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                    .eq(DocumentRuleConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                    .eq(DocumentRuleConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
                    .eq(DocumentRuleConfiguration::getEmrRuleType, dbConfiguration.getEmrRuleType())
            );
            //文档规则配置SQL执行结果数据
            documentRuleSqlExecRecordService.update(Wrappers.lambdaUpdate(DocumentRuleSqlExecRecord.class)
                    .set(DocumentRuleSqlExecRecord::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                    .set(DocumentRuleSqlExecRecord::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                    .set(DocumentRuleSqlExecRecord::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                    .eq(DocumentRuleSqlExecRecord::getDirectoryCode, dbConfiguration.getDirectoryCode())
                    .eq(DocumentRuleSqlExecRecord::getDirectoryName, dbConfiguration.getDirectoryName())
                    .eq(DocumentRuleSqlExecRecord::getEmrRuleType, dbConfiguration.getEmrRuleType())
            );
        }
        this.updateById(documentDirectoryConfiguration);
        //检测-如果是一级二级标题，需要创建病历目录的一级二级
        if (documentDirectoryConfiguration.getDirectoryCode().length() <= 5) {
            DataDictionaryDirectoryConfiguration mapped = MapperUtils.INSTANCE.map(DataDictionaryDirectoryConfiguration.class, documentDirectoryConfiguration);
            mapped.setDirectoryType("3");
            dataDictionaryDirectoryConfigurationService.batchUpdateBase(Arrays.asList(mapped));
        }
        return RUtil.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delete(List<Long> ids) {
        RLock lock = null;
        boolean result = false;
        //获取分布式锁
        try {
            lock = redissonClient.getLock("DocumentDirectoryConfigurationServiceImpl.delete");
            result = lock.tryLock(4, TimeUnit.MINUTES);
            if (!result) {
                throw new RuntimeException("当前有人在操作，稍后再试");
            }

            List<DocumentDirectoryConfiguration> documentDirectoryConfigurations = this.listByIds(ids);
            String directoryCode;
            for (DocumentDirectoryConfiguration documentDirectoryConfiguration : documentDirectoryConfigurations) {
                directoryCode = documentDirectoryConfiguration.getDirectoryCode();
                if (directoryCode.length() > 5) {
                    deleteAllRelation(documentDirectoryConfiguration);
                } else {
                    //关联的病历文档目录病历文档存在三级目录（已配置），不能删除！
                    List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService.list(
                            new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                                    .apply("length(DIRECTORY_CODE) > {0}", "5")
                                    .likeRight(DataDictionaryDirectoryConfiguration::getDirectoryCode, directoryCode)
                                    .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, "3")

                    );
                    if (CollUtil.isNotEmpty(list)) {
                        throw new RuntimeException("关联的病历数据目录病历文档存在三级目录（已配置），不能删除！");
                    } else {
                        dataDictionaryDirectoryConfigurationService.remove(Wrappers.lambdaQuery(DataDictionaryDirectoryConfiguration.class)
                                .likeRight(DataDictionaryDirectoryConfiguration::getDirectoryCode, directoryCode)
                                .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, "3"));
                    }
                    //一二级级联删除
                    LambdaQueryWrapper<DocumentDirectoryConfiguration> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.likeRight(DocumentDirectoryConfiguration::getDirectoryCode, directoryCode);
                    List<DocumentDirectoryConfiguration> directoryConfigurations = this.list(queryWrapper);
                    for (DocumentDirectoryConfiguration directoryConfiguration : directoryConfigurations) {
                        deleteAllRelation(directoryConfiguration);
                    }
                    this.removeByIds(directoryConfigurations);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return RUtil.error(e.getMessage());
        } finally {
            if (result) {
                lock.unlock();
            }
        }
        return RUtil.success();
    }

    private void deleteAllRelation(DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        if (documentDirectoryConfiguration == null || documentDirectoryConfiguration.getDirectoryCode().length() < 7) {
            return;
        }
        //三级直接删除
        this.removeById(documentDirectoryConfiguration);
        //更新关联表：DQM_EMR_DATA_DICTIONARY_ASSOCIATED
        dataDictionaryAssociatedService.remove(Wrappers.lambdaUpdate(DataDictionaryAssociated.class)
                .eq(DataDictionaryAssociated::getAssociatedDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                .eq(DataDictionaryAssociated::getAssociatedDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                .eq(DataDictionaryAssociated::getAssociatedEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
        );
        //删除要求项目字典
        requireProjectDictionaryService.remove(
                Wrappers.lambdaUpdate(RequireProjectDictionary.class)
                        .eq(RequireProjectDictionary::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                        .eq(RequireProjectDictionary::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                        .eq(RequireProjectDictionary::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
        );

        //删除任务分配
        rulePermissionConfigurationService.remove(
                Wrappers.lambdaUpdate(RulePermissionConfiguration.class)
                        .eq(RulePermissionConfiguration::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                        .eq(RulePermissionConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                        .eq(RulePermissionConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
        );
        //删除规则配置
        documentRuleConfigurationService.remove(Wrappers.lambdaUpdate(DocumentRuleConfiguration.class)
                .eq(DocumentRuleConfiguration::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                .eq(DocumentRuleConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                .eq(DocumentRuleConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
        );
        //删除文档规则配置SQL执行结果数据
        documentRuleSqlExecRecordService.remove(Wrappers.lambdaUpdate(DocumentRuleSqlExecRecord.class)
                .eq(DocumentRuleSqlExecRecord::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                .eq(DocumentRuleSqlExecRecord::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                .eq(DocumentRuleSqlExecRecord::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
        );
    }

    @Override
    public R query(DocumentDirectoryConfigurationQuery documentDirectoryConfiguration) {
        String directoryCode = documentDirectoryConfiguration.getDirectoryCode();
        LambdaQueryWrapper<DocumentDirectoryConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.likeRight(StrUtil.isNotBlank(directoryCode), DocumentDirectoryConfiguration::getDirectoryCode, directoryCode)
                .like(StrUtil.isNotBlank(documentDirectoryConfiguration.getDirectoryName()), DocumentDirectoryConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                .le(StrUtil.isNotBlank(directoryCode) && directoryCode.length() > 2 && StrUtil.isNotBlank(documentDirectoryConfiguration.getLevelCode()), DocumentDirectoryConfiguration::getLevelCode, documentDirectoryConfiguration.getLevelCode())
                .eq(StrUtil.isNotBlank(documentDirectoryConfiguration.getEmrRuleType()), DocumentDirectoryConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType());
        lambdaQueryWrapper.orderByAsc(DocumentDirectoryConfiguration::getDirectoryCode);
        List<DocumentDirectoryConfiguration> list = this.list(lambdaQueryWrapper);

        if (StrUtil.isBlank(directoryCode)) {
            return dealListInOne(list, documentDirectory -> documentDirectory.getDirectoryCode().length() <= 2, true, directoryCode);
        } else if (directoryCode.length() == 2) {
            return dealListInOne(list, documentDirectory -> documentDirectory.getDirectoryCode().length() == 5, true, directoryCode);
        } else {
            return dealListInOne(list, documentDirectory -> documentDirectory.getDirectoryCode().length() > 5, false, directoryCode);
        }
    }

    private R dealListInOne(List<DocumentDirectoryConfiguration> list, Predicate<? super DocumentDirectoryConfiguration> predicate, boolean hasChildren, String directoryCode) {
        List<DocumentDirectoryConfiguration> firstLevelList = list.stream().filter(predicate).collect(Collectors.toList());
        List<DocumentDirectoryFirstTree> documentDirectoryFirstList = new ArrayList<>();
        DocumentDirectoryFirstTree documentDirectoryFirst;
        for (DocumentDirectoryConfiguration directoryConfiguration : firstLevelList) {
            documentDirectoryFirst = new DocumentDirectoryFirstTree();
            documentDirectoryFirst.setDirectoryCode(directoryConfiguration.getDirectoryCode());
            documentDirectoryFirst.setDirectoryName(directoryConfiguration.getDirectoryName());
            documentDirectoryFirst.setSerialNum(directoryConfiguration.getSerialNum());
            documentDirectoryFirst.setEmrRuleType(directoryConfiguration.getEmrRuleType());
            documentDirectoryFirst.setLevelCode(directoryConfiguration.getLevelCode());
            documentDirectoryFirst.setId(directoryConfiguration.getId());
            documentDirectoryFirst.setHasChildren(hasChildren);
            documentDirectoryFirst.setParentCode(directoryCode);
            documentDirectoryFirstList.add(documentDirectoryFirst);
        }
        if (!hasChildren) {
            documentDirectoryFirstList.sort(Comparator
                    .comparing(DocumentDirectoryFirstTree::getDirectoryCode)  // 按 directoryCode
                    // 如果serialNum不为空，按 serialNum
                    .thenComparing((o1, o2) -> {
                        if (StrUtil.isNotBlank(o1.getSerialNum()) && StrUtil.isNotBlank(o2.getSerialNum())) {
                            return o1.getSerialNum().compareTo(o2.getSerialNum());
                        }
                        return 0;
                    }));
        }
        return RUtil.success(documentDirectoryFirstList);
    }

    @Override
    public R batchUpdateMy(List<DocumentDirectoryConfiguration> documentDirectoryConfigurations) {
        if (CollUtil.isEmpty(documentDirectoryConfigurations)) {
            return RUtil.success();
        }
        for (DocumentDirectoryConfiguration directoryConfiguration : documentDirectoryConfigurations) {
            dealSerialNum(directoryConfiguration);
            //检测-如果是一级二级标题，需要创建质量目录的一级二级
            if (directoryConfiguration.getDirectoryCode().length() <= 5) {
                DataDictionaryDirectoryConfiguration mapped = MapperUtils.INSTANCE.map(DataDictionaryDirectoryConfiguration.class, directoryConfiguration);
                mapped.setDirectoryType("3");
                dataDictionaryDirectoryConfigurationService.batchUpdateBase(Arrays.asList(mapped));
            }
        }
        Map<String, DocumentDirectoryConfiguration> configurationMap = documentDirectoryConfigurations.stream().collect(Collectors.toMap(DocumentDirectoryConfiguration::getId, item -> item, (k1, k2) -> k2));
        List<DocumentDirectoryConfiguration> documentDirectoryConfigurations1 = this.listByIds(documentDirectoryConfigurations.stream().map(DocumentDirectoryConfiguration::getId).collect(Collectors.toList()));
        DocumentDirectoryConfiguration documentDirectoryConfiguration;
        for (DocumentDirectoryConfiguration dbConfiguration : documentDirectoryConfigurations1) {
            if (dbConfiguration.getDirectoryCode().length() >= 7) {
                documentDirectoryConfiguration = configurationMap.get(dbConfiguration.getId());
                if (documentDirectoryConfiguration == null) {
                    continue;
                }
                //更新要求项目字典
                requireProjectDictionaryService.update(
                        Wrappers.lambdaUpdate(RequireProjectDictionary.class)
                                .set(RequireProjectDictionary::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                                .set(RequireProjectDictionary::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                                .set(RequireProjectDictionary::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                                .eq(RequireProjectDictionary::getDirectoryCode, dbConfiguration.getDirectoryCode())
                                .eq(RequireProjectDictionary::getDirectoryName, dbConfiguration.getDirectoryName())
                                .eq(RequireProjectDictionary::getEmrRuleType, dbConfiguration.getEmrRuleType())
                );

                //更新任务分配
                rulePermissionConfigurationService.update(
                        Wrappers.lambdaUpdate(RulePermissionConfiguration.class)
                                .set(RulePermissionConfiguration::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                                .set(RulePermissionConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                                .set(RulePermissionConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                                .eq(RulePermissionConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                                .eq(RulePermissionConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
                                .eq(RulePermissionConfiguration::getEmrRuleType, dbConfiguration.getEmrRuleType())
                );

                //更新关联表：DQM_EMR_DATA_DICTIONARY_ASSOCIATED
                dataDictionaryAssociatedService.update(Wrappers.lambdaUpdate(DataDictionaryAssociated.class)
                        .set(DataDictionaryAssociated::getAssociatedDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                        .set(DataDictionaryAssociated::getAssociatedDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                        .set(DataDictionaryAssociated::getAssociatedEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                        .eq(DataDictionaryAssociated::getAssociatedDirectoryCode, dbConfiguration.getDirectoryCode())
                        .eq(DataDictionaryAssociated::getAssociatedDirectoryName, dbConfiguration.getDirectoryName())
                        .eq(DataDictionaryAssociated::getAssociatedEmrRuleType, dbConfiguration.getEmrRuleType())
                );

                //规则配置
                documentRuleConfigurationService.update(Wrappers.lambdaUpdate(DocumentRuleConfiguration.class)
                        .set(DocumentRuleConfiguration::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                        .set(DocumentRuleConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                        .set(DocumentRuleConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                        .eq(DocumentRuleConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                        .eq(DocumentRuleConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
                        .eq(DocumentRuleConfiguration::getEmrRuleType, dbConfiguration.getEmrRuleType())
                );
                //文档规则配置SQL执行结果数据
                documentRuleSqlExecRecordService.update(Wrappers.lambdaUpdate(DocumentRuleSqlExecRecord.class)
                        .set(DocumentRuleSqlExecRecord::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                        .set(DocumentRuleSqlExecRecord::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                        .set(DocumentRuleSqlExecRecord::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                        .eq(DocumentRuleSqlExecRecord::getDirectoryCode, dbConfiguration.getDirectoryCode())
                        .eq(DocumentRuleSqlExecRecord::getDirectoryName, dbConfiguration.getDirectoryName())
                        .eq(DocumentRuleSqlExecRecord::getEmrRuleType, dbConfiguration.getEmrRuleType())
                );
            }
        }
        this.updateBatchById(documentDirectoryConfigurations);
        return RUtil.success();
    }

    @Override
    public R queryAll(DocumentDirectoryConfigurationQuery documentDirectoryConfiguration) {
        String directoryCode = documentDirectoryConfiguration.getDirectoryCode();
        LambdaQueryWrapper<DocumentDirectoryConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper
                .and(StrUtil.isNotBlank(documentDirectoryConfiguration.getDirectory()), item ->
                        item.likeRight(DocumentDirectoryConfiguration::getDirectoryCode, documentDirectoryConfiguration.getDirectory())
                                .or().like(DocumentDirectoryConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectory())
                )
                .likeRight(StrUtil.isNotBlank(directoryCode), DocumentDirectoryConfiguration::getDirectoryCode, directoryCode)
                .like(StrUtil.isNotBlank(documentDirectoryConfiguration.getDirectoryName()), DocumentDirectoryConfiguration::getDirectoryName, documentDirectoryConfiguration.getDirectoryName())
                .le(StrUtil.isNotBlank(directoryCode) && directoryCode.length() > 2 && StrUtil.isNotBlank(documentDirectoryConfiguration.getLevelCode()), DocumentDirectoryConfiguration::getLevelCode, documentDirectoryConfiguration.getLevelCode())
                .eq(StrUtil.isNotBlank(documentDirectoryConfiguration.getEmrRuleType()), DocumentDirectoryConfiguration::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType());
        lambdaQueryWrapper.orderByAsc(DocumentDirectoryConfiguration::getDirectoryCode);
        List<DocumentDirectoryConfiguration> list = this.list(lambdaQueryWrapper);
        List<DocumentDirectoryConfiguration> directoryConfigurations = list.stream()
                .filter(entity -> entity.getDirectoryCode().length() >= 7)
                .peek(item -> {
                    item.setCreateBy(null);
                    item.setCreateTime(null);
                    item.setUpdateBy(null);
                    item.setUpdateTime(null);
                })
                .limit(20)
                .collect(Collectors.toList());
        return RUtil.success(directoryConfigurations);
    }
}
