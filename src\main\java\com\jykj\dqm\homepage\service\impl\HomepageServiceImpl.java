package com.jykj.dqm.homepage.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.homepage.service.IHomepageService;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.quality.dao.DataqualityCheckRuleMapper;
import com.jykj.dqm.quality.dao.DataqulityQstnManagerMapper;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqulityQstnManager;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerVO;
import com.jykj.dqm.system.dao.DictMappingsMapper;
import com.jykj.dqm.system.entity.DictMappings;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/14 10:56:47
 */
@Service
@RequiredArgsConstructor
public class HomepageServiceImpl implements IHomepageService {
    private final DataqulityQstnManagerMapper dataqulityQstnManagerMapper;
    private final MetadataDatasourceMapper dqmMetadataDatasourceMapper;
    private final DictMappingsMapper dictMappingsMapper;
    private final DataqualityCheckRuleMapper dataqualityCheckRuleMapper;

    @Override
    public Map<String, Object> getDataAnalysis(String selectDate) {
        Map<String, Object> result = new HashMap(4);
        QueryWrapper<DataqulityQstnManager> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(selectDate)) {
            queryWrapper.lambda()
                    .ge(DataqulityQstnManager::getQstnCheckTime, DateUtil.parseDate(selectDate))
                    .lt(DataqulityQstnManager::getQstnCheckTime, DateUtil.offsetDay(DateUtil.parseDate(selectDate), 1));
        }
        //问题数据
        queryWrapper.select("sum(DATA_QSTN_NUM) as TOTAL");
        DataqulityQstnManager dataqulityQstnManager = dataqulityQstnManagerMapper.selectOne(queryWrapper);
        Integer qstnNum = dataqulityQstnManager == null ? 0 : dataqulityQstnManager.getTotal();
        result.put("qstnNum", qstnNum);

        //质量规则总数
        QueryWrapper<DataqualityCheckRule> checkRuleQueryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(selectDate)) {
            checkRuleQueryWrapper.lambda()
                    .ge(DataqualityCheckRule::getUpdateDate, DateUtil.parseDate(selectDate))
                    .lt(DataqualityCheckRule::getUpdateDate, DateUtil.offsetDay(DateUtil.parseDate(selectDate), 1));
        }
        Long checkRuleNum = dataqualityCheckRuleMapper.selectCount(checkRuleQueryWrapper);
        result.put("checkRuleNum", checkRuleNum);

        QueryWrapper<MetadataDatasource> datasourceQueryWrapper = new QueryWrapper<>();
        //数据源总量
        Long datasourceNum = dqmMetadataDatasourceMapper.selectCount(datasourceQueryWrapper);
        result.put("datasourceNum", datasourceNum);
        //字典数
        QueryWrapper<DictMappings> dictMappingsQueryWrapper = new QueryWrapper<>();
        dictMappingsQueryWrapper.lambda().eq(DictMappings::getDeletedFlag, "0");
        Long dictNum = dictMappingsMapper.selectCount(dictMappingsQueryWrapper);
        result.put("dictNum", dictNum);
        return result;
    }

    @Override
    public Map<String, Object> getProblemDataTrends(String startDateStr, String endDateStr) {
        if (StrUtil.isBlank(startDateStr) || StrUtil.isBlank(endDateStr)) {
            throw new BusinessException("开始时间和结束时间不能为空！");
        }
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDate startDate = LocalDate.parse(startDateStr, fmt);
        LocalDate endDate = LocalDate.parse(endDateStr, fmt);
        long distance = ChronoUnit.DAYS.between(startDate, endDate);
        if (distance < 0) {
            throw new BusinessException("时间区间异常");
        }
        List<Map<String, Object>> listMap = dataqulityQstnManagerMapper.getProblemDataTrends(startDateStr, endDateStr);
        Map<String, Object> collect = listMap.stream().collect(Collectors.toMap(item -> String.valueOf(item.get("DT")), item -> item.get("TOTAL"), (n1, n2) -> n2, HashMap<String, Object>::new));
        //初始化时间段
        List<String> list = new ArrayList<>();
        Stream.iterate(startDate, d -> d.plusDays(1)).limit(distance + 1).forEach(f -> list.add(f.toString()));
        Map<String, Object> result = new LinkedHashMap<>(list.size());
        list.stream().forEach(day -> {
            if (!collect.containsKey(day)) {
                result.put(day, BigDecimal.ZERO);
            } else {
                result.put(day, collect.get(day));
            }
        });
        return result;
    }

    @Override
    public Map<String, Object> getSysCheckRule() {
        List<Map<String, Object>> mapList = dataqualityCheckRuleMapper.getSysCheckRule();
        Map<String, Object> collect = mapList.stream().collect(Collectors.toMap(
                item -> ObjectUtil.isEmpty(item.get("SYS_NAME")) ? String.valueOf(item.get("SYS_CODE")) : String.valueOf(item.get("SYS_NAME")),
                item -> item.get("TOTAL"),
                (n1, n2) -> n2,
                HashMap<String, Object>::new));
        return collect;
    }

    @Override
    public Map<String, Object> getTheQstnProportion() {
        List<Map<String, Object>> mapList = dataqulityQstnManagerMapper.getTheQstnProportion();
        Map<String, Object> collect = mapList.stream().collect(Collectors.toMap(
                item -> String.valueOf(item.get("QSTN_NM")),
                item -> item.get("TOTAL"),
                (n1, n2) -> n2,
                HashMap<String, Object>::new));
        return collect;
    }

    @Override
    public List<DataqulityQstnManagerVO> getTheQstnSort(Integer limitNum) {
        if (limitNum == null || limitNum == 0) {
            limitNum = 10;
        }
        PageHelper.startPage(1, limitNum);
        List<DataqulityQstnManagerVO> list = dataqulityQstnManagerMapper.queryList(null);
        PageInfo<DataqulityQstnManagerVO> pageInfo = new PageInfo<>(list);
        return pageInfo.getList();
    }
}
