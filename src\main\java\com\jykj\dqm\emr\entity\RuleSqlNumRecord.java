package com.jykj.dqm.emr.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 数据质量SQL结果记录
* @TableName DQM_EMR_RULE_SQL_NUM_RECORD
*/
@TableName(value = "DQM_EMR_RULE_SQL_NUM_RECORD")
@Data
public class RuleSqlNumRecord implements Serializable {
    /**
    * 
    */
    @ApiModelProperty(value = "")
    private Integer id;

    /**
    * DQM_EMR_DOCUMENT_RULE_CONFIGURATION表ID
    */
    @ApiModelProperty(value = "DQM_EMR_DOCUMENT_RULE_CONFIGURATION表ID")
    private String configurationId;

    /**
    * 实际总记录数
    */
    @ApiModelProperty(value = "实际总记录数")
    private Integer allNum;

    /**
    * 符合要求的记录数
    */
    @ApiModelProperty(value = "符合要求的记录数")
    private Integer conditionalNum;

}
