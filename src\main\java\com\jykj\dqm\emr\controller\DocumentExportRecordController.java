package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.DocumentExporQueryDTO;
import com.jykj.dqm.emr.entity.DocumentExportEachDoc;
import com.jykj.dqm.emr.entity.DocumentExportRecord;
import com.jykj.dqm.emr.entity.DocumentExportRecordQuery;
import com.jykj.dqm.emr.entity.DocumentPreview;
import com.jykj.dqm.emr.service.DocumentExportRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 文档导出记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:48:50
 */
@Api(tags = {"文档导出记录"})
@RestController
@RequestMapping("/emr/documentExportRecord")
public class DocumentExportRecordController {
    @Resource
    private DocumentExportRecordService documentExportRecordService;

    /**
     * 查询文档导出记录
     *
     * @param documentExportRecordQuery DocumentExportRecordQuery
     * @return List<DocumentExportRecord> {@link DocumentExportRecord}
     * <AUTHOR>
     */
    @ApiOperation(value = "查询文档导出记录", notes = "文档导出记录")
    @PostMapping("/query")
    public R query(@Validated @RequestBody DocumentExportRecordQuery documentExportRecordQuery) {
        return documentExportRecordService.query(documentExportRecordQuery);
    }

    /**
     * 预览文档
     *
     * @param documentPreview DocumentPreview
     * <AUTHOR>
     */
    @ApiOperation(value = "预览文档", notes = "文档导出记录")
    @PostMapping("/previewWord")
    public void previewWord(@Validated @RequestBody DocumentPreview documentPreview) {
        documentExportRecordService.previewWord(documentPreview);
    }

    /**
     * 删除文档记录
     *
     * @param exportRecordId exportRecordId
     * <AUTHOR>
     */
    @ApiOperation(value = "删除文档", notes = "文档导出记录")
    @LogRemark(operate = "删除文档", module = "文档导出记录")
    @DeleteMapping("/delete/{exportRecordId}")
    public R delete(@PathVariable String exportRecordId) {
        return documentExportRecordService.delete(exportRecordId);
    }

    /**
     * 根据导出ID，获取失败原因
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果allDocxNum;finishDocxNum;failureResult
     * <AUTHOR>
     */
    @ApiOperation(value = "根据导出ID，获取失败原因", notes = "文档预览导出")
    @GetMapping("/getErrorDetail/{exportRecordId}")
    public R getErrorDetail(@PathVariable("exportRecordId") String exportRecordId) {
        return documentExportRecordService.getErrorDetail(exportRecordId);
    }

    /**
     * 根据导出ID，获取备注
     *
     * @param exportRecordId 文档导出记录ID
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "根据导出ID，获取备注", notes = "文档预览导出")
    @GetMapping("/getProblemDataRemarks/{exportRecordId}")
    public R getProblemDataRemarks(@PathVariable("exportRecordId") String exportRecordId) {
        return documentExportRecordService.getProblemDataRemarks(exportRecordId);
    }


    /**
     * 根据导出ID+目录名称+目录编码+关联类型，获取备注
     *
     * @param documentExportEachDoc DocumentExportEachDoc
     * @return DocumentExportRecordRuleDetail
     * <AUTHOR>
     */
    @ApiOperation(value = "根据导出ID+目录名称+目录编码+关联类型，获取备注", notes = "文档预览导出")
    @PostMapping("/getOneProblemDataRemark")
    public R getOneProblemDataRemark(@RequestBody @Validated DocumentExportEachDoc documentExportEachDoc) {
        return documentExportRecordService.getOneProblemDataRemark(documentExportEachDoc);
    }


    /**
     * 根据导出ID，获取历史备注
     *
     * @param documentExportEachDoc DocumentExportEachDoc
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "根据导出ID，获取历史备注", notes = "文档预览导出")
    @PostMapping("/getProblemDataHistoryRemarks")
    public R getProblemDataHistoryRemarks(@RequestBody @Validated DocumentExporQueryDTO documentExportEachDoc) {
        return documentExportRecordService.getProblemDataHistoryRemarks(documentExportEachDoc);
    }
}
