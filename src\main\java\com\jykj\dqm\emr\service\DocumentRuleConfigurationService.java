package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.entity.DocumentRuleConfigurationDTO;
import com.jykj.dqm.emr.entity.DocumentRuleConfigurationQuery;
import com.jykj.dqm.emr.entity.DocumentRuleConfigurationQueryProject;
import com.jykj.dqm.emr.entity.MetadataStructureEmrQuery;

/**
 * 文档规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/22 15:50:15
 */
public interface DocumentRuleConfigurationService extends IService<DocumentRuleConfiguration> {
    R add(DocumentRuleConfigurationDTO documentRuleConfiguration);

    R update(DocumentRuleConfigurationDTO documentRuleConfiguration);

    R query(DocumentRuleConfigurationQuery documentRuleConfiguration);

    R queryLeftTree(boolean needNotAllocationTask, String levelCode, String userAccount,String projectId);

    R queryProject(DocumentRuleConfigurationQueryProject documentRuleConfiguration);

    R getStructures(MetadataStructureEmrQuery structureEmrQuery);

    String getTimeConditionSql(String dbType, String startDate, String endDate);

    R save(DocumentRuleConfigurationDTO documentRuleConfigurationDTO);

    R queryHeaderName(DocumentRuleConfigurationQuery documentRuleConfiguration);

    R markComplete(DocumentRuleConfigurationQuery documentRuleConfigurationQuery);
}
