package com.jykj.dqm.emr.manager.generateword;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentExportRecord;
import com.jykj.dqm.emr.entity.DocumentExportRecordErrorDetail;
import com.jykj.dqm.emr.entity.DocumentExportRecordRuleDetail;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.manager.PictureUtils;
import com.jykj.dqm.emr.service.DocumentExportRecordErrorDetailService;
import com.jykj.dqm.emr.service.DocumentExportRecordRuleDetailService;
import com.jykj.dqm.emr.service.DocumentExportRecordService;
import com.jykj.dqm.emr.service.DocumentRuleSqlExecRecordService;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.utils.DateTimeUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.SysCodeContentUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 生成章节Word
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/28 14:05:14
 */
public abstract class GenerateChapterWord {
    @Autowired
    protected RedisTemplate redisTemplate;

    @Autowired
    protected DocumentExportRecordErrorDetailService documentExportRecordErrorDetailService;

    @Autowired
    protected DocumentRuleSqlExecRecordService documentRuleSqlExecRecordService;

    @Autowired
    protected DocumentExportRecordService documentExportRecordService;

    @Autowired
    protected DocumentExportRecordRuleDetailService documentExportRecordRuleDetailService;

    public static final String SYSTEM_SPLIT = "-";

    // 基于导出记录ID的批注信息映射缓存，避免重复查询和并发问题
    private static final Map<String, Map<String, DocumentExportRecordRuleDetail>> exportRecordAnnotationCache = new ConcurrentHashMap<>();

    // 记录导出记录ID对应的创建时间，用于定时清理
    private static final Map<String, Long> exportRecordTimestampCache = new ConcurrentHashMap<>();

    /**
     * 生成章节Word
     *
     * @param documentRuleConfigurations List<DocumentRuleConfiguration>
     * @param directoryConfiguration     DocumentDirectoryConfiguration
     * @param dataStartTime              dataStartTime
     * @param dataEndTime                dataEndTime
     * @param recordId                   recordId
     */
    void dealEachDoc(List<DocumentRuleConfiguration> documentRuleConfigurations,
            DocumentDirectoryConfiguration directoryConfiguration, String dataStartTime, String dataEndTime,
            String recordId, DocumentDirectoryConfiguration secondDirectoryConfiguration) {

    }

    /**
     * 规则类型
     *
     * @return 规则类型
     */
    RuleTypeEnum gainRuleRuleType() {
        return null;
    }

    /**
     * 将SQL中的时间占位符修改为具体时间
     *
     * @param sql       sql
     * @param dbType    数据库类型
     * @param startDate startDate
     * @param endDate   startDate
     * @return 补充时间后的SQL
     */
    String addTimeToSql(String sql, String dbType, String startDate, String endDate) {
        if (startDate.length() < 11) {
            startDate = startDate + " 00:00:00";
        }
        String startTimeSql = getSqlDateFunction(dbType, startDate);
        if (endDate.length() < 11) {
            endDate = endDate + " 23:59:59";
        }
        String endTimeSql = getSqlDateFunction(dbType, endDate);
        return sql.replace("#startDate", startTimeSql).replace("#endDate", endTimeSql);
    }

    /**
     * 获取日期装换方法
     *
     * @param dbType    数据库类型
     * @param dateValue 时间字段值
     * @return 日期装换方法
     * <AUTHOR>
     */
    public String getSqlDateFunction(String dbType, String dateValue) {
        String dateFun = "'" + dateValue + "'";
        if ("ORACLE".equals(dbType) || "TERADATA".equalsIgnoreCase(dbType)) {
            dateFun = "to_date('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        } else if ("MYSQL".equalsIgnoreCase(dbType)) {
            dateFun = "str_to_date('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        } else if ("SQLSERVER".equalsIgnoreCase(dbType)) {
            dateFun = "Cast('" + dateValue + "' as datetime)";
        } else if ("HIVE".equalsIgnoreCase(dbType)) {
            dateFun = "to_date('" + dateValue + "')";
        } else if ("PRESTO".equalsIgnoreCase(dbType)) {
            dateFun = "date_parse('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        }
        return dateFun;
    }

    private String getDateFormatStyle(String dbType, int len) {
        String style = "";
        if ("ORACLE".equals(dbType) || "TERADATA".equals(dbType)) {
            style = "'yyyy-MM-dd hh24:mi:ss'";
            if (len < 11) {
                style = "'yyyy-MM-dd'";
            }
        } else if ("MYSQL".equalsIgnoreCase(dbType) || "PRESTO".equalsIgnoreCase(dbType)) {
            style = "'%Y-%m-%d %T'";
            if (len < 11) {
                style = "'%Y-%m-%d'";
            }
        }
        return style;
    }

    /**
     * 生成图片
     *
     * @param picturePath           picturePath
     * @param conditionalRecordsSql conditionalRecordsSql
     * @param metadataDatasource    metadataDatasource
     * @param match                 SQL执行结果（数字）
     */
    void generatePicture(String picturePath, String conditionalRecordsSql, MetadataDatasource metadataDatasource,
            long match) {
        PictureUtils pictureUtils = new PictureUtils();
        Map<String, String> params = new HashMap<>();
        params.put("dbName", metadataDatasource.getDatabaseName());
        params.put("sql", conditionalRecordsSql);
        params.put("result", match + "");
        params.put("datetime", DateTimeUtil.getNowDateTimeStr());
        pictureUtils.combinePicture(picturePath, params);
    }

    /**
     * 记录告警信息
     *
     * @param recordId
     * @param directoryCode
     * @param directoryName
     * @param emrRuleType
     * @param result
     */
    protected void recordRedisExceptionInfo(String recordId, String directoryCode, String directoryName,
            String emrRuleType, String result) {
        LambdaUpdateWrapper<DocumentExportRecordErrorDetail> errorDetailLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        errorDetailLambdaUpdateWrapper.eq(DocumentExportRecordErrorDetail::getExportRecordId, recordId)
                .eq(DocumentExportRecordErrorDetail::getDirectoryName, directoryName)
                .eq(DocumentExportRecordErrorDetail::getDirectoryCode, directoryCode)
                .eq(DocumentExportRecordErrorDetail::getEmrRuleType, emrRuleType);
        if (Double.parseDouble(SysCodeContentUtils.getAlarmCoefficientMap().get(emrRuleType)) > Double
                .parseDouble(result) || 1.0 < Double.parseDouble(result)) {
            Map map = new HashMap();
            map.put("msg", result);
            map.put("directoryName", directoryName);
            map.put("directoryCode", directoryCode);
            map.put("emrRuleType", emrRuleType);

            redisTemplate.opsForList().leftPush(recordId + "_RESULT2", JSON.toJSONString(map));
            redisTemplate.expire(recordId + "_RESULT2", 10, TimeUnit.HOURS);

            // 将告警记录入库
            DocumentExportRecordErrorDetail documentExportRecordErrorDetail = DocumentExportRecordErrorDetail.builder()
                    .exportRecordId(recordId).errorReason(result).directoryCode(directoryCode)
                    .directoryName(directoryName).emrRuleType(emrRuleType).errorType("告警").build();
            documentExportRecordErrorDetailService.saveOrUpdate(documentExportRecordErrorDetail,
                    errorDetailLambdaUpdateWrapper);
        } else {
            // 删除
            if(RuleTypeEnum.ZHX.getValue().equals(emrRuleType) && "10.01.4".equals(directoryCode)){
                return;
            }
            documentExportRecordErrorDetailService.remove(errorDetailLambdaUpdateWrapper);
        }
    }

    /**
     * 组装表名和字段名
     *
     * @param table
     * @param field
     * @param tableAndFiledType
     * @return 表名.字段名
     */
    protected String getTableAndFieldName(String tableAndFiledType, String table, String field) {
        if ("1".equals(tableAndFiledType)) {
            // 自定义
            return field;
        }
        if (StrUtil.isBlank(field)) {
            throw new BusinessException("字段不能为空");
        }
        if (StrUtil.isBlank(table)) {
            return field;
        }
        return table + "." + field;
    }

    protected String getDataSourceId(DocumentRuleConfiguration documentRuleConfiguration) {
        return "1".equals(documentRuleConfiguration.getWhetherCrossDbQuery())
                ? documentRuleConfiguration.getCrossDbQueryDataSourceId()
                : documentRuleConfiguration.getDataSourceId();
    }

    protected String getDataSourceId2(DocumentRuleConfiguration documentRuleConfiguration) {
        return "1".equals(documentRuleConfiguration.getWhetherCrossDbQuery())
                ? documentRuleConfiguration.getCrossDbQueryDataSourceId()
                : documentRuleConfiguration.getDataSourceId2();
    }

    protected boolean whetherProjectSystemTogether(List<DocumentRuleConfiguration> documentRuleConfigurations) {
        String configValue = RedisUtil.getSysConfigValue("emr.word.project.system.together", "N");
        return "Y".equals(configValue) && documentRuleConfigurations.get(0).getRequiredProject().contains(SYSTEM_SPLIT);
    }

    protected boolean hasRemark(List<DocumentRuleConfiguration> documentRuleConfigurations) {
        return StrUtil.isNotBlank(documentRuleConfigurations.get(0).getRuleConfigurationRemark());
    }

    /**
     * 获取Long类型的值，兼容不同的数据类型
     *
     * @param value 原始值
     * @return Long类型的值
     */
    protected long getLongValue(Object value) {
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }

    // 按照SYSTEM_SPLIT对数据分组
    protected Map<String, List<DocumentRuleConfiguration>> groupDataBySystem(
            List<DocumentRuleConfiguration> documentRuleConfigurations, boolean projectSystemTogether) {
        Map<String, List<DocumentRuleConfiguration>> systemGroupMap = new LinkedHashMap<>();
        if (projectSystemTogether) {
            systemGroupMap = documentRuleConfigurations.stream()
                    .collect(Collectors.groupingBy(documentRuleConfiguration -> {
                        String hospitalProjectTmp = documentRuleConfiguration.getHospitalProject();
                        return hospitalProjectTmp.contains(SYSTEM_SPLIT)
                                ? hospitalProjectTmp.substring(0, hospitalProjectTmp.indexOf(SYSTEM_SPLIT))
                                : "";
                    }, LinkedHashMap::new, Collectors.toList()));
        } else {
            systemGroupMap.put("", documentRuleConfigurations);
        }
        return systemGroupMap;
    }

    /**
     * 处理当前章节的批注信息关联
     * 从最新的导出记录中获取批注信息，并关联到当前导出记录
     *
     * @param exportRecordId         当前导出记录ID
     * @param directoryConfiguration 目录配置
     */
    protected void processChapterAnnotationInfo(String exportRecordId,
            DocumentDirectoryConfiguration directoryConfiguration) {
        try {
            // 1. 获取项目ID
            DocumentExportRecord currentExportRecord = documentExportRecordService.getById(exportRecordId);
            if (currentExportRecord == null) {
                return;
            }
            String projectId = currentExportRecord.getProjectId();

            // 2. 获取或初始化当前导出记录的批注信息映射（基于导出记录ID缓存，避免重复查询和并发问题）
            Map<String, DocumentExportRecordRuleDetail> latestAnnotationMap = exportRecordAnnotationCache
                    .get(exportRecordId);
            if (latestAnnotationMap == null) {
                synchronized (exportRecordAnnotationCache) {
                    latestAnnotationMap = exportRecordAnnotationCache.get(exportRecordId);
                    if (latestAnnotationMap == null) {
                        latestAnnotationMap = initLatestAnnotationMap(exportRecordId, projectId);
                        exportRecordAnnotationCache.put(exportRecordId, latestAnnotationMap);
                        // 记录创建时间戳，用于定时清理
                        exportRecordTimestampCache.put(exportRecordId, System.currentTimeMillis());
                    }
                }
            }

            if (latestAnnotationMap.isEmpty()) {
                return;
            }

            // 3. 直接使用传入的目录配置信息创建批注关联
            String annotationKey = directoryConfiguration.getDirectoryCode() + "_"
                    + directoryConfiguration.getDirectoryName() + "_"
                    + directoryConfiguration.getEmrRuleType();

            DocumentExportRecordRuleDetail matchedAnnotation = latestAnnotationMap.get(annotationKey);
            if (matchedAnnotation == null) {
                return; // 没有找到匹配的历史批注信息
            }

            // 4. 检查是否已存在批注记录，避免重复创建
            long existingCount = documentExportRecordRuleDetailService.count(
                    Wrappers.<DocumentExportRecordRuleDetail>lambdaQuery()
                            .eq(DocumentExportRecordRuleDetail::getExportRecordId, exportRecordId)
                            .eq(DocumentExportRecordRuleDetail::getDirectoryCode,
                                    directoryConfiguration.getDirectoryCode())
                            .eq(DocumentExportRecordRuleDetail::getDirectoryName,
                                    directoryConfiguration.getDirectoryName())
                            .eq(DocumentExportRecordRuleDetail::getEmrRuleType,
                                    directoryConfiguration.getEmrRuleType()));

            if (existingCount > 0) {
                return; // 已存在批注记录，无需重复创建
            }

            // 5. 创建新的批注记录
            DocumentExportRecordRuleDetail newAnnotation = DocumentExportRecordRuleDetail.builder()
                    .exportRecordId(exportRecordId)
                    .directoryCode(directoryConfiguration.getDirectoryCode())
                    .directoryName(directoryConfiguration.getDirectoryName())
                    .emrRuleType(directoryConfiguration.getEmrRuleType())
                    .problemDataRemarks1(matchedAnnotation.getProblemDataRemarks1())
                    .problemDataRemarks2(matchedAnnotation.getProblemDataRemarks2())
                    .problemDataRemarks3(matchedAnnotation.getProblemDataRemarks3())
                    .problemDataRemarks4(matchedAnnotation.getProblemDataRemarks4())
                    .build();

            // 6. 保存批注信息
            documentExportRecordRuleDetailService.save(newAnnotation);

        } catch (Exception e) {
            // 异常不影响主流程，只记录日志
            System.err.println("处理章节批注信息关联时发生异常：" + e.getMessage());
        }
    }

    /**
     * 初始化最新导出记录的批注信息映射
     *
     * @param currentExportRecordId 当前导出记录ID
     * @param projectId             项目ID
     * @return 批注信息映射
     */
    private Map<String, DocumentExportRecordRuleDetail> initLatestAnnotationMap(String currentExportRecordId,
            String projectId) {
        try {
            // 获取项目下最新的导出记录（排除当前导出记录）
            DocumentExportRecord latestExportRecord = documentExportRecordService.getOne(
                    Wrappers.<DocumentExportRecord>lambdaQuery()
                            .eq(DocumentExportRecord::getProjectId, projectId)
                            .ne(DocumentExportRecord::getId, currentExportRecordId)
                            .orderByDesc(DocumentExportRecord::getCreateTime)
                            .last("LIMIT 1"));

            if (latestExportRecord == null) {
                return new HashMap<>();
            }

            // 获取最新导出记录的批注信息
            List<DocumentExportRecordRuleDetail> latestAnnotations = documentExportRecordRuleDetailService.list(
                    Wrappers.<DocumentExportRecordRuleDetail>lambdaQuery()
                            .eq(DocumentExportRecordRuleDetail::getExportRecordId, latestExportRecord.getId())
                            .and(wrapper -> wrapper
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks1)
                                    .or()
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks2)
                                    .or()
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks3)
                                    .or()
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks4)));

            // 建立索引映射
            Map<String, DocumentExportRecordRuleDetail> annotationMap = new HashMap<>();
            for (DocumentExportRecordRuleDetail annotation : latestAnnotations) {
                String key = annotation.getDirectoryCode() + "_" + annotation.getDirectoryName() + "_"
                        + annotation.getEmrRuleType();
                annotationMap.put(key, annotation);
            }

            return annotationMap;

        } catch (Exception e) {
            System.err.println("初始化批注信息映射时发生异常：" + e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 清理指定导出记录的批注信息缓存
     * 手动清理时调用
     *
     * @param exportRecordId 导出记录ID
     */
    public static void clearAnnotationCache(String exportRecordId) {
        exportRecordAnnotationCache.remove(exportRecordId);
        exportRecordTimestampCache.remove(exportRecordId);
    }

    /**
     * 清理所有批注信息缓存
     * 可在系统维护时调用
     */
    public static void clearAllAnnotationCache() {
        exportRecordAnnotationCache.clear();
        exportRecordTimestampCache.clear();
    }

    /**
     * 清理指定天数之前的批注信息缓存
     * 定时任务调用，清理过期数据
     *
     * @param daysAgo 清理多少天前的数据
     * @return 清理的记录数量
     */
    public static int clearExpiredAnnotationCache(int daysAgo) {
        long expiredTime = System.currentTimeMillis() - (daysAgo * 24L * 60L * 60L * 1000L);
        int clearedCount = 0;

        // 使用迭代器安全地删除过期记录
        Iterator<Map.Entry<String, Long>> iterator = exportRecordTimestampCache.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            if (entry.getValue() < expiredTime) {
                String exportRecordId = entry.getKey();
                iterator.remove(); // 删除时间戳记录
                exportRecordAnnotationCache.remove(exportRecordId); // 删除批注缓存
                clearedCount++;
            }
        }

        return clearedCount;
    }

    /**
     * 获取当前缓存统计信息
     * 用于监控和调试
     *
     * @return 缓存统计信息
     */
    public static Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("annotationCacheSize", exportRecordAnnotationCache.size());
        stats.put("timestampCacheSize", exportRecordTimestampCache.size());

        // 计算最老的记录时间
        if (!exportRecordTimestampCache.isEmpty()) {
            long oldestTime = exportRecordTimestampCache.values().stream()
                    .min(Long::compareTo)
                    .orElse(System.currentTimeMillis());
            long ageInHours = (System.currentTimeMillis() - oldestTime) / (60L * 60L * 1000L);
            stats.put("oldestRecordAgeInHours", ageInHours);
        }

        return stats;
    }

}
