package com.jykj.dqm.empiricalmaterial.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContentDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContentQuery;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialEvaluationContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = {"实证材料内容配置"})
@RestController
@RequestMapping("/emm/empiricalmaterial/evaluationcontent")
public class EmpiricalMaterialEvaluationContentController {
    @Autowired
    private EmpiricalMaterialEvaluationContentService evaluationContentService;

    @LogRemark(operate = "新增实证材料内容", module = "实证材料内容配置")
    @ApiOperation(value = "新增实证材料内容", notes = "实证材料内容配置")
    @PostMapping("/save")
    public R save(@Validated @RequestBody EmpiricalMaterialEvaluationContentDTO evaluationContent) {
        return evaluationContentService.save(evaluationContent);
    }

    @ApiOperation(value = "查询实证材料内容", notes = "实证材料内容配置")
    @PostMapping("/query")
    public R query(@RequestBody EmpiricalMaterialEvaluationContentQuery evaluationContent) {
        return evaluationContentService.query(evaluationContent);
    }
}
