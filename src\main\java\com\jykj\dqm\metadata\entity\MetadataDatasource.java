package com.jykj.dqm.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据源配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:35:34
 */
@ApiModel(value = "数据源配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_METADATA_DATASOURCE")
public class MetadataDatasource {
    /**
     * 数据源ID
     */
    @TableId(value = "DATA_SOURCE_ID", type = IdType.AUTO)
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据源名称
     */
    @TableField(value = "DATA_SOURCE_NAME")
    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    /**
     * 描述
     */
    @TableField(value = "DATA_SOURCE_DESCRIBE")
    @ApiModelProperty(value = "描述")
    private String dataSourceDescribe;

    /**
     * 数据库类型
     */
    @TableField(value = "DATABASE_TYPE")
    @ApiModelProperty(value = "数据库类型")
    private String databaseType;

    /**
     * 数据库驱动
     */
    @TableField(value = "DATABASE_DRIVER")
    @ApiModelProperty(value = "数据库驱动")
    private String databaseDriver;

    /**
     * 数据库访问URL
     */
    @TableField(value = "DATABASE_URL")
    @ApiModelProperty(value = "数据库访问URL")
    private String databaseUrl;

    /**
     * 用户名
     */
    @TableField(value = "DATABASE_USER")
    @ApiModelProperty(value = "用户名")
    private String databaseUser;

    /**
     * 密码(加密后)
     */
    @TableField(value = "DATABASE_PWD")
    @ApiModelProperty(value = "密码(加密后)")
    private String databasePwd;

    /**
     * 采集数据库名称
     */
    @TableField(value = "DATABASE_NAME")
    @ApiModelProperty(value = "采集数据库名称")
    private String databaseName;

    /**
     * SCHEMA
     */
    @TableField(value = "DATABASE_SCHEMA")
    @ApiModelProperty(value = "SCHEMA")
    private String databaseSchema;

    /**
     * 测试语句
     */
    @TableField(value = "TESTSQL")
    @ApiModelProperty(value = "测试语句")
    private String testsql;

    /**
     * 驱动包路径
     */
    @TableField(value = "DRIVER_FILES")
    @ApiModelProperty(value = "驱动包路径")
    private String driverFiles;

    /**
     * 任务组ID
     */
    @TableField(value = "TASK_GROUP_ID")
    @ApiModelProperty(value = "任务组ID")
    private String taskGroupId;

    /**
     * 采集系统代码
     */
    @TableField(value = "SYS_CODE")
    @ApiModelProperty(value = "采集系统代码")
    private String sysCode;

    /**
     * 采集系统名称
     */
    @TableField(value = "SYS_NAME")
    @ApiModelProperty(value = "采集系统名称")
    private String sysName;

    /**
     * 是否需要跨库关联查询(0:否,1:是)
     */
    @TableField(value = "NEED_CROSS_QUERY")
    @ApiModelProperty(value = "是否需要跨库关联查询(0:否,1:是)")
    private Integer needCrossQuery;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 后端-->前端。
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    // @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") //前端-->后端显示
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 后端-->前端。
    private Date updateTime;

    /**
     * 操作人
     */
    @TableField(value = "OPERATION_PERSON")
    @ApiModelProperty(value = "操作人")
    private String operationPerson;
}