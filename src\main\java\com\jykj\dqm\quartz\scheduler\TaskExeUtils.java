package com.jykj.dqm.quartz.scheduler;

import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.entity.TaskProcessResult;
import com.jykj.dqm.quartz.service.JobLogService;
import com.jykj.dqm.quartz.service.TaskJobInterface;
import com.jykj.dqm.utils.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/***
 * 任务执行的工具类
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class TaskExeUtils {
    private static Map<String, TaskJobInterface> tasksList = new HashMap<String, TaskJobInterface>();

    public Map<String, TaskJobInterface> getTasksList() {
        return TaskExeUtils.tasksList;
    }

    @Autowired
    public void setTasksList(TaskJobInterface[] tasks) {
        for (TaskJobInterface taskJobInterface : tasks) {
            TaskExeUtils.tasksList.put(taskJobInterface.getTaskType(), taskJobInterface);
        }
    }

    @Autowired
    private JobLogService jobLogService;

    private static TaskExeUtils taskExeUtils;

    @PostConstruct
    public void init() {
        taskExeUtils = this;
        taskExeUtils.jobLogService = this.jobLogService;
        System.out.println("---------------------------");
    }

    public static void processTask(ScheduleJobInfo jobInfo) {
        if (jobInfo == null) {
            throw new BusinessException("任务不存在！");
        }
        if (tasksList != null) {
            TaskJobInterface taskInterface = tasksList.get(jobInfo.getTaskType());
            if (taskInterface != null) {
                String logId = IdUtils.getID();
                taskExeUtils.jobLogService.startJobLog(jobInfo, logId);
                TaskProcessResult result = taskInterface.processTask(jobInfo);
                taskExeUtils.jobLogService.endJobLog(jobInfo, logId, result);
            } else {
                log.debug("调度任务类型为[" + jobInfo.getTaskType() + "]没有配置具体的处理类");
            }
        } else {
            log.info("任务执行处理类列表未初始化");
        }
    }
}
