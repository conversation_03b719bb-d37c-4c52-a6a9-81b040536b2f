package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.LevelDictionaryMapper;
import com.jykj.dqm.emr.entity.LevelDictionary;
import com.jykj.dqm.emr.entity.LevelDictionaryQuery;
import com.jykj.dqm.emr.manager.PermissionLevelCodeUtil;
import com.jykj.dqm.emr.service.LevelDictionaryService;
import com.jykj.dqm.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 等级字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 9:51:53
 */
@Service
public class LevelDictionaryServiceImpl extends ServiceImpl<LevelDictionaryMapper, LevelDictionary> implements LevelDictionaryService {
    @Override
    public R add(LevelDictionary levelDictionary) {
        this.save(levelDictionary);
        return RUtil.success();
    }

    @Override
    public R update(LevelDictionary levelDictionary) {
        this.updateById(levelDictionary);
        return RUtil.success();
    }

    @Override
    public R delete(List<Long> ids) {
        this.removeBatchByIds(ids);
        return RUtil.success();
    }

    @Override
    public R query(LevelDictionaryQuery levelDictionaryQuery) {
        PageHelper.startPage(levelDictionaryQuery.getPageNum(), levelDictionaryQuery.getPageSize());
        LambdaQueryWrapper<LevelDictionary> lambdaQueryWrapper = Wrappers.lambdaQuery();
        //单独写if，因为Children lt(boolean condition, R column, Object val);就算condition==false，后面的var表达式也会运算
        if (StrUtil.isNotBlank(levelDictionaryQuery.getStartTime())) {
            lambdaQueryWrapper.ge(LevelDictionary::getCreateTime, DateUtil.parse(levelDictionaryQuery.getStartTime(), DateUtils.YYYY_MM_DD));
        }
        if (StrUtil.isNotBlank(levelDictionaryQuery.getEndTime())) {
            levelDictionaryQuery.setEndTime(levelDictionaryQuery.getEndTime() + " 23:59:59");
            lambdaQueryWrapper.le(LevelDictionary::getUpdateTime, DateUtil.parse(levelDictionaryQuery.getEndTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        lambdaQueryWrapper.orderByAsc(LevelDictionary::getLevelCode);
        List<LevelDictionary> list = this.list(lambdaQueryWrapper);
        PageInfo<LevelDictionary> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }

    @Override
    public R queryAllow(LevelDictionaryQuery levelDictionaryQuery) {
        PageHelper.startPage(levelDictionaryQuery.getPageNum(), levelDictionaryQuery.getPageSize());
        LambdaQueryWrapper<LevelDictionary> lambdaQueryWrapper = Wrappers.lambdaQuery();
        //单独写if，因为Children lt(boolean condition, R column, Object val);就算condition==false，后面的var表达式也会运算
        if (StrUtil.isNotBlank(levelDictionaryQuery.getStartTime())) {
            lambdaQueryWrapper.ge(LevelDictionary::getCreateTime, DateUtil.parse(levelDictionaryQuery.getStartTime(), DateUtils.YYYY_MM_DD));
        }
        if (StrUtil.isNotBlank(levelDictionaryQuery.getEndTime())) {
            levelDictionaryQuery.setEndTime(levelDictionaryQuery.getEndTime() + " 23:59:59");
            lambdaQueryWrapper.le(LevelDictionary::getUpdateTime, DateUtil.parse(levelDictionaryQuery.getEndTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        lambdaQueryWrapper.le(LevelDictionary::getLevelCode, PermissionLevelCodeUtil.getLevelCode());
        lambdaQueryWrapper.orderByAsc(LevelDictionary::getLevelCode);
        List<LevelDictionary> list = this.list(lambdaQueryWrapper);
        PageInfo<LevelDictionary> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }
}
