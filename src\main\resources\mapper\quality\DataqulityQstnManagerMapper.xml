<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quality.dao.DataqulityQstnManagerMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.quality.entity.DataqulityQstnManager">
        <!--@mbg.generated-->
        <!--@Table DQM_DATAQULITY_QSTN_MANAGER-->
        <id column="PK_ID" jdbcType="INTEGER" property="pkId"/>
        <result column="DATA_QLTY_QSTN_NM" jdbcType="VARCHAR" property="dataQltyQstnNm"/>
        <result column="QSTN_PRDUS_SYS_CD" jdbcType="VARCHAR" property="qstnPrdusSysCd"/>
        <result column="QSTN_PRDUS_SYS_NM" jdbcType="VARCHAR" property="qstnPrdusSysNm"/>
        <result column="DB_NM" jdbcType="VARCHAR" property="dbNm"/>
        <result column="DATA_QLTY_RVW_RULE_NO" jdbcType="VARCHAR" property="dataQltyRvwRuleNo"/>
        <result column="DATA_TOTAL_NUM" jdbcType="INTEGER" property="dataTotalNum"/>
        <result column="DATA_QSTN_NUM" jdbcType="INTEGER" property="dataQstnNum"/>
        <result column="QSTN_CHECK_TIME" jdbcType="TIMESTAMP" property="qstnCheckTime"/>
        <result column="QSTN_CLSF_CD" jdbcType="VARCHAR" property="qstnClsfCd"/>
        <result column="QSTN_RSN_CLSF_CD" jdbcType="VARCHAR" property="qstnRsnClsfCd"/>
        <result column="RCTFCTN_SCHEM_TYP_CD" jdbcType="VARCHAR" property="rctfctnSchemTypCd"/>
        <result column="RCTFCTN_OPINIONS" jdbcType="VARCHAR" property="rctfctnOpinions"/>
        <result column="QSTN_RCTFCTN_STUS_CD" jdbcType="VARCHAR" property="qstnRctfctnStusCd"/>
        <result column="QSTN_HANDLE_TIME" jdbcType="TIMESTAMP" property="qstnHandleTime"/>
        <result column="PUSH_MESSAGE_RECEIVER" jdbcType="VARCHAR" property="pushMessageReceiver"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        PK_ID,
        DATA_QLTY_QSTN_NM,
        QSTN_PRDUS_SYS_CD,
        QSTN_PRDUS_SYS_NM,
        DB_NM,
        DATA_QLTY_RVW_RULE_NO,
        DATA_TOTAL_NUM,
        DATA_QSTN_NUM,
        QSTN_CHECK_TIME,
        QSTN_CLSF_CD,
        QSTN_RSN_CLSF_CD,
        RCTFCTN_SCHEM_TYP_CD,
        RCTFCTN_OPINIONS,
        QSTN_RCTFCTN_STUS_CD,
        QSTN_HANDLE_TIME,
        PUSH_MESSAGE_RECEIVER
    </sql>

    <insert id="insertSelective" keyColumn="PK_ID" keyProperty="pkId"
            parameterType="com.jykj.dqm.quality.entity.DataqulityQstnManager" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into DQM_DATAQULITY_QSTN_MANAGER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataQltyQstnNm != null">
                DATA_QLTY_QSTN_NM,
            </if>
            <if test="qstnPrdusSysCd != null">
                QSTN_PRDUS_SYS_CD,
            </if>
            <if test="qstnPrdusSysNm != null">
                QSTN_PRDUS_SYS_NM,
            </if>
            <if test="dbNm != null">
                DB_NM,
            </if>
            <if test="dataQltyRvwRuleNo != null">
                DATA_QLTY_RVW_RULE_NO,
            </if>
            <if test="dataTotalNum != null">
                DATA_TOTAL_NUM,
            </if>
            <if test="dataQstnNum != null">
                DATA_QSTN_NUM,
            </if>
            <if test="qstnCheckTime != null">
                QSTN_CHECK_TIME,
            </if>
            <if test="qstnClsfCd != null">
                QSTN_CLSF_CD,
            </if>
            <if test="qstnRsnClsfCd != null">
                QSTN_RSN_CLSF_CD,
            </if>
            <if test="rctfctnSchemTypCd != null">
                RCTFCTN_SCHEM_TYP_CD,
            </if>
            <if test="rctfctnOpinions != null">
                RCTFCTN_OPINIONS,
            </if>
            <if test="qstnRctfctnStusCd != null">
                QSTN_RCTFCTN_STUS_CD,
            </if>
            <if test="qstnHandleTime != null">
                QSTN_handle_TIME,
            </if>
            <if test="pushMessageReceiver != null">
                PUSH_MESSAGE_RECEIVER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataQltyQstnNm != null">
                #{dataQltyQstnNm,jdbcType=VARCHAR},
            </if>
            <if test="qstnPrdusSysCd != null">
                #{qstnPrdusSysCd,jdbcType=VARCHAR},
            </if>
            <if test="qstnPrdusSysNm != null">
                #{qstnPrdusSysNm,jdbcType=VARCHAR},
            </if>
            <if test="dbNm != null">
                #{dbNm,jdbcType=VARCHAR},
            </if>
            <if test="dataQltyRvwRuleNo != null">
                #{dataQltyRvwRuleNo,jdbcType=VARCHAR},
            </if>
            <if test="dataTotalNum != null">
                #{dataTotalNum,jdbcType=INTEGER},
            </if>
            <if test="dataQstnNum != null">
                #{dataQstnNum,jdbcType=INTEGER},
            </if>
            <if test="qstnCheckTime != null">
                #{qstnCheckTime,jdbcType=TIMESTAMP},
            </if>
            <if test="qstnClsfCd != null">
                #{qstnClsfCd,jdbcType=VARCHAR},
            </if>
            <if test="qstnRsnClsfCd != null">
                #{qstnRsnClsfCd,jdbcType=VARCHAR},
            </if>
            <if test="rctfctnSchemTypCd != null">
                #{rctfctnSchemTypCd,jdbcType=VARCHAR},
            </if>
            <if test="rctfctnOpinions != null">
                #{rctfctnOpinions,jdbcType=VARCHAR},
            </if>
            <if test="qstnRctfctnStusCd != null">
                #{qstnRctfctnStusCd,jdbcType=VARCHAR},
            </if>
            <if test="qstnHandleTime != null">
                #{qstnHandleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushMessageReceiver != null">
                #{pushMessageReceiver,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jykj.dqm.quality.entity.DataqulityQstnManager">
        <!--@mbg.generated-->
        update DQM_DATAQULITY_QSTN_MANAGER
        <set>
            <if test="dataQltyQstnNm != null">
                DATA_QLTY_QSTN_NM = #{dataQltyQstnNm,jdbcType=VARCHAR},
            </if>
            <if test="qstnPrdusSysCd != null">
                QSTN_PRDUS_SYS_CD = #{qstnPrdusSysCd,jdbcType=VARCHAR},
            </if>
            <if test="qstnPrdusSysNm != null">
                QSTN_PRDUS_SYS_NM = #{qstnPrdusSysNm,jdbcType=VARCHAR},
            </if>
            <if test="dbNm != null">
                DB_NM = #{dbNm,jdbcType=VARCHAR},
            </if>
            <if test="dataQltyRvwRuleNo != null">
                DATA_QLTY_RVW_RULE_NO = #{dataQltyRvwRuleNo,jdbcType=VARCHAR},
            </if>
            <if test="dataTotalNum != null">
                DATA_TOTAL_NUM = #{dataTotalNum,jdbcType=INTEGER},
            </if>
            <if test="dataQstnNum != null">
                DATA_QSTN_NUM = #{dataQstnNum,jdbcType=INTEGER},
            </if>
            <if test="qstnCheckTime != null">
                QSTN_CHECK_TIME = #{qstnCheckTime,jdbcType=TIMESTAMP},
            </if>
            <if test="qstnClsfCd != null">
                QSTN_CLSF_CD = #{qstnClsfCd,jdbcType=VARCHAR},
            </if>
            <if test="qstnRsnClsfCd != null">
                QSTN_RSN_CLSF_CD = #{qstnRsnClsfCd,jdbcType=VARCHAR},
            </if>
            <if test="rctfctnSchemTypCd != null">
                RCTFCTN_SCHEM_TYP_CD = #{rctfctnSchemTypCd,jdbcType=VARCHAR},
            </if>
            <if test="rctfctnOpinions != null">
                RCTFCTN_OPINIONS = #{rctfctnOpinions,jdbcType=VARCHAR},
            </if>
            <if test="qstnRctfctnStusCd != null">
                QSTN_RCTFCTN_STUS_CD = #{qstnRctfctnStusCd,jdbcType=VARCHAR},
            </if>
            <if test="qstnHandleTime != null">
                QSTN_HANDLE_TIME = #{qstnHandleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushMessageReceiver != null">
                PUSH_MESSAGE_RECEIVER = #{pushMessageReceiver,jdbcType=VARCHAR},
            </if>
        </set>
        where PK_ID = #{pkId,jdbcType=INTEGER}
    </update>

    <select id="getAllSysAndDb" resultType="java.util.Map">
        SELECT DISTINCT QSTN_PRDUS_SYS_CD "sysCode", QSTN_PRDUS_SYS_NM "sysName", DB_NM "dbNm"
        FROM DQM_DATAQULITY_QSTN_MANAGER
    </select>

    <select id="queryList" resultType="com.jykj.dqm.quality.entity.DataqulityQstnManagerVO">
        SELECT t1.*,
               t2.CHECK_RULE_NAME,
               t2.CHECK_RULE_FATHER_TYPE,
               t2.CHECK_RULE_TYPE,
               t2.CHECK_RULE_TABLE_OR_VIEW,
               t2.CHECK_RULE_COLUMN,
               t2.TOTAL_NM_SQL,
               t2.PB_SUBSIDIARY_SQL,
               t2.QUESTION_NM_SQL,
               t2.QST_TYPE
        FROM DQM_DATAQULITY_QSTN_MANAGER t1
                 LEFT JOIN DQM_DATAQUALITY_CHECK_RULE t2 ON
            t1.DATA_QLTY_RVW_RULE_NO = t2.CHECK_RULE_ID
        <where>
            <if test="sysCode != null and sysCode != ''">
                t2.SYS_CODE = #{sysCode}
            </if>
            <if test="dbNm != null and dbNm != ''">
                and t1.DB_NM = #{dbNm}
            </if>
            <if test="checkRuleTableOrView != null and checkRuleTableOrView != ''">
                and t2.CHECK_RULE_TABLE_OR_VIEW = #{checkRuleTableOrView}
            </if>
            <if test="checkRuleColumn != null and checkRuleColumn != ''">
                and t2.CHECK_RULE_COLUMN = #{checkRuleColumn}
            </if>
            <if test="checkRuleFatherType != null and checkRuleFatherType != ''">
                and t2.CHECK_RULE_FATHER_TYPE = #{checkRuleFatherType}
            </if>
            <if test="checkRuleType != null and checkRuleType != ''">
                and t2.CHECK_RULE_TYPE = #{checkRuleType}
            </if>
            <if test="_databaseId == 'mysql' and dataQltyQstnNm != null and dataQltyQstnNm != ''">
                and t1.DATA_QLTY_QSTN_NM like CONCAT('%', #{dataQltyQstnNm}, '%')
            </if>
            <if test="_databaseId == 'oracle' and dataQltyQstnNm != null and dataQltyQstnNm != ''">
                and t1.DATA_QLTY_QSTN_NM like '%'|| #{dataQltyQstnNm} || '%'
            </if>
            <if test="checkDt != null">
                and t1.QSTN_CHECK_TIME >= #{checkDt} and t1.QSTN_CHECK_TIME &lt; #{checkEndDt}
            </if>
            <if test="handleDt != null">
                and t1.QSTN_HANDLE_TIME >= #{handleDt} and t1.QSTN_HANDLE_TIME &lt; #{handleDt}
            </if>
            <if test="qstnRctfctnStusCds!= null and qstnRctfctnStusCds.size()>0">
                AND t1.QSTN_RCTFCTN_STUS_CD IN
                <foreach collection="qstnRctfctnStusCds" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY QSTN_CHECK_TIME DESC
    </select>

    <select id="getProblemDataTrends" resultType="java.util.Map" databaseId="mysql">
        SELECT sum(DATA_QSTN_NUM) TOTAL, STR_TO_DATE(QSTN_CHECK_TIME, '%Y-%m-%d') DT
        FROM DQM_DATAQULITY_QSTN_MANAGER
        <where>
            <if test="startDate != null and startDate != ''">
                and QSTN_CHECK_TIME >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and QSTN_CHECK_TIME &lt;= #{endDate}
            </if>
        </where>
        GROUP BY DT
        ORDER BY DT
    </select>

    <select id="getProblemDataTrends" resultType="java.util.Map" databaseId="oracle">
        SELECT sum(DATA_QSTN_NUM) TOTAL, TO_CHAR(QSTN_CHECK_TIME, 'yyyy-MM-dd') DT
        FROM DQM_DATAQULITY_QSTN_MANAGER
        <where>
            <if test="startDate != null and startDate != ''">
                and QSTN_CHECK_TIME >= to_date(#{startDate}, 'yyyy/mm/dd hh24:mi:ss')
            </if>
            <if test="endDate != null and endDate != ''">
                and QSTN_CHECK_TIME &lt;= to_date(#{endDate}, 'yyyy/mm/dd hh24:mi:ss')
            </if>
        </where>
        GROUP BY TO_CHAR(QSTN_CHECK_TIME, 'yyyy-MM-dd')
        ORDER BY DT
    </select>

    <select id="getTheQstnProportion" resultType="java.util.Map">
        SELECT sum(DATA_QSTN_NUM) TOTAL, DATA_QLTY_QSTN_NM QSTN_NM
        FROM DQM_DATAQULITY_QSTN_MANAGER
        GROUP BY DATA_QLTY_QSTN_NM
        ORDER BY TOTAL DESC
    </select>
</mapper>