package com.jykj.dqm.common;


/**
 * 异常枚举
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/7/25 13:26
 */
public enum ExceptionEnum implements ErrorType {
    /*1000～1999 区间表示参数错误*/
    /*2000～2999 区间表示用户错误*/
    /*3000～3999 区间表示接口异常*/
    SUCCESS(1, "操作成功"),
    ERROR(0, "操作失败"),

    // 校验公共错误
    CHECK_ERROR(1000, "校验公共错误"),

    // token错误
    TOKEN_ERROR(2000, "token错误"),
    NO_TOKEN_ERROR(2001, "没有token，请重新登录"),
    USER_PASSWORD_ERROR(2002, "用户名和密码错误，请重新输入"),
    USER_UPDATE_PASS_ERROR(2003, "当前密码输入错误，请重新输入"),
    USER_UPDATE_PASS2_ERROR(2004, "两次密码输入不一致，请重新输入"),
    USER_LOCKED_ERROR(2005, "用户已锁定"),
    USER_NOROLE_LOCKED_ERROR(2006, "没有角色"),
    USER_NOPERMISSION_LOCKED_ERROR(2007, "没有权限"),
    USER_PASSWORD_ALREADY_EXPIRED(2008, "密码已经过期"),
    USER_PASSWORD_WILL_EXPIRE_SOON(2009, "密码即将过期,请修改密码"),

    // 其他exe 错误
    OTHER_ERROR(3000, "其他exe错误"),
    CRON_EXPRESSION_ERROR(3001, "cron表达式不正确"),
    SCHEDULER_EXPRESSION_ERROR(3002, "调度异常");

    /**
     * 错误码
     */
    private Integer code;
    /**
     * 错误信息
     */
    private String msg;

    /**
     * 设置异常枚举值
     *
     * @param code 错误码
     * @param msg  错误信息
     */
    ExceptionEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     * <AUTHOR>
     */
    @Override
    public String getCode() {
        return this.code + "";
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     * <AUTHOR>
     */
    @Override
    public String getMesg() {
        return msg;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     * <AUTHOR>
     */
    public String getMsg() {
        return msg;
    }
}

