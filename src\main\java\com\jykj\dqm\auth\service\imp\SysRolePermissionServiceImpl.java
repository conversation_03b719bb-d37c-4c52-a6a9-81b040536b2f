package com.jykj.dqm.auth.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.auth.dao.SysRolePermissionMapper;
import com.jykj.dqm.auth.entity.PermissionVo;
import com.jykj.dqm.auth.entity.SysRolePermission;
import com.jykj.dqm.auth.service.SysRolePermissionService;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Service
@Slf4j
public class SysRolePermissionServiceImpl extends ServiceImpl<SysRolePermissionMapper, SysRolePermission> implements SysRolePermissionService {

    @Autowired
    private SysRolePermissionMapper sysRolePermissionMapper;

    /**
     * 新增角色权限
     *
     * @param sysRolePermission SysRolePermission
     * @return Result
     */
    @Override
    public R add(SysRolePermission sysRolePermission) {
        sysRolePermissionMapper.add(sysRolePermission);
        return RUtil.success();
    }

    /**
     * 查询所有用户角色
     *
     * @param page 当前页
     * @param size 每页数量
     * @return Result
     */
    @Override
    public R queryList(Integer page, Integer size) {
        if (page == null || size == null) {
            throw new BusinessException("参数错误，请检查page和size！");
        }
        PageHelper.startPage(page, size);
        List<SysRolePermission> list = sysRolePermissionMapper.queryList();
        PageInfo<SysRolePermission> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }

    /**
     * 通过角色Id查询角色权限
     *
     * @param roleId 角色Id
     * @return List<SysRolePermission>
     */
    @Override
    public List<SysRolePermission> queryByRoleId(Integer roleId) {
        return this.list(new QueryWrapper<SysRolePermission>().eq("role_id", roleId).eq("sys_id", Constant.SYS_NAME));
    }

    /**
     * 删除角色权限
     *
     * @param roleId 角色Id
     */
    @Override
    public void deleteByRoleId(String roleId) {
        sysRolePermissionMapper.deleteByRoleId(roleId);
    }

    /**
     * 通过角色Id添加角色权限
     *
     * @param roleId 角色Id
     * @param list   List<PermissionVo>
     */
    @Override
    public void addByRoleId(String roleId, List<PermissionVo> list) {
        List<Integer> permissionIds = getPermissionList(list, new ArrayList<>());
        permissionIds.stream().forEach(permissionId -> {
            SysRolePermission sysRolePermission = new SysRolePermission();
            sysRolePermission.setPermissionId(permissionId + "");
            sysRolePermission.setRoleId(roleId);
            sysRolePermissionMapper.add(sysRolePermission);
        });

    }

    /**
     * 获取权限Id
     *
     * @param list          List<PermissionVo> list
     * @param permissionIds ArrayList<Integer>
     * @return List<Integer>
     */
    private List<Integer> getPermissionList(List<PermissionVo> list, ArrayList<Integer> permissionIds) {
        for (PermissionVo permissionVo : list) {
            Integer permissionId = permissionVo.getPermissionId();
            permissionIds.add(permissionId);
            if (permissionVo.getChildren() != null && permissionVo.getChildren().size() != 0) {
                getPermissionList(permissionVo.getChildren(), permissionIds);
            }
        }
        return permissionIds;
    }


}
