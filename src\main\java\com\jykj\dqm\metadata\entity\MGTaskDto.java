package com.jykj.dqm.metadata.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 元数据采集任务配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 14:27:56
 */
@ApiModel(value = "元数据采集任务配置")
@Data
public class MGTaskDto {
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private String jobId;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String jobName;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    private String jobDesc;

    /**
     * 任务分组ID
     */
    @ApiModelProperty(value = "任务分组ID")
    private String jobGroupId;
    /**
     * 元模型类型
     */
    @ApiModelProperty(value = "元模型类型")
    private String taskMetamodelType;

    /**
     * 过滤类型
     */
    @ApiModelProperty(value = "过滤类型")
    private String rosterType;

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    /**
     * 数据源编码（已经配置的数据源不会在配置任务中再次显示）
     */
    @ApiModelProperty(value = "数据源编码（已经配置的数据源不会在配置任务中再次显示）")
    private String dataSourceId;
    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private String jobStartTime;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String jobStartDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String jobEndDate;


    /**
     * 执行方式
     */
    @ApiModelProperty(value = "执行方式")
    private String jobExeType;

    /**
     * 间隔时间
     */
    @ApiModelProperty(value = "间隔时间")
    private String jobSpan;

    /**
     * 执行频率
     */
    @ApiModelProperty(value = "执行频率")
    private String jobExeRate;

    /**
     * 周执行星期
     */
    @ApiModelProperty(value = "周执行星期")
    private String dayOfWeek;
    /**
     * 月的执行日期
     */
    @ApiModelProperty(value = "月的执行日期")
    private String dayOfMonth;
    /**
     * 月的执行周次
     */
    @ApiModelProperty(value = "月的执行周次")
    private String weekTime;
    /**
     * 月的执行星期
     */
    @ApiModelProperty(value = "月的执行星期")
    private String dayOfWeek2;
    /**
     * 月的执行方式
     */
    @ApiModelProperty(value = "月的执行方式")
    private String chkType;
    /**
     * 执行的月份
     */
    @ApiModelProperty(value = "执行的月份")
    private String chkMonth;
}
