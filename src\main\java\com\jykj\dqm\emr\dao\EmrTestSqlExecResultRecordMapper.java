package com.jykj.dqm.emr.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.emr.entity.EmrTestSqlExecResultRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_TEST_SQL_EXEC_RESULT_RECORD(测试SQL执行结果（只保留一次数据）)】的数据库操作Mapper
 * @createDate 2024-03-29 15:19:03
 * @Entity com.jykj.dqm.emr.entity.EmrTestSqlExecResultRecord
 */
@Mapper
public interface EmrTestSqlExecResultRecordMapper extends BaseMapper<EmrTestSqlExecResultRecord> {

}
