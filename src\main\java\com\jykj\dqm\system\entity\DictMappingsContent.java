package com.jykj.dqm.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 术语映射内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 14:26:57
 */
@ApiModel(value = "术语映射内容")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DICT_MAPPINGS_CONTENT")
public class DictMappingsContent implements Serializable {
    /**
     * 序号
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "序号")
    private Integer id;

    /**
     * 术语编码
     */
    @TableField(value = "TERM_ID")
    @ApiModelProperty(value = "术语编码")
    private String termId;

    /**
     * 映射类型
     */
    @TableField(value = "MAPPINGS_TYPE")
    @ApiModelProperty(value = "映射类型")
    private String mappingsType;

    /**
     * 源字段
     */
    @TableField(value = "SOURCE_FIELD")
    @ApiModelProperty(value = "源字段")
    private String sourceField;

    /**
     * 源字段编码
     */
    @TableField(value = "SOURCE_FIELD_CODE")
    @ApiModelProperty(value = "源字段编码")
    private String sourceFieldCode;

    /**
     * 目标字段
     */
    @TableField(value = "TARGET_FIELD")
    @ApiModelProperty(value = "目标字段")
    private String targetField;

    /**
     * 目标字段编码
     */
    @TableField(value = "TARGET_FIELD_CODE")
    @ApiModelProperty(value = "目标字段编码")
    private String targetFieldCode;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_BY")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATED_BY")
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATED_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    /**
     * 删除编辑
     */
    @TableField(value = "DELETED_FLAG")
    @ApiModelProperty(value = "删除编辑")
    private String deletedFlag;

    /**
     * 是否匹配
     */
    @TableField(value = "ISMATCH")
    @ApiModelProperty(value = "是否匹配")
    private String ismatch;

    private static final long serialVersionUID = 1L;
}