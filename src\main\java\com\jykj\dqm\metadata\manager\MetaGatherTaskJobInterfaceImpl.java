package com.jykj.dqm.metadata.manager;

import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.MetadataTaskInstance;
import com.jykj.dqm.metadata.service.MetadataTaskInstanceService;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.entity.TaskProcessResult;
import com.jykj.dqm.quartz.service.TaskJobInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class MetaGatherTaskJobInterfaceImpl implements TaskJobInterface {
    @Autowired
    private CollectionMetadataService collectionMetadataService;

    @Autowired
    private MetadataTaskInstanceService metadataTaskInstanceService;

    @Override
    public String getTaskType() {
        return "MG";
    }

    @Override
    public String getTaskDesc() {
        return "元数据采集";
    }

    @Override
    public TaskProcessResult processTask(ScheduleJobInfo jobData) {
        return processTaskSync(jobData);
    }

    private synchronized TaskProcessResult processTaskSync(ScheduleJobInfo jobData) {
        log.debug(getTaskDesc() + "启动");
        Date startDate = new Date();
        String taskGroupId = jobData.getJobId();
        R result = collectionMetadataService.exeCollectionTask(taskGroupId);

        TaskProcessResult taskResult = new TaskProcessResult();
        if (result.getStatus() == 0) {
            taskResult.setResultCode(0);
            taskResult.setMsg("OK");
        } else if (result.getStatus() == 1) {
            taskResult.setResultCode(1);
            taskResult.setMsg("相同元数据采集任务正在执行中……请稍后");
        } else {
            taskResult.setResultCode(2);
            taskResult.setMsg("元数据采集任务执行失败");
        }

        //处理执行中未记录日志场景
        if (taskResult.getResultCode() != 0) {
            MetadataTaskInstance metadataTaskInstance =
                    MetadataTaskInstance.builder()
                            .taskGroupId(taskGroupId)
                            .taskStartDt(startDate)
                            .taskEndDt(new Date())
                            .taskState("99")
                            .dataSourceId(0)
                            .taskResult(taskResult.getMsg())
                            .build();
            metadataTaskInstanceService.save(metadataTaskInstance);
        }
        return taskResult;
    }

}
