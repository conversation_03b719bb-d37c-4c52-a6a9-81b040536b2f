package com.jykj.dqm.auth.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.auth.entity.PermissionVo;
import com.jykj.dqm.auth.entity.SysRolePermission;

import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public interface SysRolePermissionService extends IService<SysRolePermission> {
    /**
     * 新增角色权限
     *
     * @param sysRolePermission SysRolePermission
     * @return Result
     */
    R add(SysRolePermission sysRolePermission);

    /**
     * 查询所有用户角色
     *
     * @param page 当前页
     * @param size 每页数量
     * @return Result
     */
    R queryList(Integer page, Integer size);

    /**
     * 通过角色Id查询角色权限
     *
     * @param roleId 角色Id
     * @return List<SysRolePermission>
     */
    List<SysRolePermission> queryByRoleId(Integer roleId);

    /**
     * 删除角色权限
     *
     * @param roleId 角色Id
     */
    void deleteByRoleId(String roleId);

    /**
     * 通过角色Id添加角色权限
     *
     * @param roleId 角色Id
     * @param list   List<PermissionVo>
     */
    void addByRoleId(String roleId, List<PermissionVo> list);
}
