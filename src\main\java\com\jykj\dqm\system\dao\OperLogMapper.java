package com.jykj.dqm.system.dao;

import com.jykj.dqm.common.OperLog;
import com.jykj.dqm.common.OperLogForm;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * 操作日志Mapper
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Mapper
public interface OperLogMapper {
    /**
     * 插入操作日志（不带if-test）
     *
     * @param record OperLog
     * @return 变更条数
     * <AUTHOR>
     */
    int insert(OperLog record);

    /**
     * 插入操作日志（带if-test）
     *
     * @param record OperLog
     * @return 变更条数
     * <AUTHOR>
     */
    int insertSelective(OperLog record);

    /**
     * 查询操作日志
     *
     * @param operLogQuery OperLogForm
     * @return List<OperLog>
     * <AUTHOR>
     */
    List<OperLog> queryLog(OperLogForm operLogQuery);

    /**
     * 清除操作日志
     *
     * @param date Date
     * @return 变更条数
     * <AUTHOR>
     */
    int clearLog(Date date);
}