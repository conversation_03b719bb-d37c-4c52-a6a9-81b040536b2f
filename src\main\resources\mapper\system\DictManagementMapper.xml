<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.system.dao.DictManagementMapper">
    <select id="queryDictList" resultType="com.jykj.dqm.system.entity.DictMappings">
        SELECT *
        FROM DQM_DICT_MAPPINGS
        <where>
            <if test="_databaseId == 'mysql' and term != null and term != ''">
                AND (TERM_ID like CONCAT( '%' ,#{term} ,'%') OR TERM_NAME like CONCAT( '%' ,#{term} ,'%'))
            </if>
            <if test="_databaseId == 'oracle' and term != null and term != ''">
                AND (TERM_ID like '%' || #{term} || '%' OR TERM_NAME like '%' || #{term} || '%')
            </if>
            <if test="mappingsType != null and mappingsType != ''">
                AND MAPPINGS_TYPE = #{mappingsType}
            </if>
            <if test="sysCode != null and sysCode != ''">
                AND SYS_CODE = #{sysCode}
            </if>
        </where>
    </select>


    <update id="truncateTable">
<!--        TRUNCATE TABLE ${tableName}-->
        DELETE FROM ${tableName}
    </update>

    <update id="syncDictMappingsData">
        INSERT INTO DQM_DICT_MAPPINGS(TERM_ID,TERM_NAME,MAPPINGS_TABLE,MAPPINGS_TYPE,CODE_FIELD,NAME_FIELD,MATCH_TYPE,BD_CODE,BD_NAME,SYS_CODE,SYS_NAME)
        SELECT a.TERM_ID, a.TERM_NAME,b.DICT_TABLE MAPPINGS_TABLE,a.MAPPINGS_TYPE,a.CODE_FIELD,a.NAME_FIELD,a.MATCH_TYPE,a.BD_CODE,a.BD_NAME,b.SYSTEM_ID as SYS_CODE, b.SYSTEM_NAME SYS_NAME
        FROM MDM_DICT_MAPPINGS a
                 LEFT JOIN MDM_DICT_TERMINOLOGY b ON a.TERM_ID= b.TERM_ID   and   b.DELETED_FLAG =0 and a.DELETED_FLAG =0
    </update>

    <update id="syncDictMappingsContentData">
        INSERT INTO DQM_DICT_MAPPINGS_CONTENT(TERM_ID,MAPPINGS_TYPE,SOURCE_FIELD,SOURCE_FIELD_CODE,TARGET_FIELD,TARGET_FIELD_CODE)
        SELECT TERM_ID,MAPPINGS_TYPE,SOURCE_FIELD,SOURCE_FIELD_CODE,TARGET_FIELD,TARGET_FIELD_CODE
        FROM MDM_DICT_MAPPINGS_CONTENT
    </update>

    <update id="syncDictData">
        INSERT INTO ${targetTableName}
        SELECT *
        FROM ${sourceTableName}
    </update>

    <select id="queryDictDataList" resultType="com.jykj.dqm.system.entity.DictMappingsContent"  databaseId="mysql">
        SELECT *
        FROM DQM_DICT_MAPPINGS_CONTENT
        <where>
            <if test="termId != null and termId != ''">
                AND TERM_ID = #{termId}
            </if>
            <if test='mappingsType != null and mappingsType != ""'>
                AND MAPPINGS_TYPE = #{mappingsType,jdbcType=VARCHAR}
            </if>
            <if test="targetField != null and targetField != ''">
                AND TARGET_FIELD like  CONCAT('%', #{targetField}, '%')
            </if>
            <if test="targetFieldCode != null and targetFieldCode != ''">
                AND TARGET_FIELD_CODE like CONCAT('%', #{targetFieldCode}, '%')
            </if>
            <if test="sourceField != null and sourceField != ''">
                AND SOURCE_FIELD like CONCAT('%', #{sourceField}, '%')
            </if>
            <if test="sourceFieldCode != null and sourceFieldCode != ''">
                AND SOURCE_FIELD_CODE like CONCAT('%', #{sourceFieldCode}, '%')
            </if>
            <if test='isMatch != null and isMatch != "" and isMatch == "N"'>
                AND TARGET_FIELD IS NULL AND TARGET_FIELD_CODE IS NULL
            </if>
            <if test='isMatch != null and isMatch != "" and isMatch == "Y"'>
                AND TARGET_FIELD IS NOT NULL AND TARGET_FIELD_CODE IS NOT NULL
            </if>
        </where>
        ORDER BY SOURCE_FIELD_CODE ASC
    </select>

    <select id="queryDictDataList" resultType="com.jykj.dqm.system.entity.DictMappingsContent" databaseId="oracle">
        SELECT *
        FROM DQM_DICT_MAPPINGS_CONTENT
        <where>
            <if test="termId != null and termId != ''">
                AND TERM_ID = #{termId}
            </if>
            <if test='mappingsType != null and mappingsType != ""'>
                AND MAPPINGS_TYPE = #{mappingsType,jdbcType=VARCHAR}
            </if>
            <if test="targetField != null and targetField != ''">
                AND TARGET_FIELD like '%'|| #{targetField} ||'%'
            </if>
            <if test="targetFieldCode != null and targetFieldCode != ''">
                AND TARGET_FIELD_CODE like '%'|| #{targetFieldCode} ||'%'
            </if>
            <if test="sourceField != null and sourceField != ''">
                AND SOURCE_FIELD like '%'|| #{sourceField} ||'%'
            </if>
            <if test="sourceFieldCode != null and sourceFieldCode != ''">
                AND SOURCE_FIELD_CODE like '%'|| #{sourceFieldCode} ||'%'
            </if>
            <if test='isMatch != null and isMatch != "" and isMatch == "N"'>
                AND TARGET_FIELD IS NULL AND TARGET_FIELD_CODE IS NULL
            </if>
            <if test='isMatch != null and isMatch != "" and isMatch == "Y"'>
                AND TARGET_FIELD IS NOT NULL AND TARGET_FIELD_CODE IS NOT NULL
            </if>
        </where>
        ORDER BY SOURCE_FIELD_CODE ASC
    </select>

    <select id="getSysInfoList" resultType="java.util.Map">
        SELECT DISTINCT SYS_CODE "sysCode", SYS_NAME "sysName"
        FROM DQM_DICT_MAPPINGS where SYS_CODE is not null
    </select>

    <select id="syncDictDataByProcedure" statementType="CALLABLE">
        {CALL SYNC_DICT_DATA()}
    </select>
</mapper>
