package com.jykj.dqm.system.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 映射类别表实体类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@ApiModel(value = "映射类别表")
@Data
public class DictMappingType implements Serializable {
    /**
     * 映射类别
     */
    @ApiModelProperty(value = "映射类别")
    private String mappingsType;

    /**
     * 映射类别名称
     */
    @ApiModelProperty(value = "映射类别名称")
    private String mappingsTypeName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crearedTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除标志
     */
    @ApiModelProperty(value = "删除标志")
    private String deletedFlag;

    private static final long serialVersionUID = 1L;
}