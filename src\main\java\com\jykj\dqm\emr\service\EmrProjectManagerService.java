package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.EmrProjectManager;
import com.jykj.dqm.emr.entity.EmrProjectManagerQuery;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_PROJECT_MANAGER(项目管理)】的数据库操作Service
 * @createDate 2024-04-08 14:19:41
 */
public interface EmrProjectManagerService extends IService<EmrProjectManager> {

    R addProject(EmrProjectManager projectManager);

    R updateProject(EmrProjectManager projectManager);

    R deleteProjectById(String projectId);

    R queryProject(EmrProjectManagerQuery emrProjectManagerQuery);

    R queryProjectAdmin(String projectType);
}
