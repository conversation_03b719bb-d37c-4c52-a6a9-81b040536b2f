<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.system.dao.DictMappingsMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.system.entity.DictMappings">
        <!--@mbg.generated-->
        <!--@Table DQM_DICT_MAPPINGS-->
        <id column="MAPPINGS_ID" jdbcType="INTEGER" property="mappingsId"/>
        <result column="TERM_ID" jdbcType="VARCHAR" property="termId"/>
        <result column="TERM_NAME" jdbcType="VARCHAR" property="termName"/>
        <result column="MAPPINGS_TABLE" jdbcType="VARCHAR" property="mappingsTable"/>
        <result column="MAPPINGS_TYPE" jdbcType="VARCHAR" property="mappingsType"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DELETED_FLAG" jdbcType="VARCHAR" property="deletedFlag"/>
        <result column="CODE_FIELD" jdbcType="VARCHAR" property="codeField"/>
        <result column="NAME_FIELD" jdbcType="VARCHAR" property="nameField"/>
        <result column="MATCH_TYPE" jdbcType="VARCHAR" property="matchType"/>
        <result column="BD_CODE" jdbcType="VARCHAR" property="bdCode"/>
        <result column="BD_NAME" jdbcType="VARCHAR" property="bdName"/>
        <result column="SYS_CODE" jdbcType="VARCHAR" property="sysCode"/>
        <result column="SYS_NAME" jdbcType="VARCHAR" property="sysName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        MAPPINGS_ID,
        TERM_ID,
        TERM_NAME,
        MAPPINGS_TABLE,
        MAPPINGS_TYPE,
        CREATE_BY,
        CREATE_TIME,
        UPDATE_BY,
        UPDATE_TIME,
        DELETED_FLAG,
        CODE_FIELD,
        NAME_FIELD,
        MATCH_TYPE,
        BD_CODE,
        BD_NAME,
        SYS_CODE,
        SYS_NAME
    </sql>
</mapper>