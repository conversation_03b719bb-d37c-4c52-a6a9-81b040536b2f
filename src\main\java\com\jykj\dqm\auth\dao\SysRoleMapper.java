package com.jykj.dqm.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.auth.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {
    /**
     * 添加角色
     *
     * @param sysRole SysRole
     */
    void addSysRole(@Param("sysRole") SysRole sysRole);

    /**
     * 更新角色
     *
     * @param sysRole SysRole
     */
    void updateByRoleId(@Param("sysRole") SysRole sysRole);

    /**
     * 查询角色
     *
     * @return List<SysRole>
     */
    List<SysRole> querylist();

    /**
     * 通过角色Id删除角色
     *
     * @param roleId 角色Id
     */
    void deleteByRoleId(@Param("roleId") Integer roleId);
}
