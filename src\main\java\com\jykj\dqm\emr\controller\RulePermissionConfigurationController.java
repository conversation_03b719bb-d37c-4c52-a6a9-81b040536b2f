package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.RulePermissionConfigurationSaveDTO;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 规则权限配置
 * <p>
 * //保存 用户-》规则权限（多）
 * //根据用户账号查询负责的规则
 * //规则配置处，根据用户过滤配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/7/4 15:29:18
 */
@Api(tags = {"规则权限配置"})
@RestController
@RequestMapping("/emr/rulePermissionConfiguration")
@Validated
public class RulePermissionConfigurationController {
    @Autowired
    RulePermissionConfigurationService rulePermissionConfigurationService;

    //保存 用户-》规则权限（多）
    @LogRemark(operate = "保存规则权限配置", module = "规则权限配置")
    @ApiOperation(value = "保存规则权限配置(勾选多少传多少，不传父目录，只传最后一级目录)", notes = "规则权限配置")
    @PostMapping("/saveRulePermission")
    public R saveRulePermission(@Validated @RequestBody RulePermissionConfigurationSaveDTO rulePermissionConfigurationSaveDTO) {
        return rulePermissionConfigurationService.saveRulePermission(rulePermissionConfigurationSaveDTO);
    }

    @ApiOperation(value = "根据用户账号查询负责的规则", notes = "规则权限配置")
    @GetMapping("/getRulePermission")
    public R getRulePermission(@NotBlank(message = "用户账号不能为空！") @RequestParam String userAccount, @NotBlank(message = "项目ID不能为空！") @RequestParam String projectId) {
        RulePermissionConfigurationSaveDTO permissionConfigurationSaveDTO = rulePermissionConfigurationService.getRulePermission(userAccount, projectId);
        return RUtil.success(permissionConfigurationSaveDTO);
    }

    @ApiOperation(value = "根据用户账号查询其负责的任务（反选使用）", notes = "规则权限配置")
    @GetMapping("/getTask")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "configType", value = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userAccount", value = "用户账号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "levelCode", value = "等级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "directoryCode", value = "目录编号", required = true, dataType = "String")
    })
    public R getTask(@NotBlank(message = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据！") @RequestParam String configType,
                     @NotBlank(message = "项目ID不能为空！") @RequestParam String projectId,
                     @NotBlank(message = "用户账号不能为空！") @RequestParam String userAccount,
                     @NotBlank(message = "等级不能为空！") @RequestParam String levelCode,
                     @RequestParam(required = false) String directoryCode) {
        RulePermissionConfigurationSaveDTO permissionConfigurationSaveDTO = rulePermissionConfigurationService.getTask(configType, userAccount, levelCode, directoryCode, projectId);
        return RUtil.success(permissionConfigurationSaveDTO);
    }

    @ApiOperation(value = "查询用户及任务情况", notes = "规则权限配置")
    @GetMapping("/getUserListAndTasks")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "configType", value = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "levelCode", value = "等级", required = false, dataType = "String"),
            @ApiImplicitParam(name = "userName", value = "用户名", required = false, dataType = "String")
    })
    public R getUserListAndTasks(@NotBlank(message = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据！") @RequestParam String configType,
                                 @NotBlank(message = "项目ID不能为空！") @RequestParam String projectId,
                                 @RequestParam(required = false) String levelCode,
                                 @RequestParam(required = false) String userName) {
        Map<String, Object> myTasksAndProgress = rulePermissionConfigurationService.getMyTasksAndProgress(levelCode, userName, null, configType, projectId);
        return RUtil.success(myTasksAndProgress);
    }

    @ApiOperation(value = "查询目录树形", notes = "规则权限配置")
    @GetMapping("/queryDirectoryTree")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "configType", value = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userAccount", value = "用户账号，没有传则查全部", required = false, dataType = "String"),
            @ApiImplicitParam(name = "levelCode", value = "等级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "needNotAllocationTask", value = "是否需要未分配的任务", required = false, dataType = "boolean")
    })
    public R queryDirectoryTree(@NotBlank(message = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据！") @RequestParam String configType,
                                @NotBlank(message = "项目ID不能为空！") @RequestParam String projectId,
                                @RequestParam(required = false) String userAccount,
                                @NotBlank(message = "等级不能为空！") @RequestParam String levelCode,
                                @RequestParam(defaultValue = "true") boolean needNotAllocationTask) {
        return rulePermissionConfigurationService.queryDirectoryTree(configType, userAccount, levelCode, needNotAllocationTask, projectId);
    }

}
