package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 文档导出记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:47:11
 */
@ApiModel(description = "文档导出记录")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentExportRecordQuery extends MyPageInfo {
    /**
     * 导出文档等级
     */
    @TableField(value = "EXPORT_DOCUMENT_LEVEL")
    @ApiModelProperty(value = "导出文档等级")
    private String exportDocumentLevel;

    @TableField(value = "PROJECT_ID")
    @NotNull(message = "项目ID不能为空")
    @ApiModelProperty(value = "项目ID,必传")
    private String projectId;

    /**
     * 创建人
     */
    @TableField(value = "CREATE_BY")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;
}