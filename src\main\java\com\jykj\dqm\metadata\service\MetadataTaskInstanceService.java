package com.jykj.dqm.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.MetadataTaskInstance;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceDTO;

/**
 * 采集任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 13:53:47
 */
public interface MetadataTaskInstanceService extends IService<MetadataTaskInstance> {
    R queryTaskAllocationHisInfo(MetadataTaskInstanceDTO metadataTaskInstanceDTO);
}
