package com.jykj.dqm.empiricalmaterial.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectory;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectoryQuery;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialDirectoryService;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfigurationQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 实证材料目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:58:25
 */
@Api(tags = {"实证材料目录配置"})
@RestController
@RequestMapping("/emm/empiricalmaterial")
public class EmpiricalMaterialDirectoryController {
    @Autowired
    private EmpiricalMaterialDirectoryService empiricalMaterialDirectoryService;

    @LogRemark(operate = "新增实证材料目录", module = "实证材料目录配置")
    @ApiOperation(value = "新增实证材料目录", notes = "实证材料目录配置")
    @PostMapping("/add")
    public R add(@Validated @RequestBody EmpiricalMaterialDirectory empiricalMaterialDirectory) {
        return empiricalMaterialDirectoryService.add(empiricalMaterialDirectory);
    }

    @LogRemark(operate = "更新实证材料目录", module = "实证材料目录配置")
    @ApiOperation(value = "更新实证材料目录", notes = "实证材料目录配置")
    @PostMapping("/update")
    public R update(@Validated @RequestBody EmpiricalMaterialDirectory empiricalMaterialDirectory) {
        return empiricalMaterialDirectoryService.update(empiricalMaterialDirectory);
    }

    @LogRemark(operate = "删除实证材料目录", module = "实证材料目录配置")
    @ApiOperation(value = "删除实证材料目录", notes = "实证材料目录配置")
    @DeleteMapping("/delete")
    public R delete(@RequestParam("ids") List<Long> ids) {
        return empiricalMaterialDirectoryService.delete(ids);
    }

    @ApiOperation(value = "查询实证材料目录", notes = "实证材料目录配置")
    @PostMapping("/query")
    public R query(@RequestBody EmpiricalMaterialDirectoryQuery empiricalMaterialDirectoryQuery) {
        return empiricalMaterialDirectoryService.query(empiricalMaterialDirectoryQuery);
    }

    @LogRemark(operate = "批量更新实证材料目录", module = "实证材料目录配置")
    @ApiOperation(value = "批量更新实证材料目录(根据ID只是更新，没有新增)", notes = "实证材料目录配置")
    @PostMapping("/batchUpdate")
    public R batchUpdate(@Validated @RequestBody List<EmpiricalMaterialDirectory> empiricalMaterialDirectories) {
        return empiricalMaterialDirectoryService.batchUpdateMy(empiricalMaterialDirectories);
    }
}
