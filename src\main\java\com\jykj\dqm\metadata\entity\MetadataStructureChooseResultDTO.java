package com.jykj.dqm.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 库表结构选择结果
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/14 13:43:20
 */
@ApiModel(value = "库表结构选择结果")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_METADATA_STRUCTURE_CHOOSE_RESULT")
public class MetadataStructureChooseResultDTO implements Serializable {
    /**
     * 数据源ID
     */
    @TableField(value = "DATA_SOURCE_ID")
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据库名称
     */
    @TableField(value = "DATABASE_NAME")
    @ApiModelProperty(value = "数据库名称")
    private String databaseName;

    /**
     * SCHEMA名称
     */
    @TableField(value = "SCHEMA_NAME", keepGlobalFormat = true)
    @ApiModelProperty(value = "SCHEMA名称")
    private String schemaName;

    /**
     * CATALOG名称
     */
    @TableField(value = "CATALOG_NAME", keepGlobalFormat = true)
    @ApiModelProperty(value = "CATALOG名称")
    private String catalogName;

    /**
     * 系统编码
     */
    @TableField(value = "SYS_CODE")
    @ApiModelProperty(value = "系统编码")
    private String sysCode;

    /**
     * 系统名称
     */
    @TableField(value = "SYS_NAME")
    @ApiModelProperty(value = "系统名称")
    private String sysName;

    /**
     * 表名
     */
    @TableField(value = "TABLE_NAME", keepGlobalFormat = true)
    @ApiModelProperty(value = "表名")
    private List<String> tableNames;

    /**
     * 0/1 0有且，1不包含
     */
    @TableField(value = "CHOOSE_RESULT")
    @ApiModelProperty(value = "0/1 0有且，1不包含")
    private String chooseResult;

    private static final long serialVersionUID = 1L;
}