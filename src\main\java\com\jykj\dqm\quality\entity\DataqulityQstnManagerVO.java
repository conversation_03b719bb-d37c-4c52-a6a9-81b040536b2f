package com.jykj.dqm.quality.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 质量问题管理VO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 10:33:34
 */
@ApiModel(value = "质量问题管理VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DATAQULITY_QSTN_MANAGER")
public class DataqulityQstnManagerVO extends DataqulityQstnManager implements Serializable {
    /**
     * 检核规则名称
     */
    @ApiModelProperty(value = "检核规则名称")
    private String checkRuleName;

    /**
     * 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
     */
    @ApiModelProperty(value = "检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性")
    private String checkRuleFatherType;

    /**
     * 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）
     */
    @ApiModelProperty(value = "检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）")
    private String checkRuleType;

    /**
     * 检查表或视图
     */
    @ApiModelProperty(value = "检查表或视图")
    private String checkRuleTableOrView;

    /**
     * 检核字段
     */
    @ApiModelProperty(value = "检核字段")
    private String checkRuleColumn;

    /**
     * 问题数SQL
     */
    @ApiModelProperty(value = "问题数SQL")
    private String questionNmSql;

    /**
     * 总数据记录数SQL
     */
    @ApiModelProperty(value = "总数据记录数SQL")
    private String totalNmSql;

    /**
     * 问题明细SQL
     */
    @ApiModelProperty(value = "问题明细SQL")
    private String pbSubsidiarySql;

    /**
     * 问题分类编码：提示，预警，严重
     */
    @ApiModelProperty(value = "问题分类编码：提示，预警，严重")
    private String qstType;

    @ApiModelProperty(value = "操作记录")
    List<DataqulityQstnOperationRecord> dataqulityQstnOperationRecords;

    private static final long serialVersionUID = 1L;
}