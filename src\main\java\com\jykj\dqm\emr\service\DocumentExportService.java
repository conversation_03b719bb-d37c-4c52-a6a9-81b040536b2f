package com.jykj.dqm.emr.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.DocumentExport;
import com.jykj.dqm.emr.entity.DocumentExportEachDoc;
import com.jykj.dqm.emr.entity.DocumentExportRecord;
import com.jykj.dqm.emr.entity.DocumentExportRecordRuleDetail;
import lombok.SneakyThrows;

/**
 * 文档导出
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:47:11
 */
public interface DocumentExportService {
    /**
     * 导出文档（选择时间段+导出文档等级），注意向下包括
     *
     * @param documentExport DocumentExport
     * @return 响应结果
     * <AUTHOR>
     */
    R previewExport(DocumentExport documentExport);

    //处理关联的数量
    @SneakyThrows
    void dealConditional(DocumentExportRecord documentExportRecord);

    /**
     * 定时任务调用
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果
     * <AUTHOR>
     */
    R previewExport(String exportRecordId);

    /**
     * 问题数据备注
     *
     * @param documentExportRecordRuleDetail DocumentExportRecordRuleDetail
     * @return 响应结果
     * <AUTHOR>
     */
    R problemDataRemarks(DocumentExportRecordRuleDetail documentExportRecordRuleDetail);

    /**
     * 导出文档,将预览的文档完全导出
     *
     * @param exportRecordId 文档导出记录ID
     * @param username       用户名
     * @return 响应结果
     * <AUTHOR>
     */
    void exportAsync(String exportRecordId, String username, String token);

    /**
     * 根据导出ID，获取当前进度,第二次传值的时候带上第一次结果中的allDocxNum，减少数据库查询次数
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果
     * <AUTHOR>
     */
    R getCurrentProgress(String exportRecordId);

    R regeneratePreviewDocument(DocumentExportEachDoc documentExport);

    void downloadDoc(String exportRecordId);

    R getBase(String exportRecordId, String userAccount);

    R getMedicaRecordsAndQuality(String exportRecordId, Integer directoryType, String directoryCode,String userAccount);

    R getMedicaRecordsOrQualityStatus(String exportRecordId, String directoryType);
}
