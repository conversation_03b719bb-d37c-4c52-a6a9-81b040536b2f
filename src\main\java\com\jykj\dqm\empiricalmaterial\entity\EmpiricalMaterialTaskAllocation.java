package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.emr.entity.UserAndTimeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 实证材料任务分配
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_TASK_ALLOCATION
 */
@TableName(value = "DQM_EMR_EMPIRICAL_MATERIAL_TASK_ALLOCATION")
@Data
public class EmpiricalMaterialTaskAllocation extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 等级关联
     */
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态:0-未分配，1-已分配，2-已完成")
    private String taskStatus;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String personInCharge;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarksDesc;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}