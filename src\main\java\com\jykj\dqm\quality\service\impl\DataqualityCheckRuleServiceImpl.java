package com.jykj.dqm.quality.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.quality.dao.DataqualityCheckRuleMapper;
import com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleQueryDTO;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;
import com.jykj.dqm.quality.entity.PrimaryForeignFiled;
import com.jykj.dqm.quality.entity.RuleFirstPageQueryDTO;
import com.jykj.dqm.quality.manager.rulesql.CheckRuleTypeEnum;
import com.jykj.dqm.quality.manager.rulesql.CheckSqlFactory;
import com.jykj.dqm.quality.service.DataqualityCheckRuleService;
import com.jykj.dqm.quartz.dao.ScheduleJobTaskDao;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.scheduler.QuartzManager;
import com.jykj.dqm.quartz.service.impl.ScheduleJobTaskService;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检核规则
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@Service
public class DataqualityCheckRuleServiceImpl extends ServiceImpl<DataqualityCheckRuleMapper, DataqualityCheckRule> implements DataqualityCheckRuleService {
    @Autowired
    MetadataDatasourceMapper dqmMetadataDatasourceMapper;

    @Autowired
    DataqualityCheckRuleMapper dataqualityCheckRuleMapper;

    @Autowired
    private DataqualityTaskInstanceMapper dataqualityTaskInstanceMapper;

    @Autowired
    private QuartzManager quartzManager;

    @Autowired
    private ScheduleJobTaskService scheduleJobTaskService;

    @Autowired
    private ScheduleJobTaskDao scheduleJobTaskDao;

    @Override
    public R firstPageList(RuleFirstPageQueryDTO ruleFirstPageQueryDTO) {
        if (ruleFirstPageQueryDTO.getEndDt() != null) {
            ruleFirstPageQueryDTO.setEndDt(DateUtil.offsetDay(ruleFirstPageQueryDTO.getEndDt(), 1));
        }
        PageHelper.startPage(ruleFirstPageQueryDTO.getPageNum(), ruleFirstPageQueryDTO.getPageSize());
        List<Map<String, Object>> list = dqmMetadataDatasourceMapper.firstPageList(ruleFirstPageQueryDTO);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }

    @Override
    public R ruleList(DataqualityCheckRuleQueryDTO dataqualityCheckRuleDTO) {
        PageHelper.startPage(dataqualityCheckRuleDTO.getPageNum(), dataqualityCheckRuleDTO.getPageSize());
        List<DataqualityCheckRule> list = dataqualityCheckRuleMapper.ruleList(dataqualityCheckRuleDTO);
        PageInfo<DataqualityCheckRule> pageInfo = new PageInfo<>(list);
        List<DataqualityCheckRule> dataqualityCheckRules = pageInfo.getList();
        List<PrimaryForeignFiled> primaryForeignFileds;
        for (DataqualityCheckRule dataqualityCheckRule : dataqualityCheckRules) {
            if (CheckRuleTypeEnum.YZX.getType().equalsIgnoreCase(dataqualityCheckRule.getCheckRuleType())) {
                primaryForeignFileds = new ArrayList<>();
                String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
                String stCheckRuleColumn = dataqualityCheckRule.getStCheckRuleColumn();
                String[] arrayC = checkRuleColumn.split(","); //t1主
                String[] arraySTC = stCheckRuleColumn.split(","); //t2副
                for (int i = 0; i < arrayC.length; i++) {
                    primaryForeignFileds.add(PrimaryForeignFiled.builder().primaryKeyTableField(arrayC[i]).secondaryKeyTableField(arraySTC[i]).build());
                }
                dataqualityCheckRule.setPrimaryForeignFileds(primaryForeignFileds);
            }
        }
        pageInfo.setList(dataqualityCheckRules);
        return RUtil.success(pageInfo);
    }

    @Override
    public R addRule(DataqualityCheckRule dataqualityCheckRule) {
        addSql(dataqualityCheckRule);
        dataqualityCheckRuleMapper.insert(dataqualityCheckRule);
        return RUtil.success();
    }

    /**
     * 根据规则配置，生成SQL添加到DataqualityCheckRule对象中
     *
     * @param dataqualityCheckRule DataqualityCheckRule
     * <AUTHOR>
     */
    private void addSql(DataqualityCheckRule dataqualityCheckRule) {
        MetadataDatasource dqmMetadataDatasource = dqmMetadataDatasourceMapper.selectOne(
                new QueryWrapper<MetadataDatasource>()
                        .eq("SYS_CODE", dataqualityCheckRule.getSysCode())
                        .eq("DATABASE_NAME", dataqualityCheckRule.getDbNm())
                        .eq("DATABASE_SCHEMA", dataqualityCheckRule.getCheckSchema())
        );
        DataqualityCheckRuleDTO dataqualityCheckRuleDTO = MapperUtils.INSTANCE.map(DataqualityCheckRuleDTO.class, dataqualityCheckRule);
        dataqualityCheckRuleDTO.setDbType(dqmMetadataDatasource.getDatabaseType());
        CheckSqlDTO checkSql = CheckSqlFactory.getCheckSql(dataqualityCheckRuleDTO);
        dataqualityCheckRule.setTotalNmSql(checkSql.getTotalNmSql());
        dataqualityCheckRule.setQuestionNmSql(checkSql.getQuestionNmSql());
        dataqualityCheckRule.setPbSubsidiarySql(checkSql.getQstDetailFieldSql());
        dataqualityCheckRule.setCheckSqlDTO(checkSql);
        //处理前端异常传值 checkRuleId==0 导致oracle自增主键一直是0
        if (dataqualityCheckRule.getCheckRuleId() != null && 0 == dataqualityCheckRule.getCheckRuleId()) {
            dataqualityCheckRule.setCheckRuleId(null);
        }
    }

    @Override
    public R updateRule(DataqualityCheckRule dataqualityCheckRule) {
        //如果DQM_DATAQUALITY_TASK_INSTANCE已经存在执行记录，不能修改删除
        Long instanceNum = dataqualityTaskInstanceMapper.selectCount((new QueryWrapper<DataqualityTaskInstance>()).lambda().eq(DataqualityTaskInstance::getCheckRuleId, dataqualityCheckRule.getCheckRuleId()));
        if (instanceNum != null && instanceNum > 0) {
            return RUtil.error("已经存在执行记录，不能修改");
        }
        dataqualityCheckRule.setUpdateDate(new Date());
        //将规则转换成SQL
        addSql(dataqualityCheckRule);
        dataqualityCheckRuleMapper.update(dataqualityCheckRule,
                new QueryWrapper<DataqualityCheckRule>().eq("CHECK_RULE_ID", dataqualityCheckRule.getCheckRuleId()));
        return RUtil.success();
    }


    @Override
    public R delRule(String checkRuleId) {
        if (!StringUtil.isInteger(checkRuleId)) {
            return RUtil.error("checkRuleId参数错误！");
        }
        //如果DQM_DATAQUALITY_TASK_INSTANCE已经存在执行记录，不能删除
        Long instanceNum = dataqualityTaskInstanceMapper.selectCount((new QueryWrapper<DataqualityTaskInstance>()).lambda().eq(DataqualityTaskInstance::getCheckRuleId, checkRuleId));
        if (instanceNum != null && instanceNum > 0) {
            return RUtil.error("已经存在执行记录，不能删除");
        }
        dataqualityCheckRuleMapper.deleteById(checkRuleId);
        //获取已经设置的规则编号，通过BIZ_DATA_ID+TASK_TYPE
        Map<String, Object> build = MapUtil.builder(new HashMap<String, Object>())
                .put("bizDataId", checkRuleId)
                .put("taskType", "QC").build();
        List<ScheduleJobInfo> dbScheduleJobInfos = scheduleJobTaskDao.queryScheduleJobTaskByJobGroupTaskType(build);
        if (CollUtil.isEmpty(dbScheduleJobInfos)) {
            return RUtil.success();
        }
        dbScheduleJobInfos.forEach(jobInfo -> quartzManager.removeJob(jobInfo));
        return RUtil.success();
    }

    @Override
    public R updateRuleStatus(Map<String, Object> params) {
        if (ObjectUtil.isEmpty(params.get("checkRuleStatus")) || ObjectUtil.isEmpty(params.get("checkRuleId"))) {
            return RUtil.error("检核规则状态和规则ID不能为空！");
        }
        DataqualityCheckRule dataqualityCheckRuleDTO = new DataqualityCheckRule();
        dataqualityCheckRuleDTO.setCheckRuleStatus((String) params.get("checkRuleStatus"));
        dataqualityCheckRuleDTO.setCheckRuleId((Integer) params.get("checkRuleId"));
        dataqualityCheckRuleMapper.updateById(dataqualityCheckRuleDTO);

        //获取已经设置的规则编号，通过BIZ_DATA_ID+TASK_TYPE
        Map<String, Object> build = MapUtil.builder(new HashMap<String, Object>())
                .put("bizDataId", params.get("checkRuleId"))
                .put("taskType", "QC").build();
        List<ScheduleJobInfo> dbScheduleJobInfos = scheduleJobTaskDao.queryScheduleJobTaskByJobGroupTaskType(build);
        if (CollUtil.isEmpty(dbScheduleJobInfos)) {
            return RUtil.success();
        }
        dbScheduleJobInfos.forEach(jobInfo -> {
            //删除执行配置
            scheduleJobTaskService.deleteScheduleJobTask(jobInfo);
            //删除任务
            quartzManager.removeJob(jobInfo);
        });

        return RUtil.success();
    }

    @Override
    public R getCopiedSerialNumber(String checkRuleName) {
        List<DataqualityCheckRule> list = this.list(new LambdaQueryWrapper<DataqualityCheckRule>().likeRight(DataqualityCheckRule::getCheckRuleName, checkRuleName));
        Integer maxSerialNumber = list.stream().map(item -> item.getCheckRuleName())
                .filter(item -> item.contains("_"))
                .map(item -> {
                    String[] s = item.split("_");
                    if (StringUtil.isInteger(s[s.length - 1])) {
                        return Integer.parseInt(s[s.length - 1]) + 1;
                    }
                    return 1;
                }).max(Comparator.naturalOrder()).orElse(1);
        return RUtil.success(maxSerialNumber);
    }

    @Override
    public R getTable(String dataSourceId, String tableName) {
        if (StrUtil.isBlank(dataSourceId)) {
            return RUtil.error("dataSourceId不能为空");
        }
        //获取有且的表数量
        int containTableNum = dataqualityCheckRuleMapper.getContainTableNum(dataSourceId);
        List<String> tableList;
        if (containTableNum > 0) {
            //包含的表，从DQM_METADATA_STRUCTURE_CHOOSE_RESULT 中获取
            tableList = dataqualityCheckRuleMapper.getTableFromChooseResult(dataSourceId, tableName);
        } else {
            //排除DQM_METADATA_STRUCTURE_CHOOSE_RESULT中不包含的表，剩下的全部表
            tableList = dataqualityCheckRuleMapper.getTableFromAllExcludeNotContain(dataSourceId, tableName);
        }
        return RUtil.success(tableList);
    }

    @Override
    public R getTableField(String dataSourceId, String tableName, String fieldName, Integer pageSize, Integer pageNum) {
        if (StrUtil.isBlank(dataSourceId)) {
            return RUtil.error("dataSourceId不能为空");
        }
        if (StrUtil.isBlank(tableName)) {
            return RUtil.error("tableName不能为空");
        }
        PageHelper.startPage(pageNum, pageSize);
        List<Map<String, Object>> list = dataqualityCheckRuleMapper.getTableField(dataSourceId, tableName, fieldName);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }

    @Override
    public R getDictTable(String tableName, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<Map<String, Object>> mapList = dataqualityCheckRuleMapper.getDictTable(tableName);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(mapList);
        return RUtil.success(pageInfo);
    }

    @Override
    public R getDictTableFiled(String fieldName, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<Map<String, Object>> mapList = dataqualityCheckRuleMapper.getDictTableFiled(fieldName);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(mapList);
        return RUtil.success(pageInfo);
    }

    @Override
    public R getCheckSql(DataqualityCheckRule dataqualityCheckRule) {
        addSql(dataqualityCheckRule);
        return RUtil.success(dataqualityCheckRule.getCheckSqlDTO());
    }
}
