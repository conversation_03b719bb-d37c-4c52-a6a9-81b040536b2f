package com.jykj.dqm.emr.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import com.jykj.dqm.emr.entity.DocumentExport;
import com.jykj.dqm.emr.entity.DocumentExportEachDoc;
import com.jykj.dqm.emr.entity.DocumentExportRecordRuleDetail;
import com.jykj.dqm.emr.service.DocumentExportService;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.NumberMyUtil;
import com.jykj.dqm.utils.StpUtilMy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档导出
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:48:50
 */
@Api(tags = {"文档预览导出"})
@RestController
@RequestMapping("/emr/documentExport")
public class DocumentExportController {
    @Resource
    private DocumentExportService documentExportService;

    /**
     * 预览导出文档（选择时间段+导出文档等级），注意向下包括
     *
     * @param documentExport DocumentExport
     * @return 响应结果
     * <AUTHOR>
     */
    @LogRemark(operate = "预览导出文档", module = "文档预览导出")
    @ApiOperation(value = "预览导出文档", notes = "文档预览导出")
    @PostMapping("/previewExport")
    public R previewExport(@Validated @RequestBody DocumentExport documentExport) {
        return documentExportService.previewExport(documentExport);
    }

    /**
     * 重新生成某一个章节的预览文档
     *
     * @param documentExport DocumentExportEachDoc
     * @return 响应结果
     * <AUTHOR>
     */
    @LogRemark(operate = "重新生成预览文档", module = "文档预览导出")
    @ApiOperation(value = "重新生成预览文档", notes = "文档预览导出")
    @PostMapping("/regeneratePreviewDocument")
    public R regeneratePreviewDocument(@Validated @RequestBody DocumentExportEachDoc documentExport) {
        return documentExportService.regeneratePreviewDocument(documentExport);
    }


    /**
     * 根据导出ID，获取当前进度,第二次传值的时候带上第一次结果中的allDocxNum，减少数据库查询次数
     *
     * @param exportRecordId 文档导出记录ID
     * @param allDocxNum     文档总数量，第二次调用传值，减少数据库查询次数
     * @return 响应结果allDocxNum;finishDocxNum;failureResult
     * <AUTHOR>
     */
    @ApiOperation(value = "据导出ID，获取当前进度,第二次传值的时候带上第一次结果中的allDocxNum，减少数据库查询次数", notes = "文档预览导出")
    @GetMapping("/getCurrentProgress")
    public R getCurrentProgress(@RequestParam("exportRecordId") String exportRecordId, @RequestParam(value = "allDocxNum", required = false) Integer allDocxNum) {
        return documentExportService.getCurrentProgress(exportRecordId);
    }

    /**
     * 导出文档,将预览的文档完全导出（异步执行）
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果
     * <AUTHOR>
     */
    @LogRemark(operate = "导出文档（异步执行）", module = "文档预览导出")
    @ApiOperation(value = "导出文档（异步执行）", notes = "文档预览导出")
    @GetMapping("/exportAsync")
    public R exportAsync(@RequestParam("exportRecordId") String exportRecordId) {
        //TransmittableThreadLocalManager.set(StpUtilMy.getTokenValue());
        //校验当前是否有导出任务

        Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.processMap;
        if (processMap != null && processMap.get(StpUtilMy.getTokenValue()) != null) {
            throw new BusinessException("当前文档正在导出中，同一时间只能导出一份文档，请稍后重试！");
        }
        String username = StpUtilMy.getUserName();
        documentExportService.exportAsync(exportRecordId, username, StpUtilMy.getTokenValue());
        if (processMap != null) {
            processMap.put(StpUtilMy.getTokenValue(), MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, 0.0).build());
        }
        return RUtil.success();
    }

    /**
     * 异步导出文档进度
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果
     * <AUTHOR>
     */
    @ApiOperation(value = "异步导出文档进度（-1表示失败，1表示完成）,建议5秒调一次", notes = "文档预览导出")
    @GetMapping("/exportProcess")
    public R exportProcess(@RequestParam(value = "exportRecordId", required = false) String exportRecordId) {
        String tokenValue = StpUtilMy.getTokenValue();
        Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.processMap;
        Map<String, Object> result = new HashMap<>();
        if (processMap == null || CollUtil.isEmpty(processMap.get(tokenValue))) {
            result.put("process", 0);
            result.put("exportRecordId", exportRecordId);
            return RUtil.success(result);
        }
        Map<String, Object> entity = processMap.get(tokenValue);
        List<Object> values = new ArrayList<>(entity.values());
        List<Object> keys = new ArrayList<>(entity.keySet());
        Double process = (Double) values.get(0);
        Double one = new Double("1.0");
        Double minusOne = new Double("-1.0");
        result.put("process", NumberMyUtil.round(process, 4));
        result.put("exportRecordId", keys.get(0));

        if (process != null && (one.compareTo(process) == 0 || minusOne.compareTo(process) == 0)) {
            processMap.remove(tokenValue);
        }
        return RUtil.success(result);
    }

    /**
     * 下载异步导出的文档
     *
     * @param exportRecordId 文档导出记录ID
     * @return 响应结果
     * <AUTHOR>
     */
    @ApiOperation(value = "下载异步导出的文档", notes = "文档预览导出", produces = "application/octet-stream")
    @GetMapping("/downloadDoc")
    public void downloadDoc(@RequestParam("exportRecordId") String exportRecordId) {
        documentExportService.downloadDoc(exportRecordId);
    }

    /**
     * 问题数据备注
     *
     * @param documentExportRecordRuleDetail DocumentExportRecordRuleDetail
     * @return 响应结果
     * <AUTHOR>
     */
    @LogRemark(operate = "问题数据备注", module = "文档预览导出")
    @ApiOperation(value = "问题数据备注", notes = "文档预览导出")
    @PostMapping("/problemDataRemarks")
    public R problemDataRemarks(@Validated @RequestBody DocumentExportRecordRuleDetail documentExportRecordRuleDetail) {
        return documentExportService.problemDataRemarks(documentExportRecordRuleDetail);
    }

    @ApiOperation(value = "获取基础数据", notes = "文档预览导出")
    @GetMapping("/getBase")
    public R getBase(@RequestParam("exportRecordId") String exportRecordId, @RequestParam(required = false) String userAccount) {
        return documentExportService.getBase(exportRecordId, userAccount);
    }

    @ApiOperation(value = "获取病历|质量数据", notes = "文档预览导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "directoryCode", value = "目录编码，为空查左侧列表，不为空查子级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "directoryType", value = "目录类型：2、病历数据，3、质量数据", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userAccount", value = "用户账号，没有传则查全部", required = false, dataType = "String")
    })
    @GetMapping("/getMedicaRecordsOrQuality")
    public R getMedicaRecordsAndQuality(@RequestParam("exportRecordId") String exportRecordId,
                                        @NotBlank Integer directoryType,
                                        @NotBlank String directoryCode,
                                        @RequestParam(required = false) String userAccount) {
        return documentExportService.getMedicaRecordsAndQuality(exportRecordId, directoryType, directoryCode, userAccount);
    }

    @ApiOperation(value = "获取病历|质量数据的状态（是否通过）", notes = "文档预览导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "directoryType", value = "目录类型：2、病历数据，3、质量数据", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "directoryCode", value = "目录编码，为空查左侧列表，不为空查子级", required = false)
    })
    @GetMapping("/getMedicaRecordsOrQualityStatus")
    public R getMedicaRecordsOrQualityStatus(@RequestParam("exportRecordId") String exportRecordId, String directoryType) {
        return documentExportService.getMedicaRecordsOrQualityStatus(exportRecordId, directoryType);
    }
}
