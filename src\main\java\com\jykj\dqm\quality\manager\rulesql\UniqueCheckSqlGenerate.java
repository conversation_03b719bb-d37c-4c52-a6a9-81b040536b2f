package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.stereotype.Component;

/**
 * 唯一性检核SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class UniqueCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    /*
         select LOGIN_ID,PASSWORD from SYS_USER
         where CONCAT_WS(',',LOGIN_ID,PASSWORD)
         in
            (select CONCAT_WS(',',LOGIN_ID,PASSWORD) from SYS_USER where SYS_ID='DQM' group by LOGIN_ID,PASSWORD  HAVING COUNT(*) > 1
            )
         and SYS_ID='DQM'
     */
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        //问题明细字段
        StringBuilder qstDetailFieldSql = new StringBuilder();
        String ukFields = getConcatField(dataqualityCheckRule.getDbType(), checkRuleColumn);
        qstDetailFieldSql.append("select " + detailFiled + " from " + table + " where " + ukFields)
                .append(" in (select " + ukFields + " from " + table);
        //添加in里面的条件
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" where " + checkRuleWhere);
        }
        qstDetailFieldSql.append(" group by " + checkRuleColumn + "  HAVING COUNT(*) > 1)");
        //添加外面的条件
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" and " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.WYX;
    }
}
