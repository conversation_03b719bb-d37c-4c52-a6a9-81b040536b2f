package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.RequireProjectDictionary;
import com.jykj.dqm.emr.entity.RequireProjectDictionaryQuery;
import com.jykj.dqm.emr.service.RequireProjectDictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 要求项目字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/20 16:25:03
 */
@Api(tags = {"要求项目字典"})
@RestController
@RequestMapping("/emr/requireProjectDictionary")
public class RequireProjectDictionaryController {
    @Autowired
    private RequireProjectDictionaryService requireProjectDictionaryService;

    @LogRemark(operate = "新增要求项目字典", module = "要求项目名称字典")
    @ApiOperation(value = "新增要求项目字典", notes = "要求项目名称字典")
    @PostMapping("/add")
    public R add(@Validated @RequestBody RequireProjectDictionary requireProjectDictionary) {
        return requireProjectDictionaryService.add(requireProjectDictionary);
    }

    @LogRemark(operate = "更新要求项目字典", module = "要求项目名称字典")
    @ApiOperation(value = "更新要求项目字典", notes = "要求项目名称字典")
    @PostMapping("/update")
    public R update(@Validated @RequestBody RequireProjectDictionary requireProjectDictionary) {
        return requireProjectDictionaryService.update(requireProjectDictionary);
    }

    @LogRemark(operate = "删除要求项目字典", module = "要求项目名称字典")
    @ApiOperation(value = "删除要求项目字典", notes = "要求项目名称字典")
    @DeleteMapping("/delete")
    public R delete(@RequestParam("ids") List<Long> ids) {
        return requireProjectDictionaryService.delete(ids);
    }

    @ApiOperation(value = "查询要求项目字典", notes = "要求项目名称字典")
    @PostMapping("/query")
    public R query(@RequestBody RequireProjectDictionaryQuery requireProjectDictionaryQuery) {
        return requireProjectDictionaryService.query(requireProjectDictionaryQuery);
    }
}
