package com.jykj.dqm.homepage.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.homepage.service.DqmHomepageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 数据质量新首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2024-04-23 17:24
 */
@Api(tags = {"数据质量首页（新）"})
@RestController
@RequestMapping("/dqm/homepage")
@RequiredArgsConstructor
public class DqmHomepageController {

    private final DqmHomepageService dqmHomepageService;

    /**
     * qstnNum-问题数据量，checkRuleNum-质量规则总数，checkRuleGroupNum-质量规则分组统计，datasourceNum-数据源总量，dictNum-字典数，sysNum-系统数
     *
     * @param startDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @return
     */
    @ApiOperation(value = "数据分析+质量规则总数", notes = "qstnNum-问题数据量，checkRuleNum-质量规则总数，checkRuleGroupNum-质量规则分组统计，datasourceNum-数据源总量，dictNum-字典数，sysNum-系统数", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "startDate", value = "yyyy-MM-dd"),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "endDate", value = "yyyy-MM-dd"),
    })
    @GetMapping("/getDataAnalysisAndCheckRuleNum")
    public R<?> getDataAnalysisAndCheckRuleNum(@RequestParam(name = "startDate", required = false) String startDate, @RequestParam(name = "endDate", required = false) String endDate) {
        Map<String, Object> map = dqmHomepageService.getDataAnalysisAndCheckRuleNum(startDate, endDate);
        return RUtil.success(map);
    }

    @ApiOperation(value = "规则问题级别及占比情况", notes = "", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "startDate", value = "yyyy-MM-dd"),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "endDate", value = "yyyy-MM-dd"),
    })
    @GetMapping("/getRuleQstnLevel")
    public R<?> getRuleQstnLevel(@RequestParam(name = "startDate", required = false) String startDate, @RequestParam(name = "endDate", required = false) String endDate) {
        List<Map<String, Object>> map = dqmHomepageService.getRuleQstnLevel(startDate, endDate);
        return RUtil.success(map);
    }

    @ApiOperation(value = "各系统检测数与问题数情况分析", notes = "", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "startDate", value = "yyyy-MM-dd"),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "endDate", value = "yyyy-MM-dd"),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "sysCode", value = "yyyy-MM-dd"),
    })
    @GetMapping("/getSysQstnNumAnalysis")
    public R<?> getSysQstnNumAnalysis(@RequestParam(name = "startDate", required = false) String startDate,
                                      @RequestParam(name = "endDate", required = false) String endDate,
                                      @RequestParam(name = "sysCode", required = false) String sysCode) {
        List<Map<String, Object>> map = dqmHomepageService.getSysQstnNumAnalysis(startDate, endDate, sysCode);
        return RUtil.success(map);
    }
}
