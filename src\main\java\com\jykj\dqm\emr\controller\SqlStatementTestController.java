package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.EmrTestSqlExecResultRecord;
import com.jykj.dqm.emr.entity.SqlStatementQuery;
import com.jykj.dqm.emr.entity.SqlStatementQueryOneRule;
import com.jykj.dqm.emr.service.EmrTestSqlExecResultRecordService;
import com.jykj.dqm.emr.service.SqlStatementTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * sql语句测试
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:21:35
 */
@Api(tags = {"SQL语句测试"})
@RestController
@RequestMapping("/emr/sql")
public class SqlStatementTestController {
    @Autowired
    private SqlStatementTestService sqlStatementTestService;

    @Autowired
    private EmrTestSqlExecResultRecordService emrTestSqlExecResultRecordService;

    @LogRemark(operate = "执行SQL", module = "SQL语句测试")
    @ApiOperation(value = "执行SQL", notes = "SQL语句测试")
    @PostMapping("/execute")
    public R execute(@RequestBody @Validated SqlStatementQuery sqlStatementQuery) {
        return sqlStatementTestService.execute(sqlStatementQuery);
    }

    @LogRemark(operate = "执行一个规则的所有SQL", module = "SQL语句测试")
    @ApiOperation(value = "执行一个规则的所有SQL", notes = "SQL语句测试")
    @PostMapping("/executeOneRule")
    public R executeOneRule(@RequestBody @Validated SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        return sqlStatementTestService.executeOneRule(sqlStatementQueryOneRule);
    }

    @LogRemark(operate = "异步执行一个规则的所有SQL", module = "SQL语句测试")
    @ApiOperation(value = "异步执行一个规则的所有SQL", notes = "SQL语句测试")
    @PostMapping("/asyncExecuteOneRule")
    public R asyncExecuteOneRule(@RequestBody @Validated SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        Long num = emrTestSqlExecResultRecordService.lambdaQuery().eq(EmrTestSqlExecResultRecord::getDirectoryCode, sqlStatementQueryOneRule.getDirectoryCode())
                .eq(EmrTestSqlExecResultRecord::getDirectoryName, sqlStatementQueryOneRule.getDirectoryName())
                .eq(EmrTestSqlExecResultRecord::getEmrRuleType, sqlStatementQueryOneRule.getEmrRuleType())
                .eq(EmrTestSqlExecResultRecord::getExportRecordId, "1")
                .eq(EmrTestSqlExecResultRecord::getConfigType, "0")
                .eq(EmrTestSqlExecResultRecord::getExecStatus, "2").count();
        if (num > 0) {
            return RUtil.error("该规则正在执行，请勿重复执行！");
        }
        sqlStatementTestService.asyncExecuteOneRule(sqlStatementQueryOneRule);
        return RUtil.success("执行成功！");
    }

    @ApiOperation(value = "获取SQL执行状态", notes = "SQL语句测试")
    @PostMapping("/getSqlExecStatus")
    public R getSqlExecStatus(@RequestBody @Validated SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        return sqlStatementTestService.getSqlExecStatus(sqlStatementQueryOneRule);
    }

    @ApiOperation(value = "获取SQL执行结果", notes = "SQL语句测试")
    @PostMapping("/getSqlExecResult")
    public R getSqlExecResult(@RequestBody @Validated SqlStatementQueryOneRule sqlStatementQueryOneRule) {
        return sqlStatementTestService.getSqlExecResult(sqlStatementQueryOneRule);
    }
}
