package com.jykj.dqm.metadata.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.MetadataTaskInstanceMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataDatasourceSubTaskInfoVO;
import com.jykj.dqm.metadata.entity.MetadataTaskInstance;
import com.jykj.dqm.metadata.service.TaskGroupSubtasksService;
import com.jykj.dqm.utils.MapperUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采集数据
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 10:20:26
 */
@Slf4j
@Service
public class CollectionMetadataService {
    @Autowired
    TaskGroupSubtasksService dqmTaskGroupSubtasksService;

    @Autowired
    ExecCollectionTask execCollectionTask;

    @Autowired
    MetadataTaskInstanceMapper metadataTaskInstanceMapper;

    /**
     * 根据任务组ID执行任务
     *
     * @param taskGroupId 任务组ID
     * @return Result
     * <AUTHOR>
     */
    public R exeCollectionTask(String taskGroupId) {
        Map<String, Object> params = new HashMap<>();
        params.put("TASK_GROUP_ID", taskGroupId);
        params.put("TASK_STATE", "00");
        try {
            List<MetadataTaskInstance> metadataTaskInstances = metadataTaskInstanceMapper.selectByMap(params);
            if (CollectionUtil.isNotEmpty(metadataTaskInstances)) {
                return RUtil.error(1, "任务正在执行中...");
            }
            //根据任务组ID获取下面对应的任务采集任务
            R result = dqmTaskGroupSubtasksService.querySubtask(taskGroupId);
            List<MetadataDatasourceSubTaskInfoVO> dqmMetadataDatasources = (List<MetadataDatasourceSubTaskInfoVO>) result.getData();
            List<MetadataDatasource> metadataDatasources = MapperUtils.INSTANCE.mapAsList(MetadataDatasource.class, dqmMetadataDatasources);
            //启动多线程异步执行
            metadataDatasources.forEach(dqmMetadataDatasource -> execCollectionTask.execCollectionTask(dqmMetadataDatasource, taskGroupId));
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            return RUtil.error(2, "执行失败！");
        }
        return RUtil.success();
    }
}
