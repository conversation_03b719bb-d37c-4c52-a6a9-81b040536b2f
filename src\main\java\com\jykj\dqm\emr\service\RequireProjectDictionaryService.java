package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.RequireProjectDictionary;
import com.jykj.dqm.emr.entity.RequireProjectDictionaryQuery;

import java.util.List;

/**
 * 要求项目字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/20 16:25:32
 */
public interface RequireProjectDictionaryService extends IService<RequireProjectDictionary> {
    R add(RequireProjectDictionary requireProjectDictionary);

    R update(RequireProjectDictionary requireProjectDictionary);

    R delete(List<Long> ids);

    R query(RequireProjectDictionaryQuery requireProjectDictionaryQuery);
}
