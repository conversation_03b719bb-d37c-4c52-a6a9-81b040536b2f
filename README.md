# EMRrate-Management-System

电子病历评级文档管理系统

## 功能说明

### 文档转换功能
系统支持多种文档格式转换，便于文档的处理和展示：

1. **Word转HTML**: 将Word文档转换为HTML格式，支持在线预览
2. **Word转PDF**: 将Word文档转换为PDF格式
3. **Word转OFD**: 将Word文档转换为OFD(开放版式文档)格式

### 加密工具类
系统提供统一的加密解密工具类，便于数据安全管理：

1. **HashCryptoFactory**: 提供哈希加密工厂，支持多种哈希算法（SM3和MD5），用于密码等敏感信息的单向加密
2. **SM3Util**: 提供国密SM3哈希算法实现，用于密码等敏感信息的单向加密
3. **MD5Util**: 提供MD5哈希算法实现，向下兼容旧系统
4. **SymmetricCryptoFactory**: 提供对称加密工厂，支持多种对称加密算法（SM4和AES），用于数据的可逆加密和解密
5. **加密用途**: 主要用于数据库密码等敏感信息的加密存储和解密使用

### 数据源跨库关联查询功能
系统支持配置数据源是否需要跨库关联查询功能：

1. **功能说明**: 当数据源配置为需要跨库关联查询时，系统会自动配置Presto相关环境
2. **配置流程**: 
   - 在数据源中设置"是否需要跨库关联查询"为"是"
   - 系统自动在/home/<USER>/aes/pw.env文件中添加或修改数据源密码配置
   - 系统自动在/home/<USER>/catalog/目录下创建或修改对应的properties文件
   - 系统自动执行Docker相关命令重新构建和启动Presto容器
3. **支持的数据库类型**: MySQL、Oracle、SQL Server、PostgreSQL等
4. **全局配置项**: 
   - 在application.yml中可以配置是否开启自动配置跨库catalog功能
   - 默认关闭，需要手动开启才会执行自动配置
   ```yaml
   # 跨库查询配置
   cross-query:
     auto-catalog-config: false  # 是否开启自动配置跨库catalog，默认关闭
     presto-docker-path: /home/<USER>
     presto-env-password: Jykj1994@  # Presto环境密码，用于加密env.encrypted文件
   ```

### 配置加密算法
系统支持通过配置文件指定默认加密算法，可在application.yml或各环境配置文件中设置：

```yaml
# 加密算法配置
crypto:
  hash-algorithm: SM3  # 可选值: SM3, MD5 - 用于哈希加密（如密码哈希）
  symmetric-algorithm: SM4  # 可选值: SM4, AES - 用于对称加密（如数据加密）
```

### Word表格单元格合并功能
系统支持在生成Word文档时对表格进行单元格合并，特别是针对一致性报告中的类别列合并：

1. **DetailTableCategoryMergePolicy**: 专门用于table1的类别列合并
   - 自动识别相同系统（医院项目中"-"前的内容）
   - 将相同系统的行在"类别"列进行单元格合并
   - 前3列（类别、要求项目、医院项目）上下左右完全居中对齐，其他列左对齐
   - 保持表格格式和样式的一致性
   - **智能文字换行**: 直接从表格单元格的CTTblWidth获取实际列宽，自动计算字符长度限制，文字超出时自动换行而不撑开单元格
     - 动态宽度获取：直接读取已设置的单元格宽度，无需重复计算
     - 自动换行计算：DXA单位转厘米，再根据宋体11号字体特性计算字符数（每厘米约2.5个中文字符）
     - 智能边界控制：最多41个字符，确保显示效果
     - 容错处理：如果无法获取宽度，使用合理的默认值（3cm）
     - **重构优化**: 将calculateCharLimitByWidth、splitStringByLengthAndSubEnd和splitStringByLength三个方法整合为splitContentByTableWidth方法，逐字符精确计算宽度并判断是否达到表格宽度，支持中英文混合、一行全是英文、一行全是中文等复杂场景，提高准确性和代码可维护性

3. **使用场景**:
   - 报告生成
   - table1：当医院项目包含多个相同系统的项目时，自动合并类别列，前3列居中显示
   - table2：统计数据表格，所有列居中显示，提高数据展示的专业性
   - 提高报告的可读性和专业性

4. **实现原理**:
   - 继承poi-tl的DynamicTableRenderPolicy
   - 在渲染表格时统计相同类别的行数
   - 使用Apache POI的VMerge功能实现垂直单元格合并
   - 使用ParagraphAlignment.CENTER设置文字水平居中对齐
   - 使用STVerticalJc.CENTER设置单元格垂直居中对齐
   - 使用ParagraphAlignment.LEFT设置文字左对齐

### 数据分组合并功能
系统支持对统计数据按系统进行分组合并，特别是针对一致性报告中的table2数据处理：

1. **mergeTable2DataBySystem**: 专门用于报告中table2的系统分组合并
   - 自动识别相同系统（医院项目中"-"前的内容）
   - 将相同系统的多个项目数据进行累加合并
   - 重新计算一致性比例，保持数据准确性

2. **使用场景**:
   - YZX一致性报告生成
   - 当同一系统包含多个检查项目时，自动合并统计数据
   - 简化报告结构，提高数据可读性

3. **合并规则**:
   - 按项目类型分组：将相同项目类型的不同系统数据合并到一行
   - 医院项目列：只显示项目类型名称，如"项目代码"、"检查项目"
   - 记录总数T列：显示该项目类型下所有系统的数据，格式为"系统名称 数量"，最后一行显示"合计 累加总数"
   - 有对照记录数C列：显示该项目类型下所有系统的数据，格式为"系统名称 数量"，最后一行显示"合计 累加匹配数"
   - 一致性比例：使用该项目类型的合计数据计算 C/T 的比例
   - 序号：按项目类型重新编号

### 批注信息自动关联功能
系统支持在导出Word文档时自动关联历史批注信息，提高文档维护效率：

1. **功能说明**: 在每个章节生成时，系统会自动查找历史批注信息并关联到当前导出记录
2. **关联规则**:
   - 通过DIRECTORY_CODE（目录编码）、DIRECTORY_NAME（目录名称）、EMR_RULE_TYPE（规则类型）三个字段进行关联
   - 自动获取项目下最新的导出记录对应的批注信息进行关联
   - 包含四种批注信息：PROBLEM_DATA_REMARKS1（文中说明）、PROBLEM_DATA_REMARKS2（SQL注解）、PROBLEM_DATA_REMARKS3（原因说明）、PROBLEM_DATA_REMARKS4（整改措施）
3. **数据来源**:
   - 从DQM_EMR_DOCUMENT_RULE_SQL_EXEC_RECORD表获取当前章节的执行记录
   - 从DQM_EMR_DOCUMENT_EXPORT_RECORD表获取项目下最新的导出记录
   - 从DQM_EMR_DOCUMENT_EXPORT_RECORD_RULE_DETAIL表获取最新导出记录的批注信息
4. **处理时机**: 在每个章节的dealEachDoc方法执行完成后自动处理，确保及时关联
5. **性能优化**: 使用基于导出记录ID的ConcurrentHashMap缓存，避免重复查询和并发问题
6. **并发安全**: 解决了多个导出任务同时执行时的数据混乱问题
7. **内存管理**: 通过定时任务每天凌晨2点清理1天前的缓存数据，避免内存泄漏
8. **监控统计**: 提供缓存统计信息和手动管理接口，便于监控和维护
9. **异常处理**: 包含完整的异常处理机制，确保批注关联失败不影响文档导出

## 项目结构

### 核心文件
- `DocumentExportServiceImpl.java`: 文档导出服务实现类，负责整个导出流程的协调
- `GenerateChapterWord.java`: 章节Word生成基类，包含批注信息自动关联功能
- `ZHXGenerateChapterWord.java`: 整合性章节Word生成实现类
- `WZXGenerateChapterWord.java`: 完整性章节Word生成实现类
- `YZXGenerateChapterWord.java`: 一致性章节Word生成实现类
- `AnnotationCacheCleanupTask.java`: 批注信息缓存清理定时任务
- `AnnotationCacheController.java`: 批注信息缓存管理接口

### 定时任务
- **缓存清理任务**: 每天凌晨2点自动清理2天前的批注信息缓存
- **缓存统计任务**: 每小时记录缓存使用情况，便于监控

### 管理接口
- `GET /emr/annotation-cache/statistics`: 获取缓存统计信息
- `POST /emr/annotation-cache/cleanup?daysAgo=2`: 手动清理指定天数前的缓存
- `DELETE /emr/annotation-cache/record/{exportRecordId}`: 清理指定导出记录的缓存
- `POST /emr/annotation-cache/clear-all`: 清理所有缓存（慎用）
- `POST /emr/annotation-cache/run-scheduled-cleanup`: 立即执行定时清理任务

### 使用方法

#### Word转OFD
```java
// 示例：将Word文档转换为OFD格式
try {
    String wordFilePath = "文件路径/文档.docx";
    String ofdOutputPath = "文件路径/文档.ofd";
    WordUtil.docxToOFD(wordFilePath, ofdOutputPath);
} catch (Exception e) {
    e.printStackTrace();
}
```

#### 哈希加密（单向加密）
```java
// 示例：使用HashCryptoFactory进行哈希加密
String content = "需要加密的内容";
// 使用默认哈希加密算法(配置文件中指定)加密
String encrypted = HashCryptoFactory.encrypt(content);

// 使用指定算法加密
CryptoUtil md5Crypto = HashCryptoFactory.getCrypto("MD5");
String md5Encrypted = md5Crypto.encrypt(content);

CryptoUtil sm3Crypto = HashCryptoFactory.getCrypto("SM3");
String sm3Encrypted = sm3Crypto.encrypt(content);
```

#### 对称加密和解密（可逆加密）
```java
// 示例：使用SymmetricCryptoFactory进行对称加密和解密
String content = "需要加密的内容";

// 使用默认对称加密算法(配置文件中指定)加密
String encrypted = SymmetricCryptoFactory.encrypt(content);
// 解密
String decrypted = SymmetricCryptoFactory.decrypt(encrypted);

// 使用指定算法加密解密
SymmetricCryptoUtil aesCrypto = SymmetricCryptoFactory.getCrypto("AES");
String aesEncrypted = aesCrypto.encrypt(content);
String aesDecrypted = aesCrypto.decrypt(aesEncrypted);

SymmetricCryptoUtil sm4Crypto = SymmetricCryptoFactory.getCrypto("SM4");
String sm4Encrypted = sm4Crypto.encrypt(content);
String sm4Decrypted = sm4Crypto.decrypt(sm4Encrypted);

// 判断是否是加密内容
boolean isEncrypted = SymmetricCryptoFactory.isEncrypted(someString);
```

#### Word表格类别列合并
```java
// 示例：生成支持类别列合并和智能文字换行的表格
WordUtils wordUtils = new WordUtils();

// 准备表格数据，包含类别列
List<Map<String, Object>> table1Data = new ArrayList<>();
// 标题行
Map<String, Object> headerMap = new LinkedHashMap<>();
headerMap.put("zero", "类别");
headerMap.put("one", "要求项目");
headerMap.put("two", "医院项目");
headerMap.put("three", "数据库表与字段名");
headerMap.put("four", "数据字典表与字段名");
table1Data.add(headerMap);

// 数据行 - 相同系统的项目会在类别列自动合并，长文字自动换行
Map<String, Object> dataMap1 = new LinkedHashMap<>();
dataMap1.put("zero", "心电检查项目");
dataMap1.put("one", "心电-检查项目名称");
dataMap1.put("two", "心电-检查项目名称");
dataMap1.put("three", "dqm_dataquality_check_rule.check_item_name"); // 长字段名会自动换行
dataMap1.put("four", "dqm_dataquality_qstn_detail.question_detail_info");
table1Data.add(dataMap1);

Map<String, Object> dataMap2 = new LinkedHashMap<>();
dataMap2.put("zero", "心电检查项目");  // 相同系统，会与上一行合并
dataMap2.put("one", "心电-检查项目编码");
dataMap2.put("two", "心电-检查项目编码");
dataMap2.put("three", "CHECK_ITEM_NAME"); // 根据列宽自动计算字符限制
dataMap2.put("four", "BEST_MERGED_RECORD_INDEX_S");
table1Data.add(dataMap2);

// 生成支持类别列合并和智能换行的表格（前3列居中对齐）
wordUtils.generateTableWithCategoryMerge(table1Data, "输出文件路径/table1");

// 生成所有列居中对齐的表格
wordUtils.generateTableWithCenterAlign(table2Data, "输出文件路径/table2");
```

#### 智能文字换行功能说明
```java
// 系统会根据列的实际宽度逐字符精确计算，支持复杂的中英文混合场景：

// 1. 直接从表格单元格的CTTblWidth获取实际列宽
BigInteger cellWidth = getCellWidthFromTable(cell);

// 2. 使用重构后的splitContentByTableWidth方法，逐字符精确计算宽度
// - 结合了calculateCharLimitByWidth、splitStringByLengthAndSubEnd和splitStringByLength的功能
// - 逐字符计算宽度，判断是否达到表格宽度限制，支持复杂混合场景
String[] split = splitContentByTableWidth(cellWidth, cellContent, needSubEnd);

// 3. 逐字符宽度计算（精确处理中英文混合）：
// - DXA转厘米：宽度(DXA) / 567
// - 中文字符宽度：0.38厘米，英文字符宽度：0.2厘米
// - 逐个字符累加宽度，直到达到最大宽度限制
// - 安全边距：预留0.1厘米边距，避免撑开单元格

// 4. 复杂场景支持：
// - 一行全是中文：按中文字符宽度逐个累加
// - 一行全是英文：按英文字符宽度逐个累加
// - 中英文混合：每个字符按实际类型计算宽度
// - 例如："数据库table_name字段"会精确计算每个字符宽度并在合适位置换行

// 5. 重构优势：
// - 逐字符精确计算，处理复杂混合场景
// - 代码更简洁，逻辑更清晰
// - 一次性处理所有分割逻辑，避免重复计算
// - 更准确的宽度计算和分割效果，避免撑开单元格

// 6. 实际应用示例：
// 输入："数据库table_name字段信息description"
// 假设列宽为3厘米（约1701 DXA）
// 逐字符计算：
// - "数据库" = 0.38*3 = 1.14厘米
// - "table_name" = 0.2*10 = 2.0厘米
// - 累计3.14厘米 > 3厘米，在此处换行
// 结果：第一行"数据库table_name"，第二行"字段信息description"
```

#### 数据分组合并处理
```java
// 示例：对table2数据按系统进行分组合并
// 原始数据（4行）
List<Map<String, Object>> originalTable2Data = new ArrayList<>();
// 标题行
Map<String, Object> headerMap = new LinkedHashMap<>();
headerMap.put("serialNum", "序号");
headerMap.put("one", "医院项目");
headerMap.put("two", "记录总数T");
headerMap.put("three", "有对照记录数C");
headerMap.put("four", "一致性比例");
originalTable2Data.add(headerMap);

// 数据行
Map<String, Object> row1 = new LinkedHashMap<>();
row1.put("serialNum", 1);
row1.put("one", "心电-检查项目名称");
row1.put("two", 15349L);
row1.put("three", 6910L);
row1.put("four", "0.450000");
originalTable2Data.add(row1);

Map<String, Object> row2 = new LinkedHashMap<>();
row2.put("serialNum", 2);
row2.put("one", "心电-检查项目编码");
row2.put("two", 22259L);
row2.put("three", 6910L);
row2.put("four", "0.310000");
originalTable2Data.add(row2);

Map<String, Object> row3 = new LinkedHashMap<>();
row3.put("serialNum", 3);
row3.put("one", "检查-检查项目名称");
row3.put("two", 15349L);
row3.put("three", 6910L);
row3.put("four", "0.450000");
originalTable2Data.add(row3);

Map<String, Object> row4 = new LinkedHashMap<>();
row4.put("serialNum", 4);
row4.put("one", "检查-检查项目编码");
row4.put("two", 22259L);
row4.put("three", 6910L);
row4.put("four", "0.310000");
originalTable2Data.add(row4);

// 调用合并方法（在YZXGenerateChapterWord中）
List<Map<String, Object>> mergedTable2Data = mergeTable2DataBySystem(originalTable2Data);

// 合并后的数据（2行）：
// 序号1:
//   医院项目列: "项目代码"
//   记录总数T列: "检查 15349\n心电 22259\n合计 37608"
//   有对照记录数C列: "检查 6910\n心电 6910\n合计 19820"
//   一致性比例: "0.527647"
// 序号2:
//   医院项目列: "检查项目"
//   记录总数T列: "检查 15349\n心电 22259\n合计 37608"
//   有对照记录数C列: "检查 6910\n心电 6910\n合计 19820"
//   一致性比例: "0.527647"
```

#### 批注信息自动关联
```java
// 示例：批注信息自动关联功能在每个章节生成时自动执行
// 在每个GenerateChapterWord实现类的dealEachDoc方法最后会调用：
processChapterAnnotationInfo(recordId, documentDirectoryConfiguration);

// 该方法的核心逻辑：

// 1. 获取当前导出记录的项目ID
DocumentExportRecord currentExportRecord = documentExportRecordService.getById(exportRecordId);
String projectId = currentExportRecord.getProjectId();

// 2. 获取或初始化当前导出记录的批注信息映射（基于导出记录ID缓存，避免重复查询和并发问题）
Map<String, DocumentExportRecordRuleDetail> latestAnnotationMap = exportRecordAnnotationCache.get(exportRecordId);
if (latestAnnotationMap == null) {
    synchronized (exportRecordAnnotationCache) {
        latestAnnotationMap = exportRecordAnnotationCache.get(exportRecordId);
        if (latestAnnotationMap == null) {
            latestAnnotationMap = initLatestAnnotationMap(exportRecordId, projectId);
            exportRecordAnnotationCache.put(exportRecordId, latestAnnotationMap);
        }
    }
}

// 3. 直接使用传入的目录配置信息创建批注关联（无需查询SQL执行记录）
String annotationKey = directoryConfiguration.getDirectoryCode() + "_"
        + directoryConfiguration.getDirectoryName() + "_"
        + directoryConfiguration.getEmrRuleType();

DocumentExportRecordRuleDetail matchedAnnotation = latestAnnotationMap.get(annotationKey);
if (matchedAnnotation != null) {
    // 4. 检查是否已存在批注记录，避免重复创建
    // 5. 创建并保存新的批注记录到DQM_EMR_DOCUMENT_EXPORT_RECORD_RULE_DETAIL表
}

// 优势：
// - 在每个章节生成时就处理批注信息，确保及时关联
// - 直接使用传入的DocumentDirectoryConfiguration参数，无需额外查询SQL执行记录
// - 使用基于导出记录ID的ConcurrentHashMap缓存，避免重复查询和并发问题
// - 解决了多个导出任务同时执行时的数据混乱问题
// - 通过定时任务每天凌晨2点清理2天前的缓存，避免内存泄漏
// - 检查重复记录，避免重复创建批注信息
// - 异常处理不影响主要的文档生成流程
// - 支持ZHX（整合性）、WZX（完整性）、YZX（一致性）等所有规则类型

// 缓存机制说明：
// - exportRecordAnnotationCache: Map<String, Map<String, DocumentExportRecordRuleDetail>>
// - exportRecordTimestampCache: Map<String, Long> 记录缓存创建时间
// - 外层Key: 导出记录ID，内层Key: directoryCode_directoryName_emrRuleType
// - 每个导出任务有独立的缓存空间，避免并发冲突
// - 定时任务每天凌晨2点清理2天前的缓存数据

// 定时清理任务：
// - AnnotationCacheCleanupTask.cleanupExpiredAnnotationCache() 每天凌晨2点执行
// - AnnotationCacheCleanupTask.logCacheStatistics() 每小时记录缓存统计
// - 提供手动清理接口：/emr/annotation-cache/cleanup
// - 提供缓存统计接口：/emr/annotation-cache/statistics
```