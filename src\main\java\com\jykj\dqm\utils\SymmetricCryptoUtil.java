package com.jykj.dqm.utils;

/**
 * 对称加密工具接口，定义通用的加密解密方法
 *
 * <AUTHOR>
 * @version 1.00
 */
public interface SymmetricCryptoUtil {

    /**
     * 对字符串进行加密
     *
     * @param str 待加密的字符串
     * @return 加密后的字符串
     */
    String encrypt(String str);

    /**
     * 对字符串进行解密
     *
     * @param str 待解密的字符串
     * @return 解密后的字符串
     */
    String decrypt(String str);

    /**
     * 获取加密算法名称
     *
     * @return 加密算法名称
     */
    String getAlgorithmName();

    /**
     * 判断字符串是否为加密内容
     *
     * @param str 待判断的字符串
     * @return 是否为加密内容
     */
    boolean isEncrypted(String str);
}