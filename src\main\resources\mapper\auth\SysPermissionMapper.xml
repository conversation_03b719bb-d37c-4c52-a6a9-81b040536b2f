<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jykj.dqm.auth.dao.SysPermissionMapper">

    <resultMap id="BaseResultMap" type="com.jykj.dqm.auth.entity.SysPermission">
        <result column="GROUP_CODE" property="groupCode" jdbcType="VARCHAR"/>
        <result column="PERMISSION_ID" property="permissionId" jdbcType="VARCHAR"/>
        <result column="PERMISSION_NAME" property="permissionName" jdbcType="VARCHAR"/>
        <result column="SYS_ID" property="sysId" jdbcType="VARCHAR"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="ORGANIZATION_CODE" property="organizationCode" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="add" parameterType="com.jykj.dqm.auth.entity.SysPermission" databaseId="mysql">
        insert into SYS_PERMISSION
        (GROUP_CODE,PERMISSION_NAME,SYS_ID,DESCRIPTION,ORGANIZATION_CODE,PARENT_ID)
        VALUES (
        #{sysPermission.groupCode,jdbcType=VARCHAR},
        #{sysPermission.permissionName,jdbcType=VARCHAR},
        #{sysPermission.sysId,jdbcType=VARCHAR},
        #{sysPermission.description,jdbcType=VARCHAR},
        #{sysPermission.organizationCode,jdbcType=VARCHAR},
        #{sysPermission.parentId,jdbcType=NUMERIC}
        )
    </insert>

    <insert id="add" parameterType="com.jykj.dqm.auth.entity.SysPermission" databaseId="oracle">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="permissionId">
            SELECT SEQ_SYS_PERMISSION.nextval as permissionId from dual
        </selectKey>
        insert into SYS_PERMISSION
        (GROUP_CODE,PERMISSION_ID,PERMISSION_NAME,SYS_ID,DESCRIPTION,ORGANIZATION_CODE,PARENT_ID)
        VALUES (
        #{sysPermission.groupCode,jdbcType=VARCHAR},
        #{permissionId},
        #{sysPermission.permissionName,jdbcType=VARCHAR},
        #{sysPermission.sysId,jdbcType=VARCHAR},
        #{sysPermission.description,jdbcType=VARCHAR},
        #{sysPermission.organizationCode,jdbcType=VARCHAR},
        #{sysPermission.parentId,jdbcType=NUMERIC}
        )
    </insert>


    <select id="queryList" resultMap="BaseResultMap">
        select *
        from SYS_PERMISSION
        where SYS_ID = 'DQM'
    </select>

    <select id="getSysPermissionList" resultMap="BaseResultMap">
        select *
        from SYS_PERMISSION where SYS_ID = 'DQM'
        <if test="permissionIds!= null and permissionIds.size()>0">
            AND PERMISSION_ID IN
            <foreach collection="permissionIds" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>