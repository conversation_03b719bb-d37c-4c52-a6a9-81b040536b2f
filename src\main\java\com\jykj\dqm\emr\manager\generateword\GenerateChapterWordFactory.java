package com.jykj.dqm.emr.manager.generateword;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.XWPFTemplate;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.DocumentExportRecordErrorDetailMapper;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentExportRecordErrorDetail;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.service.DocumentDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DocumentExportRecordErrorDetailService;
import com.jykj.dqm.emr.utils.PathNameUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 生成Word工厂(+策略模式)
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 17:23:30
 */
@Slf4j
@Component
public class GenerateChapterWordFactory {
    private static Map<String, GenerateChapterWord> generateChapterWordMap = new ConcurrentHashMap<>();

    @Autowired
    public void setTasksList(GenerateChapterWord[] generateChapterWords) {
        for (GenerateChapterWord generateChapterWord : generateChapterWords) {
            generateChapterWordMap.put(generateChapterWord.gainRuleRuleType().getValue(), generateChapterWord);
        }
    }

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private DocumentExportRecordErrorDetailService documentExportRecordErrorDetailService;

    @Autowired
    private DocumentExportRecordErrorDetailMapper documentExportRecordErrorDetailMapper;

    @Autowired
    private DocumentDirectoryConfigurationService documentDirectoryConfigurationService;

    /**
     * 异步执行
     *
     * @param documentRuleConfigurations
     * @param directoryConfiguration
     * @param dataStartTime
     * @param dataEndTime
     * @param path
     * @param recordId
     */
    @Async("doExportExecutor")
    public void generateChapterWord(List<DocumentRuleConfiguration> documentRuleConfigurations, DocumentDirectoryConfiguration directoryConfiguration,
                                    String dataStartTime, String dataEndTime, String path, String recordId, DocumentDirectoryConfiguration secondDirectoryConfiguration) {
        long startTime = System.currentTimeMillis();
        try {
            if (CollUtil.isEmpty(documentRuleConfigurations)) {
                throw new BusinessException("未配置规则！！！");
            }

            String ruleType = directoryConfiguration.getEmrRuleType();
            //特殊处理10.01.4整合性
            GenerateChapterWord generateChapterWord = generateChapterWordMap.get(ruleType);
            if ("10.01.4".equals(directoryConfiguration.getDirectoryCode()) && RuleTypeEnum.ZHX.getValue().equals(ruleType)) {
                generateChapterWord = generateChapterWordMap.get(RuleTypeEnum.ZHX10.getValue());
            }
            if (generateChapterWord == null) {
                throw new BusinessException("规则参数异常，没有对应解析类");
            }
            //if (redisTemplate.opsForList().size(recordId + "_RESULT") > 0) {
            //    throw new BusinessException("已经报错，不在继续执行");
            //}
            generateChapterWord.dealEachDoc(documentRuleConfigurations, directoryConfiguration, dataStartTime, dataEndTime, recordId, secondDirectoryConfiguration);
        } catch (Exception e) {
            //记录异常信息
            String localizedMessage = e.getLocalizedMessage();
            if (StrUtil.isBlank(localizedMessage)) {
                localizedMessage = StringUtil.getStackTrace(e);
            }
            recordRedisExceptionInfo(recordId, directoryConfiguration.getDirectoryCode(), directoryConfiguration.getDirectoryName(), directoryConfiguration.getEmrRuleType(), localizedMessage);
            String fullPath = PathNameUtils.getFullPathFileName(path, directoryConfiguration.getDirectoryCode(), directoryConfiguration.getDirectoryName(), directoryConfiguration.getEmrRuleType());

            fullPath = fullPath + "_pre";

            dealErrorDoc(e, fullPath + ".docx");
            if (!"未配置规则！！！".equals(e.getMessage()) && !"未配置数据源！！！".equals(e.getMessage())) {
                log.error(e.getMessage(), e);
            }
        } finally {
            long endTime = System.currentTimeMillis();
            //记录处理信息
            Long recordRedisNumInfo = recordRedisNumInfo(recordId);
            log.info("线程名称:{},规则:{}-{}-{},耗时{}毫秒，记录ID {}：计数{}", Thread.currentThread().getName(), directoryConfiguration.getDirectoryCode(), directoryConfiguration.getDirectoryName(), directoryConfiguration.getEmrRuleType(), endTime - startTime, recordId, recordRedisNumInfo);
        }
    }

    /**
     * 同步执行
     *
     * @param documentRuleConfigurations
     * @param directoryConfiguration
     * @param dataStartTime
     * @param dataEndTime
     * @param path
     * @param recordId
     */
    @Transactional(rollbackFor = Exception.class)
    public R generateChapterWordSync(List<DocumentRuleConfiguration> documentRuleConfigurations, DocumentDirectoryConfiguration directoryConfiguration, String dataStartTime, String dataEndTime, String path, String recordId) {
        try {
            if (CollUtil.isEmpty(documentRuleConfigurations)) {
                throw new BusinessException("未配置规则！！！");
            }
            String ruleType = directoryConfiguration.getEmrRuleType();
            //特殊处理10.01.4整合性
            GenerateChapterWord generateChapterWord = generateChapterWordMap.get(ruleType);
            if ("10.01.4".equals(directoryConfiguration.getDirectoryCode()) && RuleTypeEnum.ZHX.getValue().equals(ruleType)) {
                generateChapterWord = generateChapterWordMap.get(RuleTypeEnum.ZHX10.getValue());
            }
            if (generateChapterWord == null) {
                throw new BusinessException("规则参数异常，没有对应解析类");
            }
            //获取01.04对应的配置
            DocumentDirectoryConfiguration secondDirectoryConfiguration =
                    documentDirectoryConfigurationService.getOne(
                            new LambdaQueryWrapper<DocumentDirectoryConfiguration>().eq(DocumentDirectoryConfiguration::getDirectoryCode, directoryConfiguration.getDirectoryCode().substring(0, 5))
                    );

            generateChapterWord.dealEachDoc(documentRuleConfigurations, directoryConfiguration, dataStartTime, dataEndTime, recordId, secondDirectoryConfiguration);
            DocumentExportRecordErrorDetail documentExportRecordErrorDetail = documentExportRecordErrorDetailMapper.selectOne(
                    new LambdaQueryWrapper<DocumentExportRecordErrorDetail>()
                            .eq(DocumentExportRecordErrorDetail::getExportRecordId, recordId)
                            .eq(DocumentExportRecordErrorDetail::getDirectoryName, directoryConfiguration.getDirectoryName())
                            .eq(DocumentExportRecordErrorDetail::getDirectoryCode, directoryConfiguration.getDirectoryCode())
                            .eq(DocumentExportRecordErrorDetail::getEmrRuleType, directoryConfiguration.getEmrRuleType())

            );
            if (documentExportRecordErrorDetail == null) {
                documentExportRecordErrorDetail = new DocumentExportRecordErrorDetail()
                        .builder()
                        .exportRecordId(recordId)
                        .directoryCode(directoryConfiguration.getDirectoryCode())
                        .directoryName(directoryConfiguration.getDirectoryName())
                        .emrRuleType(directoryConfiguration.getEmrRuleType())
                        .errorReason("1")
                        .errorType("正常")
                        .build();
            }
            return RUtil.success(documentExportRecordErrorDetail);
        } catch (Exception e) {
            //记录异常信息
            String fullPath = PathNameUtils.getFullPathFileName(path, directoryConfiguration.getDirectoryCode(), directoryConfiguration.getDirectoryName(), directoryConfiguration.getEmrRuleType());
            if (!FileUtil.exist(new File(fullPath + ".docx"))) {
                fullPath = fullPath + "_pre";
            }
            //记录异常信息
            String localizedMessage = e.getLocalizedMessage();
            if (StrUtil.isBlank(localizedMessage)) {
                localizedMessage = StringUtil.getStackTrace(e);
            }
            //将错误记录入库
            DocumentExportRecordErrorDetail documentExportRecordErrorDetail =
                    DocumentExportRecordErrorDetail.builder()
                            .errorReason(localizedMessage)
                            .errorType("失败")
                            .build();
            LambdaUpdateWrapper<DocumentExportRecordErrorDetail> errorDetailLambdaUpdateWrapper = Wrappers.lambdaUpdate();
            errorDetailLambdaUpdateWrapper.eq(DocumentExportRecordErrorDetail::getExportRecordId, recordId)
                    .eq(DocumentExportRecordErrorDetail::getDirectoryName, directoryConfiguration.getDirectoryName())
                    .eq(DocumentExportRecordErrorDetail::getDirectoryCode, directoryConfiguration.getDirectoryCode())
                    .eq(DocumentExportRecordErrorDetail::getEmrRuleType, directoryConfiguration.getEmrRuleType());
            documentExportRecordErrorDetailService.update(documentExportRecordErrorDetail, errorDetailLambdaUpdateWrapper);
            dealErrorDoc(e, fullPath + ".docx");
            //&& !"规则参数异常，没有对应解析类".equals(e.getMessage())
            if (!"未配置规则！！！".equals(e.getMessage()) && !"未配置数据源！！！".equals(e.getMessage())) {
                log.error(e.getMessage(), e);
            }
            documentExportRecordErrorDetail = new DocumentExportRecordErrorDetail()
                    .builder()
                    .exportRecordId(recordId)
                    .directoryCode(directoryConfiguration.getDirectoryCode())
                    .directoryName(directoryConfiguration.getDirectoryName())
                    .emrRuleType(directoryConfiguration.getEmrRuleType())
                    .errorReason("重新生成失败！" + e.getMessage())
                    .errorType("失败")
                    .build();
            return RUtil.success(documentExportRecordErrorDetail);
        }
    }

    private void recordRedisExceptionInfo(String recordId, String directoryCode, String directoryName, String emrRuleType, String result) {
        Map map = new HashMap();
        map.put("msg", result);
        map.put("directoryName", directoryName);
        map.put("directoryCode", directoryCode);
        map.put("emrRuleType", emrRuleType);

        redisTemplate.opsForList().leftPush(recordId + "_RESULT", JSON.toJSONString(map));
        redisTemplate.expire(recordId + "_RESULT", 10, TimeUnit.HOURS);

        LambdaUpdateWrapper<DocumentExportRecordErrorDetail> errorDetailLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        errorDetailLambdaUpdateWrapper.eq(DocumentExportRecordErrorDetail::getExportRecordId, recordId)
                .eq(DocumentExportRecordErrorDetail::getDirectoryName, directoryName)
                .eq(DocumentExportRecordErrorDetail::getDirectoryCode, directoryCode)
                .eq(DocumentExportRecordErrorDetail::getEmrRuleType, emrRuleType);
        //将错误记录入库(更新或者插入)
        DocumentExportRecordErrorDetail documentExportRecordErrorDetail =
                DocumentExportRecordErrorDetail.builder().exportRecordId(recordId).errorReason(result).directoryCode(directoryCode).directoryName(directoryName).emrRuleType(emrRuleType).errorType("失败").build();
        documentExportRecordErrorDetailService.saveOrUpdate(documentExportRecordErrorDetail, errorDetailLambdaUpdateWrapper);
    }

    private Long recordRedisNumInfo(String recordId) {
        Long increment = redisTemplate.opsForValue().increment(recordId);
        redisTemplate.expire(recordId, 10, TimeUnit.HOURS);
        return increment;
    }


    /**
     * 报错将错误信息，写入Word中
     *
     * @param exception Exception
     * @param path      path
     */
    private void dealErrorDoc(Exception exception, String path) {
        XWPFTemplate template;
        InputStream templateInputStream = null;
        try {
            String fileName = "错误.docx";
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/" + fileName);
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            //替换模板中的内容
            template = XWPFTemplate.compile(templateInputStream).render(new HashMap<String, Object>() {
                {
                    put("failureResult", StringUtil.getStackTrace(exception));
                }
            });
            //输出到文件
            template.writeAndClose(new FileOutputStream(path));
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }
}
