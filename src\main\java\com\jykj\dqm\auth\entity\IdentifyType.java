/*
 * Copyright (c) 2021-2022 蓬安县妇幼 All Rights Reserved.
 */

package com.jykj.dqm.auth.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 验证方式（0：帐密；1：验证码 ；2：账密+验证码
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/4/1 16:38
 */
@ApiModel(description = "验证方式")
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum IdentifyType {
    ACCOUNT_PASSWORD("0", "帐密"),
    ACCOUNT_CODE("1", "短信验证码"),
    ACCOUNT_PASSWORD_PIN("2", "账密+短信验证码");
    private String code;
    private String name;
}
