package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jykj.dqm.emr.entity.DocumentDirectorySecondTree;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 实证材料目录标题Tree
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "实证材料目录标题Tree")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmpiricalMaterialDirectoryTree {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 等级关联
     */
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 项目序号
     */
    @ApiModelProperty(value = "项目序号")
    private String projectNum;

    /**
     * 业务项目
     */
    @ApiModelProperty(value = "业务项目")
    private String businessProject;

    /**
     * 评价类别
     */
    @ApiModelProperty(value = "评价类别")
    private String evaluationCategory;

    /**
     * 评价内容
     */
    @TableField(exist = false)
    private List<EmpiricalMaterialEvaluationContent> evaluationContents;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarksDesc;

    /**
     * 是否有下一级
     */
    @ApiModelProperty(value = "是否有下一级")
    private boolean hasChildren = true;

    /**
     * 父级编码
     */
    @ApiModelProperty(value = "父级编码")
    private String parentCode;
}